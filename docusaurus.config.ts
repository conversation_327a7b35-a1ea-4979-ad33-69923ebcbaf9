import {themes as prismThemes} from 'prism-react-renderer';
import type {Config} from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';
import simplePlantUML from '@akebifiky/remark-simple-plantuml'

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

const config: Config = {
  title: '91 Architecture Site',
  tagline: 'All information about 91 Architecture',
  favicon: 'img/91icon.svg',

  // Set the production url of your site here
  url: 'https://91.life',
  // Set the /<baseUrl>/ pathname under which your site is served
  // For GitHub pages deployment, it is often '/<projectName>/'
  baseUrl: '/',

  // GitHub pages deployment config.
  // If you aren't using GitHub pages, you don't need these.
  organizationName: '91life', // Usually your GitHub org/user name.
  projectName: 'architecture', // Usually your repo name.

  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',

  // Even if you don't use internationalization, you can use this field to set
  // useful metadata like html lang. For example, if your site is Chinese, you
  // may want to replace "en" with "zh-<PERSON>".
  i18n: {
    defaultLocale: 'en',
    locales: ['en'],
  },

  presets: [
    [
      'classic',
      {
        docs: {
          sidebarPath: './sidebars.ts',
          remarkPlugins: [simplePlantUML],
          // Please change this to your repo.
          // Remove this to remove the "edit this page" links.
          editUrl:
            'https://bitbucket.org/arhm/architecture',
        },
        theme: {
          customCss: './src/css/custom.css',
        },
      } satisfies Preset.Options,
    ],
  ],

  themeConfig: {
    // Replace with your project's social card
    image: 'img/docusaurus-social-card.jpg',
    // Enable dark/light mode switch
    colorMode: {
      defaultMode: 'light',
      disableSwitch: false,
      respectPrefersColorScheme: true,
    },
    navbar: {
      style: 'primary',
      logo: {
        alt: '91 Architecture',
        src: 'img/91icon.svg',
        srcDark: 'img/91icon.svg', // Add a dark mode logo if available
      },
      items: [
        {
          type: 'docSidebar',
          sidebarId: 'tutorialSidebar',
          position: 'left',
          label: 'Documentation',
        },
        {
          href: 'https://bitbucket.org/arhm/architecture',
          label: 'Bitbucket',
          position: 'right',
        },
      ],
    },
    footer: {
      style: 'dark',
      links: [
        {
          title: 'More',
          items: [
  
            {
              label: 'Bitbucket',
              href: 'https://bitbucket.org/arhm/architecture',
            },
          ],
        },
      ],
      copyright: `Copyright © ${new Date().getFullYear()} 91 Life, Inc. Built with Docusaurus.`,
    },
    // Modern looking code blocks
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.vsDark,
    },
  } satisfies Preset.ThemeConfig,
};

export default config;
