// @ts-check
// Note: type annotations allow type checking and IDEs autocompletion

/** @type {import('@docusaurus/types').Config} */
const config = {
  title: '91.life AI Platform',
  tagline: 'Enterprise AI Platform Documentation',
  favicon: 'img/logos/91life-logo.png',

  // Set the production url of your site here
  url: 'https://docs.91.life',
  // Set the /<baseUrl>/ pathname under which your site is served
  baseUrl: '/',

  // GitHub pages deployment config.
  organizationName: '91.life',
  projectName: 'ai-platform-docs',

  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',

  // Even if you don't use internalization, you can use this field to set useful
  // metadata like html lang. For example, if your site is Chinese, you may want
  // to replace "en" with "zh-Hans".
  i18n: {
    defaultLocale: 'en',
    locales: ['en'],
  },

  presets: [
    [
      'classic',
      /** @type {import('@docusaurus/preset-classic').Options} */
      ({
        docs: {
          sidebarPath: require.resolve('./sidebars.js'),
          editUrl: 'https://github.com/91-life/ai-platform-docs/tree/main/',
          showLastUpdateTime: true,
          showLastUpdateAuthor: true,
          routeBasePath: 'docs',
          path: 'docs',
        },
        blog: {
          showReadingTime: true,
          editUrl: 'https://github.com/91-life/ai-platform-docs/tree/main/',
          blogSidebarTitle: 'Recent Updates',
          blogSidebarCount: 5,
          authorsMapPath: 'blog/authors.yml',
          routeBasePath: 'blog',
          path: 'blog',
          postsPerPage: 10,
          blogListComponent: '@theme/BlogListPage',
          blogPostComponent: '@theme/BlogPostPage',
          blogTagsListComponent: '@theme/BlogTagsListPage',
          blogTagsPostsComponent: '@theme/BlogTagsPostsPage',
        },
        theme: {
          customCss: require.resolve('./src/css/custom.css'),
        },
        sitemap: {
          changefreq: 'weekly',
          priority: 0.5,
        },
        googleAnalytics: {
          trackingID: 'G-XXXXXXXXXX',
          anonymizeIP: true,
        },
      }),
    ],
  ],

  themes: [
    '@docusaurus/theme-mermaid',
  ],

  plugins: [
    [
      '@docusaurus/plugin-content-docs',
      {
        id: 'api',
        path: 'api',
        routeBasePath: 'api',
        sidebarPath: require.resolve('./sidebarsApi.js'),
        editUrl: 'https://github.com/91-life/ai-platform-docs/tree/main/',
      },
    ],
  ],

  themeConfig:
    /** @type {import('@docusaurus/preset-classic').ThemeConfig} */
    ({
      image: 'img/logos/91life-logo.png',
      navbar: {
        title: '91.life AI Platform',
        logo: {
          alt: '91.life Logo',
          src: 'img/logos/91life-logo.png',
          srcDark: 'img/logos/91life-logo.png',
        },
        items: [
          {
            type: 'docSidebar',
            sidebarId: 'AI-Architecture',
            position: 'left',
            label: 'AI-Architecture',
          },
          {
            to: '/api',
            label: 'API Reference',
            position: 'left',
          },
          {
            to: '/blog',
            label: 'Updates',
            position: 'left',
          },
          {
            type: 'search',
            position: 'right',
          },
          {
            href: 'https://github.com/91-life/ai-platform',
            label: 'GitHub',
            position: 'right',
          },
        ],
      },
      footer: {
        style: 'dark',
        logo: {
          alt: '91.life Logo',
          src: 'img/logos/91life-logo.png',
          href: 'https://91.life',
        },
        links: [
          {
            title: 'Documentation',
            items: [
              {
                label: 'Getting Started',
                to: '/docs/intro',
              },
              {
                label: 'Architecture Overview',
                to: '/docs/architecture-overview',
              },
              {
                label: 'API Reference',
                to: '/api',
              },
            ],
          },
          {
            title: 'Resources',
            items: [
              {
                label: 'Release Notes',
                to: '/blog',
              },
              {
                label: 'Security',
                to: '/docs/security/authentication',
              },
              {
                label: 'Support',
                to: '/docs/support',
              },
            ],
          },
          {
            title: 'Company',
            items: [
              {
                label: '91.life',
                href: 'https://91.life',
              },
              {
                label: 'Contact',
                href: 'https://91.life/contact',
              },
              {
                label: 'Careers',
                href: 'https://91.life/careers',
              },
            ],
          },
        ],
        copyright: `Copyright © ${new Date().getFullYear()} 91.life. Built with Docusaurus.`,
      },
      prism: {
        theme: require('prism-react-renderer').themes.github,
        darkTheme: require('prism-react-renderer').themes.dracula,
        additionalLanguages: ['bash', 'json', 'yaml', 'python', 'typescript'],
      },
      colorMode: {
        defaultMode: 'light',
        disableSwitch: false,
        respectPrefersColorScheme: true,
      },
      tableOfContents: {
        minHeadingLevel: 2,
        maxHeadingLevel: 5,
      },
      docs: {
        sidebar: {
          hideable: true,
          autoCollapseCategories: true,
        },
      },
      mermaid: {
        theme: {
          light: 'default',
          dark: 'dark'
        }
      },
      algolia: {
        appId: 'YOUR_APP_ID',
        apiKey: 'YOUR_SEARCH_API_KEY',
        indexName: 'YOUR_INDEX_NAME',
        contextualSearch: true,
      },
    }),
};

module.exports = config; 