<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-architecture/architecture_principles" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Architecture Principles | 91 Architecture Site</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://91.life/docs/architecture/architecture_principles"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Architecture Principles | 91 Architecture Site"><meta data-rh="true" name="description" content="This document outlines the core architectural principles that must guide all development and design of our SaaS platform. These principles are fundamental to maintaining a robust, maintainable, and scalable system."><meta data-rh="true" property="og:description" content="This document outlines the core architectural principles that must guide all development and design of our SaaS platform. These principles are fundamental to maintaining a robust, maintainable, and scalable system."><link data-rh="true" rel="icon" href="/img/91icon.svg"><link data-rh="true" rel="canonical" href="https://91.life/docs/architecture/architecture_principles"><link data-rh="true" rel="alternate" href="https://91.life/docs/architecture/architecture_principles" hreflang="en"><link data-rh="true" rel="alternate" href="https://91.life/docs/architecture/architecture_principles" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="91 Architecture Site RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="91 Architecture Site Atom Feed"><link rel="stylesheet" href="/assets/css/styles.ffe4d919.css">
<script src="/assets/js/runtime~main.fa55924a.js" defer="defer"></script>
<script src="/assets/js/main.5d97154b.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();null!==e?t(e):window.matchMedia("(prefers-color-scheme: dark)").matches?t("dark"):(window.matchMedia("(prefers-color-scheme: light)").matches,t("light"))}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/91icon.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top navbar--primary"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--light_rzkv"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--dark_mMAk"></div></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/intro">Documentation</a></div><div class="navbar__items navbar__items--right"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite" aria-pressed="false"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/intro">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" href="/docs/category/architecture">Architecture</a><button aria-label="Collapse sidebar category &#x27;Architecture&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/architecture/Architecture Presentation">Architecture Presentation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/architecture/Initial Architecture">Initial Architecture</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/docs/architecture/architecture_principles">Architecture Principles</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/decision-records">Decision Records</a><button aria-label="Expand sidebar category &#x27;Decision Records&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/research">Research</a><button aria-label="Expand sidebar category &#x27;Research&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/docs/ai-architecture/api/">ai-architecture</a></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/category/architecture"><span itemprop="name">Architecture</span></a><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Architecture Principles</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>Architecture Principles</h1></header>
<p>This document outlines the core architectural principles that must guide all development and design of our SaaS platform. These principles are fundamental to maintaining a robust, maintainable, and scalable system.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="1-development-process-and-tooling">1. Development Process and Tooling<a href="#1-development-process-and-tooling" class="hash-link" aria-label="Direct link to 1. Development Process and Tooling" title="Direct link to 1. Development Process and Tooling">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="11-jira-driven-development">1.1 JIRA-Driven Development<a href="#11-jira-driven-development" class="hash-link" aria-label="Direct link to 1.1 JIRA-Driven Development" title="Direct link to 1.1 JIRA-Driven Development">​</a></h3>
<p>Every development task must be tracked through JIRA tickets. This ensures:</p>
<ul>
<li>Clear traceability of changes</li>
<li>Proper documentation of requirements and decisions</li>
<li>Consistent project management</li>
<li>Ability to track progress and dependencies</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="12-you-professional-software-engineers--not-hackers">1.2 You Professional Software Engineers ... not Hackers<a href="#12-you-professional-software-engineers--not-hackers" class="hash-link" aria-label="Direct link to 1.2 You Professional Software Engineers ... not Hackers" title="Direct link to 1.2 You Professional Software Engineers ... not Hackers">​</a></h3>
<p>We are software engineers, not hackers:</p>
<ul>
<li>Write production-grade, maintainable code</li>
<li>Follow established software engineering practices</li>
<li>Document design decisions and code</li>
<li>Write comprehensive tests</li>
<li>Consider security, performance, and scalability</li>
<li>Use proper version control and branching strategies</li>
<li>Follow code review processes</li>
<li>Consider long-term maintenance and support</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="13-open-source-and-license-compliance">1.3 Open Source and License Compliance<a href="#13-open-source-and-license-compliance" class="hash-link" aria-label="Direct link to 1.3 Open Source and License Compliance" title="Direct link to 1.3 Open Source and License Compliance">​</a></h3>
<p>All frameworks and tools used within our software must be Open Source with liberal licenses (EPL, APL, MIT, BSD, AGPL, GPL). This ensures:</p>
<ul>
<li>Freedom to modify and customize</li>
<li>No vendor lock-in</li>
<li>Community support and transparency</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="14-framework-selection-and-customization">1.4 Framework Selection and Customization<a href="#14-framework-selection-and-customization" class="hash-link" aria-label="Direct link to 1.4 Framework Selection and Customization" title="Direct link to 1.4 Framework Selection and Customization">​</a></h3>
<p>We must maintain a balance between using existing solutions and custom development:</p>
<ul>
<li>Critical frameworks (Huly, Dapr, Watermill, Marquez) must be debuggable, maintainable, and customizable</li>
<li>We can reuse parts of software but not entire platforms</li>
<li>Every framework/tool requires an Architecture Decision Record (ADR) and approval from the architecture group</li>
</ul>
<img decoding="async" loading="lazy" src="https://www.plantuml.com/plantuml/png/TP6nRiCm34HtVGMdeXto2qMC62WIe8kJBV0WAwmZH29LcNAGlozT9q1Tj1llxa57IUj1R9OIWnakdfBDDeBZc9YGP2-tW93HYQK-e3776aSCdAA6qE1dmK5QMArUuRZRDXKSlBkWj6QXvhog6zXajEAP55Z8Ev7TsOkMbOnw-EZnEe5_k7S-fSoZ3HFq6ETa6rF0QST7sozZfcfsvIUqr3Dpq0jwwbjZY4MVmlTd0VxuPrc0zp88T0ZsUo4yGNTJTm6_PIv9pUSgT9RW2NPewSKWdM3gC6kTz3--0000" class="img_ev3q">
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="2-design-principles">2. Design Principles<a href="#2-design-principles" class="hash-link" aria-label="Direct link to 2. Design Principles" title="Direct link to 2. Design Principles">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="21-kiss-keep-it-simple-stupid">2.1 KISS (Keep It Simple, Stupid)<a href="#21-kiss-keep-it-simple-stupid" class="hash-link" aria-label="Direct link to 2.1 KISS (Keep It Simple, Stupid)" title="Direct link to 2.1 KISS (Keep It Simple, Stupid)">​</a></h3>
<p>Complexity should be avoided whenever possible:</p>
<ul>
<li>Prefer simple, straightforward solutions over complex ones</li>
<li>Focus on maintainability and readability</li>
<li>Document the reasoning behind any necessary complexity</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="22-separation-of-concerns">2.2 Separation of Concerns<a href="#22-separation-of-concerns" class="hash-link" aria-label="Direct link to 2.2 Separation of Concerns" title="Direct link to 2.2 Separation of Concerns">​</a></h3>
<p>Apply separation of concerns everywhere:</p>
<ul>
<li>Use clear boundaries between components</li>
<li>Keep domain logic isolated from technical implementation details for cleaner architecture</li>
<li>Isolate different responsibilities into distinct modules</li>
<li>Keep configuration separate from code</li>
<li>Separate deployment concerns from application logic</li>
<li>Maintain clear distinction between public and internal APIs</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="23-the-best-is-the-enemy-of-the-good">2.3 The Best is the Enemy of the Good<a href="#23-the-best-is-the-enemy-of-the-good" class="hash-link" aria-label="Direct link to 2.3 The Best is the Enemy of the Good" title="Direct link to 2.3 The Best is the Enemy of the Good">​</a></h3>
<p>Striving for perfection can prevent progress:</p>
<ul>
<li>Focus on delivering working solutions that meet requirements</li>
<li>Avoid over-engineering and excessive refinement</li>
<li>Prioritize practical solutions over theoretical perfection</li>
<li>Embrace iterative improvement over perfect first attempts</li>
<li>Remember that &quot;good enough&quot; is often better than &quot;perfect but late&quot;</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="24-early-optimization-is-the-root-of-all-evil">2.4 Early Optimization is the Root of All Evil<a href="#24-early-optimization-is-the-root-of-all-evil" class="hash-link" aria-label="Direct link to 2.4 Early Optimization is the Root of All Evil" title="Direct link to 2.4 Early Optimization is the Root of All Evil">​</a></h3>
<p>Premature optimization can lead to problems:</p>
<ul>
<li>Focus on correctness and clarity first</li>
<li>Optimize only after identifying actual bottlenecks</li>
<li>Measure before optimizing</li>
<li>Avoid complex optimizations that make code harder to maintain</li>
<li>Let performance requirements drive optimization decisions</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="25-minimize-external-dependencies">2.5 Minimize External Dependencies<a href="#25-minimize-external-dependencies" class="hash-link" aria-label="Direct link to 2.5 Minimize External Dependencies" title="Direct link to 2.5 Minimize External Dependencies">​</a></h3>
<p>Minimize dependency on external services and tools:</p>
<ul>
<li>Maintain control over critical functionality</li>
<li>External libraries often include more functionality than needed, introducing unnecessary bloat</li>
<li>Reduce the risk of being forced into disruptive upgrades</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="26-problem-first-approach">2.6 Problem-First Approach<a href="#26-problem-first-approach" class="hash-link" aria-label="Direct link to 2.6 Problem-First Approach" title="Direct link to 2.6 Problem-First Approach">​</a></h3>
<p>Before implementing any solution:</p>
<ul>
<li>Analyze the problem thoroughly</li>
<li>Identify gaps in current stack</li>
<li>Evaluate build vs. buy options</li>
<li>Consider hybrid approaches</li>
<li>Document the decision-making process (ADR)</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="27-pay-now-or-pay-more">2.7 Pay Now or Pay More<a href="#27-pay-now-or-pay-more" class="hash-link" aria-label="Direct link to 2.7 Pay Now or Pay More" title="Direct link to 2.7 Pay Now or Pay More">​</a></h3>
<p>Not investing early in good design will increase technical debt, reduce agility, and cost significantly more to undo later. This principle emphasizes the importance of proper upfront investment in architecture and design decisions.</p>
<ul>
<li>Conduct thorough design reviews before implementation</li>
<li>Document architectural decisions and their rationale</li>
<li>Consider long-term implications of design choices</li>
<li>Balance immediate needs with future scalability</li>
<li>Invest in automated testing and quality assurance</li>
<li>Regular architecture reviews to identify potential</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="28-automation-first">2.8 Automation First<a href="#28-automation-first" class="hash-link" aria-label="Direct link to 2.8 Automation First" title="Direct link to 2.8 Automation First">​</a></h3>
<p>What is not automated is a huge cost to the company. Strive for 100% automation of all tasks, especially those pertaining to platform runtime maintenance, deployment, upgrade, and onboarding.</p>
<ul>
<li>Automate all deployment and release processes</li>
<li>Implement self-healing and auto-recovery mechanisms</li>
<li>Automate platform monitoring and alerting</li>
<li>Create automated onboarding and provisioning workflows</li>
<li>Implement automated testing at all levels</li>
<li>Automate documentation generation and updates</li>
<li>Establish automated compliance and security checks</li>
<li>Create automated backup and disaster recovery procedures</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="29-clear-naming">2.9 Clear Naming<a href="#29-clear-naming" class="hash-link" aria-label="Direct link to 2.9 Clear Naming" title="Direct link to 2.9 Clear Naming">​</a></h3>
<p>Badly naming things only adds misery to the world. Clear, consistent, and meaningful names are essential for maintainable and understandable code.</p>
<ul>
<li>Use descriptive and self-explanatory names for all components</li>
<li>Follow consistent naming conventions across the platform</li>
<li>Avoid abbreviations unless universally understood</li>
<li>Use domain-specific terminology appropriately</li>
<li>Maintain naming consistency across related components</li>
<li>Document naming conventions and patterns</li>
<li>Review and refactor unclear names during code reviews</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="3-service-architecture">3. Service Architecture<a href="#3-service-architecture" class="hash-link" aria-label="Direct link to 3. Service Architecture" title="Direct link to 3. Service Architecture">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="31-treat-everything-as-data">3.1 Treat Everything as Data<a href="#31-treat-everything-as-data" class="hash-link" aria-label="Direct link to 3.1 Treat Everything as Data" title="Direct link to 3.1 Treat Everything as Data">​</a></h3>
<p>All platform elements must be treated as data:</p>
<ul>
<li>Code as data</li>
<li>Models as data</li>
<li>Documents as data</li>
<li>Configuration as data</li>
<li>Metadata for everything</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="32-service-design-principles">3.2 Service Design Principles<a href="#32-service-design-principles" class="hash-link" aria-label="Direct link to 3.2 Service Design Principles" title="Direct link to 3.2 Service Design Principles">​</a></h3>
<p>All services must follow these principles:</p>
<ol>
<li>
<p><strong>Mandatory Dapr Integration</strong></p>
<ul>
<li>All services must be &quot;Daprized&quot;</li>
<li>Leverage Dapr building blocks</li>
<li>Ensure consistent service-to-service communication</li>
</ul>
</li>
<li>
<p><strong>Systematic Interface-Driven Development</strong></p>
<ul>
<li>Design services around clear interfaces</li>
<li>Hide implementation details</li>
<li>Focus on API contracts</li>
<li>Make API&#x27;s acceptance a milestone before progressing on implementation</li>
</ul>
</li>
<li>
<p><strong>Systematic Command-Based Service Design</strong></p>
<ul>
<li>Design services around commands they execute</li>
<li>Commands will uniquely define the API that the service offers be it REST, gRPC, MCP, GraphQl etc...</li>
<li>Clear separation of concerns</li>
<li>Event-driven architecture justify when not appropriate</li>
</ul>
</li>
<li>
<p><strong>Keep Transverse Concerns ... Transverse</strong>
Focus on your business logic and implement those concerns through injection by the platform mechanisms through its SDKs:</p>
<ul>
<li>I/O operations</li>
<li>Configuration management</li>
<li>Transaction management</li>
<li>Security</li>
<li>Entitlement</li>
<li>DataLineage</li>
<li>Monitoring</li>
<li>Session Context</li>
</ul>
</li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="33-component-replaceability">3.3 Component Replaceability<a href="#33-component-replaceability" class="hash-link" aria-label="Direct link to 3.3 Component Replaceability" title="Direct link to 3.3 Component Replaceability">​</a></h3>
<p>Switching components, frameworks, or tools should be a trivial task:</p>
<ul>
<li>Design services with clear boundaries and interfaces</li>
<li>Use dependency injection and inversion of control</li>
<li>Implement adapter patterns for external dependencies</li>
<li>Keep framework-specific code isolated</li>
<li>Document integration points and dependencies</li>
<li>Use abstraction layers to decouple from specific implementations</li>
</ul>
<img decoding="async" loading="lazy" src="https://www.plantuml.com/plantuml/png/RLBBQiCm4BphAvRSG_-Wf3Y5659oi6-b1w5k715fPQHrY2dzzwuUAJtqOcPdv9rPhNQ9TUJ5EvMkbcOTjGSJ_1m8YGTU7K94mvecXqhDsbprXB0PCDwiGJZg5UC6lXJ0-uFhYJ6UjS4Fq0c6FcjTy5xJ9npaup0MfJjaPOoQqWrZGkW26OoKYZfswlkFuJ_7cct_T2e_zATgH6SxBL6p3LHj2lDhfKrMciUzvZPBjBpMk6WozqfisQ6NIoYqr9dUQzP7IoYH2hq_PlOjaEKGBKs5bBAsJKcSiZ_ZlROPJ1vgw66xVSxpTuU2PDQnAqY6QAZ5RfKaRIW7R327Qb22D9JT7qUDKXHODcYd2qCuPoE05-SWrLqby4jYi7wkiuhA5vGxDksECqWsiMXkLIWRp0tLJf2ycHy0" class="img_ev3q">
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="4-development-standards">4. Development Standards<a href="#4-development-standards" class="hash-link" aria-label="Direct link to 4. Development Standards" title="Direct link to 4. Development Standards">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="41-only-use-supported-languages">4.1 Only use Supported Languages<a href="#41-only-use-supported-languages" class="hash-link" aria-label="Direct link to 4.1 Only use Supported Languages" title="Direct link to 4.1 Only use Supported Languages">​</a></h3>
<p>Development is restricted to:</p>
<ul>
<li>Go</li>
<li>Python</li>
<li>TypeScript/JavaScript</li>
<li>Java</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="42-documentation-and-testing-for-everything">4.2 Documentation and Testing for Everything<a href="#42-documentation-and-testing-for-everything" class="hash-link" aria-label="Direct link to 4.2 Documentation and Testing for Everything" title="Direct link to 4.2 Documentation and Testing for Everything">​</a></h3>
<ul>
<li>All activities (from requirements down to deployment) must be documented</li>
<li>Comprehensive test coverage</li>
<li>Documentation must also be versioned in Git</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="43-everything-as-code">4.3 Everything as Code<a href="#43-everything-as-code" class="hash-link" aria-label="Direct link to 4.3 Everything as Code" title="Direct link to 4.3 Everything as Code">​</a></h3>
<p>Following Rex&#x27;s EaaC principle:</p>
<ul>
<li>Version control for all artifacts</li>
<li>Use Markdown for documentation</li>
<li>Use PlantUML for diagrams</li>
<li>Use Terraform for infrastructure</li>
<li>LLM Rules as Code</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="44-data-lineage-everywhere">4.4 Data Lineage Everywhere<a href="#44-data-lineage-everywhere" class="hash-link" aria-label="Direct link to 4.4 Data Lineage Everywhere" title="Direct link to 4.4 Data Lineage Everywhere">​</a></h3>
<ul>
<li>Track data flow through the system using the SDk - not doing it must be justified and approved by architecture</li>
<li>Document data transformations</li>
<li>Maintain audit trails</li>
<li>Enable data quality monitoring</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="45-mcp-everywhere">4.5 MCP Everywhere<a href="#45-mcp-everywhere" class="hash-link" aria-label="Direct link to 4.5 MCP Everywhere" title="Direct link to 4.5 MCP Everywhere">​</a></h3>
<p>All services must be compatible with the Model Context Protocol (MCP):</p>
<ul>
<li>Enable service activation and interaction through LLMs</li>
<li>Provide standardized context and metadata for LLM understanding</li>
<li>Maintain proper context preservation across LLM interactions</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="46-use-staged-event-driven-architecture">4.6 use Staged Event Driven Architecture<a href="#46-use-staged-event-driven-architecture" class="hash-link" aria-label="Direct link to 4.6 use Staged Event Driven Architecture" title="Direct link to 4.6 use Staged Event Driven Architecture">​</a></h3>
<p>The platform must implement Staged Event-Driven Architecture (SEDA):</p>
<ul>
<li>Break complex processes into stages</li>
<li>Enable better resource utilization</li>
<li>Improve system scalability</li>
<li>Support asynchronous processing</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="47-command-replay-and-state-reconstruction">4.7 Command Replay and State Reconstruction<a href="#47-command-replay-and-state-reconstruction" class="hash-link" aria-label="Direct link to 4.7 Command Replay and State Reconstruction" title="Direct link to 4.7 Command Replay and State Reconstruction">​</a></h3>
<p>The system must support complete state reconstruction through command replay:</p>
<ul>
<li>All commands entering the system must be persisted</li>
<li>Commands must be immutable and versioned</li>
<li>State must be deterministically reconstructible</li>
<li>Support point-in-time state reconstruction</li>
<li>Enable system recovery through command replay</li>
<li>Maintain command ordering and causality</li>
<li>Enable system testing through command replay</li>
<li>Allow for system migration and upgrades through replay</li>
<li>Support disaster recovery scenarios</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="49-infrastructure-agnostic-execution">4.9 Infrastructure Agnostic Execution<a href="#49-infrastructure-agnostic-execution" class="hash-link" aria-label="Direct link to 4.9 Infrastructure Agnostic Execution" title="Direct link to 4.9 Infrastructure Agnostic Execution">​</a></h3>
<p>The platform must be developed in an infrastructure-agnostic manner:</p>
<ul>
<li>Design all mechanisms to work across different deployment scenarios:<!-- -->
<ul>
<li>On-premise data centers</li>
<li>Public cloud providers (Google Cloud, AWS, Azure)</li>
<li>Local development environments (laptops)</li>
</ul>
</li>
<li>Ensure consistent behavior across all deployment environments</li>
<li>Design for portability from the start</li>
<li>Use infrastructure as code for consistent deployment</li>
<li>Maintain development parity between all environments</li>
<li>Test across all deployment scenarios</li>
<li>Use containerization for consistent runtime environments</li>
<li>Design for offline capabilities where required</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="410-guarantee-version-coexistence">4.10 Guarantee Version Coexistence<a href="#410-guarantee-version-coexistence" class="hash-link" aria-label="Direct link to 4.10 Guarantee Version Coexistence" title="Direct link to 4.10 Guarantee Version Coexistence">​</a></h3>
<p>The platform must support multiple versions of components running simultaneously:</p>
<ul>
<li>Allow different versions of backend and frontend components to coexist</li>
<li>Support version-aware routing and service discovery</li>
<li>Maintain backward compatibility between versions</li>
<li>Support gradual migration and canary deployments</li>
<li>Enable client-side version selection</li>
<li>Maintain version-specific documentation and SDKs</li>
<li>Support version deprecation policies</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="5-api-design">5. API Design<a href="#5-api-design" class="hash-link" aria-label="Direct link to 5. API Design" title="Direct link to 5. API Design">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="51-standard-api-façades">5.1 Standard API Façades<a href="#51-standard-api-façades" class="hash-link" aria-label="Direct link to 5.1 Standard API Façades" title="Direct link to 5.1 Standard API Façades">​</a></h3>
<ul>
<li>Start any design top down from API to Implementation</li>
<li>Use standard APIs as façades where possible</li>
<li>Hide implementation details</li>
<li>Provide consistent interfaces through commands (not doing it requires justification and approval)</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="52-sdk-design-principles">5.2 SDK Design Principles<a href="#52-sdk-design-principles" class="hash-link" aria-label="Direct link to 5.2 SDK Design Principles" title="Direct link to 5.2 SDK Design Principles">​</a></h3>
<p>When designing SDKs:</p>
<ul>
<li>Focus on user experience</li>
<li>Hide implementation complexity</li>
<li>Maintain backward compatibility</li>
<li>Follow language-specific best practices</li>
</ul>
<img decoding="async" loading="lazy" src="https://www.plantuml.com/plantuml/png/NL3BQWCn3BpxAqJk-OMIaFQmjAKXIqwX1zMhx9huXUs5Bg7_NaaDDEr1Q3o3HYDlQiFIvk1DlRYOiM00cq9EaMBhs-892jc6SVHaJ4PxmP5WrJy-mmikL5PmDG0dUM_ttJjWbIvK5xAdm2xON8ggTAzxKNP9f4_N5cLtRqn_tPaVAnNOvkoTrTcx7IBfqB5_EiYGT5Yl7tGhbsBPmwLOtKrCZGznuzGWdMy9RvcXJcds0tmGSzOJHi0uG7KIqX136VvleRO0Vu_Kd_kvZBN93GD5E9SKOERSlruRHlpbtm00" class="img_ev3q"></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://bitbucket.org/arhm/architecture/docs/architecture/architecture_principles.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/architecture/Initial Architecture"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Initial Architecture</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/category/decision-records"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">Decision Records</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#1-development-process-and-tooling" class="table-of-contents__link toc-highlight">1. Development Process and Tooling</a><ul><li><a href="#11-jira-driven-development" class="table-of-contents__link toc-highlight">1.1 JIRA-Driven Development</a></li><li><a href="#12-you-professional-software-engineers--not-hackers" class="table-of-contents__link toc-highlight">1.2 You Professional Software Engineers ... not Hackers</a></li><li><a href="#13-open-source-and-license-compliance" class="table-of-contents__link toc-highlight">1.3 Open Source and License Compliance</a></li><li><a href="#14-framework-selection-and-customization" class="table-of-contents__link toc-highlight">1.4 Framework Selection and Customization</a></li></ul></li><li><a href="#2-design-principles" class="table-of-contents__link toc-highlight">2. Design Principles</a><ul><li><a href="#21-kiss-keep-it-simple-stupid" class="table-of-contents__link toc-highlight">2.1 KISS (Keep It Simple, Stupid)</a></li><li><a href="#22-separation-of-concerns" class="table-of-contents__link toc-highlight">2.2 Separation of Concerns</a></li><li><a href="#23-the-best-is-the-enemy-of-the-good" class="table-of-contents__link toc-highlight">2.3 The Best is the Enemy of the Good</a></li><li><a href="#24-early-optimization-is-the-root-of-all-evil" class="table-of-contents__link toc-highlight">2.4 Early Optimization is the Root of All Evil</a></li><li><a href="#25-minimize-external-dependencies" class="table-of-contents__link toc-highlight">2.5 Minimize External Dependencies</a></li><li><a href="#26-problem-first-approach" class="table-of-contents__link toc-highlight">2.6 Problem-First Approach</a></li><li><a href="#27-pay-now-or-pay-more" class="table-of-contents__link toc-highlight">2.7 Pay Now or Pay More</a></li><li><a href="#28-automation-first" class="table-of-contents__link toc-highlight">2.8 Automation First</a></li><li><a href="#29-clear-naming" class="table-of-contents__link toc-highlight">2.9 Clear Naming</a></li></ul></li><li><a href="#3-service-architecture" class="table-of-contents__link toc-highlight">3. Service Architecture</a><ul><li><a href="#31-treat-everything-as-data" class="table-of-contents__link toc-highlight">3.1 Treat Everything as Data</a></li><li><a href="#32-service-design-principles" class="table-of-contents__link toc-highlight">3.2 Service Design Principles</a></li><li><a href="#33-component-replaceability" class="table-of-contents__link toc-highlight">3.3 Component Replaceability</a></li></ul></li><li><a href="#4-development-standards" class="table-of-contents__link toc-highlight">4. Development Standards</a><ul><li><a href="#41-only-use-supported-languages" class="table-of-contents__link toc-highlight">4.1 Only use Supported Languages</a></li><li><a href="#42-documentation-and-testing-for-everything" class="table-of-contents__link toc-highlight">4.2 Documentation and Testing for Everything</a></li><li><a href="#43-everything-as-code" class="table-of-contents__link toc-highlight">4.3 Everything as Code</a></li><li><a href="#44-data-lineage-everywhere" class="table-of-contents__link toc-highlight">4.4 Data Lineage Everywhere</a></li><li><a href="#45-mcp-everywhere" class="table-of-contents__link toc-highlight">4.5 MCP Everywhere</a></li><li><a href="#46-use-staged-event-driven-architecture" class="table-of-contents__link toc-highlight">4.6 use Staged Event Driven Architecture</a></li><li><a href="#47-command-replay-and-state-reconstruction" class="table-of-contents__link toc-highlight">4.7 Command Replay and State Reconstruction</a></li><li><a href="#49-infrastructure-agnostic-execution" class="table-of-contents__link toc-highlight">4.9 Infrastructure Agnostic Execution</a></li><li><a href="#410-guarantee-version-coexistence" class="table-of-contents__link toc-highlight">4.10 Guarantee Version Coexistence</a></li></ul></li><li><a href="#5-api-design" class="table-of-contents__link toc-highlight">5. API Design</a><ul><li><a href="#51-standard-api-façades" class="table-of-contents__link toc-highlight">5.1 Standard API Façades</a></li><li><a href="#52-sdk-design-principles" class="table-of-contents__link toc-highlight">5.2 SDK Design Principles</a></li></ul></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="footer__link-item">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 91 Life, Inc. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>