<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-research/fhir/fhir" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">FHIR Database Strategy | 91 Architecture Site</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://91.life/docs/research/fhir/"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="FHIR Database Strategy | 91 Architecture Site"><meta data-rh="true" name="description" content="1. Introduction"><meta data-rh="true" property="og:description" content="1. Introduction"><link data-rh="true" rel="icon" href="/img/91icon.svg"><link data-rh="true" rel="canonical" href="https://91.life/docs/research/fhir/"><link data-rh="true" rel="alternate" href="https://91.life/docs/research/fhir/" hreflang="en"><link data-rh="true" rel="alternate" href="https://91.life/docs/research/fhir/" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="91 Architecture Site RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="91 Architecture Site Atom Feed"><link rel="stylesheet" href="/assets/css/styles.ffe4d919.css">
<script src="/assets/js/runtime~main.fa55924a.js" defer="defer"></script>
<script src="/assets/js/main.5d97154b.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();null!==e?t(e):window.matchMedia("(prefers-color-scheme: dark)").matches?t("dark"):(window.matchMedia("(prefers-color-scheme: light)").matches,t("light"))}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/91icon.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top navbar--primary"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--light_rzkv"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--dark_mMAk"></div></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/intro">Documentation</a></div><div class="navbar__items navbar__items--right"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite" aria-pressed="false"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/intro">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/architecture">Architecture</a><button aria-label="Expand sidebar category &#x27;Architecture&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/decision-records">Decision Records</a><button aria-label="Expand sidebar category &#x27;Decision Records&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" href="/docs/category/research">Research</a><button aria-label="Collapse sidebar category &#x27;Research&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/category/data-flow">Data Flow</a><button aria-label="Expand sidebar category &#x27;Data Flow&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/category/fhir">FHIR</a><button aria-label="Collapse sidebar category &#x27;FHIR&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-3 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/docs/research/fhir/">FHIR Database Strategy</a></li></ul></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/docs/ai-architecture/api/">ai-architecture</a></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/category/research"><span itemprop="name">Research</span></a><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/category/fhir"><span itemprop="name">FHIR</span></a><meta itemprop="position" content="2"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">FHIR Database Strategy</span><meta itemprop="position" content="3"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>FHIR Database Strategy</h1></header>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="1-introduction">1. Introduction<a href="#1-introduction" class="hash-link" aria-label="Direct link to 1. Introduction" title="Direct link to 1. Introduction">​</a></h2>
<p>This document summarizes the research and decisions regarding the database
strategy for our scalable FHIR server backend. The core challenge is balancing
the need to efficiently store and query complex FHIR resources, support standard
FHIR search capabilities (including relationships and normalization), while
ensuring the system can scale horizontally to handle potentially massive
healthcare data volumes and high throughput.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="2-core-database-requirements-for-fhir">2. Core Database Requirements for FHIR<a href="#2-core-database-requirements-for-fhir" class="hash-link" aria-label="Direct link to 2. Core Database Requirements for FHIR" title="Direct link to 2. Core Database Requirements for FHIR">​</a></h2>
<p>Based on the FHIR specification and anticipated usage patterns, the database solution
must effectively support:</p>
<ul>
<li><strong>Resource Storage:</strong> Efficiently store FHIR resources, which are complex, semi-structured documents (often
represented as JSON).</li>
<li><strong>FHIR Search:</strong> Execute standard FHIR search parameters efficiently, including searches on nested fields within
resources.</li>
<li><strong>Search Normalization:</strong> Support common search expectations like case-insensitivity and diacritic-insensitivity
for string parameters, and potentially normalization for other types (dates, tokens, quantities).</li>
<li><strong>Relationship Handling:</strong> Efficiently retrieve related resources as specified by <code>_include</code> and <code>_revinclude</code>
parameters.</li>
<li><strong>High Availability (HA):</strong> Ensure data durability and service uptime through redundancy/failover mechanisms.</li>
<li><strong>Horizontal Scalability:</strong> Ability to scale out by adding more servers to handle growing data volumes and user
load (sharding).</li>
<li><strong>Write Performance:</strong> Maintain acceptable insert and update performance even with the necessary indexes for
efficient searching.</li>
<li><strong>Data Integrity:</strong> Ensure consistency appropriate for healthcare data (level of transactional guarantee needed).</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="3-database-options-explored">3. Database Options Explored<a href="#3-database-options-explored" class="hash-link" aria-label="Direct link to 3. Database Options Explored" title="Direct link to 3. Database Options Explored">​</a></h2>
<p>We considered several database categories:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="31-postgresql-traditional-sql--extensions">3.1. PostgreSQL (Traditional SQL + Extensions)<a href="#31-postgresql-traditional-sql--extensions" class="hash-link" aria-label="Direct link to 3.1. PostgreSQL (Traditional SQL + Extensions)" title="Direct link to 3.1. PostgreSQL (Traditional SQL + Extensions)">​</a></h3>
<ul>
<li><strong>Models:</strong> HAPI FHIR (normalized index tables) or Aidbox (JSONB + Functional Indexes).</li>
<li><strong>Pros:</strong>
<ul>
<li>Mature ACID transactions and relational integrity features.</li>
<li>Powerful SQL query language, including native <code>JOIN</code> operations ideal for efficiently handling
<code>_include</code>/<code>_revinclude</code> (often lower latency).</li>
<li>Strong JSONB support allows storing FHIR documents effectively.</li>
<li>Functional Indexes (Aidbox style) allow indexing expressions on JSONB data, enabling search within documents.</li>
</ul>
</li>
<li><strong>Cons:</strong>
<ul>
<li><strong>Horizontal scaling (sharding) is traditionally complex.</strong> Cross-shard JOINs, transactions, and referential
integrity are difficult to manage efficiently without specialized extensions (e.g., CitusDB) or architectures.
Often requires significant operational expertise or vertical scaling limits.</li>
</ul>
</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="32-mongodb-document-database">3.2. MongoDB (Document Database)<a href="#32-mongodb-document-database" class="hash-link" aria-label="Direct link to 3.2. MongoDB (Document Database)" title="Direct link to 3.2. MongoDB (Document Database)">​</a></h3>
<ul>
<li><strong>Models:</strong> Nested Map or Binary Proto + Extracted Search Params.</li>
<li><strong>Pros:</strong>
<ul>
<li><strong>Excellent horizontal scaling</strong> via native, mature sharding capabilities. Easier to scale out for large
datasets/throughput compared to traditional SQL sharding.</li>
<li>Document model aligns naturally with FHIR resource structure.</li>
<li>Flexible schema evolution.</li>
</ul>
</li>
<li><strong>Cons:</strong>
<ul>
<li><strong>No native JOINs across collections.</strong> Handling <code>_include</code>/<code>_revinclude</code> requires multiple application-level
queries (increasing latency) or complex <code>$lookup</code> aggregations.</li>
<li>Multi-document/multi-shard ACID transactions are more complex than traditional SQL.</li>
<li>Relational integrity is not enforced by the database; requires application logic.</li>
</ul>
</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="33-distributed-sql-spanner-cockroachdb">3.3. Distributed SQL (Spanner, CockroachDB)<a href="#33-distributed-sql-spanner-cockroachdb" class="hash-link" aria-label="Direct link to 3.3. Distributed SQL (Spanner, CockroachDB)" title="Direct link to 3.3. Distributed SQL (Spanner, CockroachDB)">​</a></h3>
<ul>
<li><strong>Models:</strong> Relational tables, potentially with JSON/JSONB columns.</li>
<li><strong>Pros:</strong>
<ul>
<li>Designed for <strong>both horizontal scalability AND strong consistency</strong> (often serializable ACID).</li>
<li>Support distributed SQL <strong>JOINs</strong>, potentially offering low latency for <code>_include</code>/<code>_revinclude</code> similar
to traditional SQL but in a scalable architecture.</li>
<li>Often provide SQL interfaces (PostgreSQL compatible for CockroachDB).</li>
<li>Support JSON types and functional index equivalents (Expression Indexes in CockroachDB, Generated Columns
in Spanner).</li>
</ul>
</li>
<li><strong>Cons:</strong>
<ul>
<li>Can be complex to operate, tune, and understand (distributed system nuances).</li>
<li>Potentially higher cost (especially managed services like Spanner).</li>
<li>Newer ecosystem compared to traditional SQL or MongoDB.</li>
</ul>
</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="4-suggested-approach-cockroachdb-distributed-sql">4. Suggested Approach: CockroachDB (Distributed SQL)<a href="#4-suggested-approach-cockroachdb-distributed-sql" class="hash-link" aria-label="Direct link to 4. Suggested Approach: CockroachDB (Distributed SQL)" title="Direct link to 4. Suggested Approach: CockroachDB (Distributed SQL)">​</a></h2>
<p>Based on the requirements, particularly the need for both <strong>efficient relationship handling (JOINs for
<code>_include</code>/<code>_revinclude</code>) and native horizontal scalability</strong>, we suggest to proceed with <strong>CockroachDB</strong>.</p>
<p><strong>Rationale:</strong> CockroachDB provides a unique combination that addresses the core challenges. It allows
us to leverage the power and familiarity of SQL for complex queries and JOINs while offering the seamless
horizontal scalability required for potentially massive FHIR datasets. Its support for JSONB and PostgreSQL-compatible
Expression Indexes directly addresses the need to store and efficiently query FHIR resource documents.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="41-proposed-data-model-table-per-resource-type">4.1. Proposed Data Model (Table per Resource Type)<a href="#41-proposed-data-model-table-per-resource-type" class="hash-link" aria-label="Direct link to 4.1. Proposed Data Model (Table per Resource Type)" title="Direct link to 4.1. Proposed Data Model (Table per Resource Type)">​</a></h3>
<p>We will use a relational schema in CockroachDB, likely with a table per FHIR resource type:</p>
<div class="language-sql codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-sql codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic">-- Example table for Patients (simplified)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">CREATE</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">TABLE</span><span class="token plain"> patients </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    id UUID </span><span class="token keyword" style="color:#00009f">PRIMARY</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">KEY</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">DEFAULT</span><span class="token plain"> gen_random_uuid</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><span class="token comment" style="color:#999988;font-style:italic">-- Internal primary key</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    logical_id STRING </span><span class="token operator" style="color:#393A34">NOT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">NULL</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">UNIQUE</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain">           </span><span class="token comment" style="color:#999988;font-style:italic">-- FHIR logical ID (e.g., &#x27;pat123&#x27;)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    version_id STRING </span><span class="token operator" style="color:#393A34">NOT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">NULL</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain">                  </span><span class="token comment" style="color:#999988;font-style:italic">-- FHIR version ID</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    last_updated TIMESTAMPTZ </span><span class="token operator" style="color:#393A34">NOT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">NULL</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain">           </span><span class="token comment" style="color:#999988;font-style:italic">-- FHIR meta.lastUpdated</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    is_deleted </span><span class="token keyword" style="color:#00009f">BOOL</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">DEFAULT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">false</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain">              </span><span class="token comment" style="color:#999988;font-style:italic">-- For soft deletes</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    resource_data JSONB </span><span class="token operator" style="color:#393A34">NOT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">NULL</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain">                </span><span class="token comment" style="color:#999988;font-style:italic">-- Stores the full FHIR resource as JSONB</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token comment" style="color:#999988;font-style:italic">-- Potentially add other top-level indexed metadata if frequently queried without JSONB access</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">INDEX</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">last_updated</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">INDEX</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">is_deleted</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">WHERE</span><span class="token plain"> is_deleted</span><span class="token operator" style="color:#393A34">=</span><span class="token boolean" style="color:#36acaa">true</span><span class="token plain"> </span><span class="token comment" style="color:#999988;font-style:italic">-- Index only deleted resources if needed often</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token comment" style="color:#999988;font-style:italic">-- CockroachDB automatically interleaves tables for locality if desired (e.g., observations interleaved with patients)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic">-- Example table for Observations (simplified)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">CREATE</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">TABLE</span><span class="token plain"> observations </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    id UUID </span><span class="token keyword" style="color:#00009f">PRIMARY</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">KEY</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">DEFAULT</span><span class="token plain"> gen_random_uuid</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    logical_id STRING </span><span class="token operator" style="color:#393A34">NOT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">NULL</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">UNIQUE</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    version_id STRING </span><span class="token operator" style="color:#393A34">NOT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">NULL</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    last_updated TIMESTAMPTZ </span><span class="token operator" style="color:#393A34">NOT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">NULL</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    is_deleted </span><span class="token keyword" style="color:#00009f">BOOL</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">DEFAULT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">false</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    resource_data JSONB </span><span class="token operator" style="color:#393A34">NOT</span><span class="token plain"> </span><span class="token boolean" style="color:#36acaa">NULL</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token comment" style="color:#999988;font-style:italic">-- Index top-level metadata</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token keyword" style="color:#00009f">INDEX</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">last_updated</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="42-indexing-strategy">4.2. Indexing Strategy<a href="#42-indexing-strategy" class="hash-link" aria-label="Direct link to 4.2. Indexing Strategy" title="Direct link to 4.2. Indexing Strategy">​</a></h3>
<p>We will leverage CockroachDB&#x27;s indexing capabilities:</p>
<ul>
<li>Primary/Standard Indexes: On primary keys (id) and frequently queried top-level metadata (logical_id, last_updated).</li>
<li>Expression Indexes (Functional Indexes): These are crucial for searching within the resource_data JSONB column.
We will create indexes on expressions that extract and potentially normalize FHIR search parameter values.<!-- -->
<div class="language-sql codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-sql codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic">-- Example: Index for case-insensitive search on first family name</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">CREATE</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">INDEX</span><span class="token plain"> idx_patient_family_normalized </span><span class="token keyword" style="color:#00009f">ON</span><span class="token plain"> patients </span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">lower</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">resource_data</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token string" style="color:#e3116c">&#x27;name&#x27;</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token number" style="color:#36acaa">0</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;&gt;</span><span class="token string" style="color:#e3116c">&#x27;family&#x27;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic">-- Example: Index for Observation code (first coding system/code)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">CREATE</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">INDEX</span><span class="token plain"> idx_observation_code </span><span class="token keyword" style="color:#00009f">ON</span><span class="token plain"> observations </span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">resource_data</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token string" style="color:#e3116c">&#x27;code&#x27;</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token string" style="color:#e3116c">&#x27;coding&#x27;</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token number" style="color:#36acaa">0</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;&gt;</span><span class="token string" style="color:#e3116c">&#x27;system&#x27;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">resource_data</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token string" style="color:#e3116c">&#x27;code&#x27;</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token string" style="color:#e3116c">&#x27;coding&#x27;</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token number" style="color:#36acaa">0</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;&gt;</span><span class="token string" style="color:#e3116c">&#x27;code&#x27;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic">-- Example: Index for Observation date</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">CREATE</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">INDEX</span><span class="token plain"> idx_observation_date </span><span class="token keyword" style="color:#00009f">ON</span><span class="token plain"> observations </span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">resource_data</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;&gt;</span><span class="token string" style="color:#e3116c">&#x27;effectiveDateTime&#x27;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain">::TIMESTAMPTZ</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">)</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"> </span><span class="token comment" style="color:#999988;font-style:italic">-- Cast JSON text to timestamp</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
</li>
<li>GIN Indexes (Optional): For more complex JSONB searching (e.g., checking for the existence of keys or matching
multiple values within arrays using operators like @&gt;), GIN indexes on the resource_data column could be used,
though targeted expression indexes are generally preferred for specific search parameters.</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="43-normalization-strategy">4.3. Normalization Strategy<a href="#43-normalization-strategy" class="hash-link" aria-label="Direct link to 4.3. Normalization Strategy" title="Direct link to 4.3. Normalization Strategy">​</a></h3>
<p>Normalization (case-insensitivity, diacritics, UTC dates) will primarily be handled within the Expression Index
definitions:</p>
<ul>
<li>Strings: Use functions like <code>lower()</code> within the index definition. Accent removal might require custom functions
or pre-processing if not built-in. Search terms must be similarly normalized at query time.</li>
<li>Dates: Cast JSON string dates to <code>TIMESTAMPTZ</code> within the index expression, which normalizes them to UTC for
reliable comparison.
Other Types: Extract relevant components (e.g., token system/code, reference parts) using JSONB operators
within the index expressions.</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="44-handling-relationships-_include_revinclude">4.4. Handling Relationships (<code>_include</code>/<code>_revinclude</code>)<a href="#44-handling-relationships-_include_revinclude" class="hash-link" aria-label="Direct link to 44-handling-relationships-_include_revinclude" title="Direct link to 44-handling-relationships-_include_revinclude">​</a></h3>
<p>Leverage standard SQL JOINs:</p>
<ol>
<li>The application translates the FHIR search (with <code>_include</code> or <code>_revinclude</code>) into a SQL query.</li>
<li>This query JOINs the resource table(s) with appropriate index expressions or other tables based on the
relationships defined by the FHIR parameters.</li>
</ol>
<div class="language-sql codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-sql codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic">-- Conceptual Example: GET /Patient?_id=pat123&amp;_revinclude=Observation:subject</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">SELECT</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    p</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">resource_data </span><span class="token keyword" style="color:#00009f">AS</span><span class="token plain"> patient_resource</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    o</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">resource_data </span><span class="token keyword" style="color:#00009f">AS</span><span class="token plain"> observation_resource</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">FROM</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    patients p</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">LEFT</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">JOIN</span><span class="token plain"> </span><span class="token comment" style="color:#999988;font-style:italic">-- Find observations referencing this patient</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    observations o </span><span class="token keyword" style="color:#00009f">ON</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">o</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">resource_data</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;</span><span class="token string" style="color:#e3116c">&#x27;subject&#x27;</span><span class="token operator" style="color:#393A34">-</span><span class="token operator" style="color:#393A34">&gt;&gt;</span><span class="token string" style="color:#e3116c">&#x27;reference&#x27;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"> </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">(</span><span class="token string" style="color:#e3116c">&#x27;Patient/&#x27;</span><span class="token plain"> </span><span class="token operator" style="color:#393A34">||</span><span class="token plain"> p</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">logical_id</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"> </span><span class="token comment" style="color:#999988;font-style:italic">-- Join based on reference path in JSONB</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">WHERE</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    p</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">logical_id </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;pat123&#x27;</span><span class="token punctuation" style="color:#393A34">;</span><span class="token plain"> </span><span class="token comment" style="color:#999988;font-style:italic">-- Find the specific patient</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic">-- Note: Performance depends on having an appropriate expression index on the reference path in the &#x27;observations&#x27; table.</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<ol start="3">
<li>CockroachDB&#x27;s distributed SQL engine optimizes and executes the JOIN across the necessary nodes in the cluster.</li>
<li>Results are returned to the application for Bundle construction.</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="45-performance--scalability">4.5. Performance &amp; Scalability<a href="#45-performance--scalability" class="hash-link" aria-label="Direct link to 4.5. Performance &amp; Scalability" title="Direct link to 4.5. Performance &amp; Scalability">​</a></h2>
<ul>
<li>Reads: Performance relies on well-designed Expression Indexes for specific searches and efficient distributed
JOIN execution for includes/revincludes.</li>
<li>Writes: Indexes add overhead. CockroachDB manages Raft replication for consistency, which impacts write latency.
Tuning batching and transaction sizes is important.</li>
<li>Scalability: Add more CockroachDB nodes. The database automatically rebalances data ranges (shards) across nodes.
Performance scales near-linearly for many workloads if schema/queries are designed well to avoid hotspots.</li>
</ul></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://bitbucket.org/arhm/architecture/docs/research/fhir/fhir.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/category/fhir"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">FHIR</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/ai-architecture/api/"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">API Reference</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#1-introduction" class="table-of-contents__link toc-highlight">1. Introduction</a></li><li><a href="#2-core-database-requirements-for-fhir" class="table-of-contents__link toc-highlight">2. Core Database Requirements for FHIR</a></li><li><a href="#3-database-options-explored" class="table-of-contents__link toc-highlight">3. Database Options Explored</a><ul><li><a href="#31-postgresql-traditional-sql--extensions" class="table-of-contents__link toc-highlight">3.1. PostgreSQL (Traditional SQL + Extensions)</a></li><li><a href="#32-mongodb-document-database" class="table-of-contents__link toc-highlight">3.2. MongoDB (Document Database)</a></li><li><a href="#33-distributed-sql-spanner-cockroachdb" class="table-of-contents__link toc-highlight">3.3. Distributed SQL (Spanner, CockroachDB)</a></li></ul></li><li><a href="#4-suggested-approach-cockroachdb-distributed-sql" class="table-of-contents__link toc-highlight">4. Suggested Approach: CockroachDB (Distributed SQL)</a><ul><li><a href="#41-proposed-data-model-table-per-resource-type" class="table-of-contents__link toc-highlight">4.1. Proposed Data Model (Table per Resource Type)</a></li><li><a href="#42-indexing-strategy" class="table-of-contents__link toc-highlight">4.2. Indexing Strategy</a></li><li><a href="#43-normalization-strategy" class="table-of-contents__link toc-highlight">4.3. Normalization Strategy</a></li><li><a href="#44-handling-relationships-_include_revinclude" class="table-of-contents__link toc-highlight">4.4. Handling Relationships (<code>_include</code>/<code>_revinclude</code>)</a></li></ul></li><li><a href="#45-performance--scalability" class="table-of-contents__link toc-highlight">4.5. Performance &amp; Scalability</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="footer__link-item">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 91 Life, Inc. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>