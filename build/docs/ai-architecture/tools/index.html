<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-ai-architecture/tools/tools" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">tools | 91 Architecture Site</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://91.life/docs/ai-architecture/tools/"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="tools | 91 Architecture Site"><meta data-rh="true" name="description" content="Tooling and Libraries"><meta data-rh="true" property="og:description" content="Tooling and Libraries"><link data-rh="true" rel="icon" href="/img/91icon.svg"><link data-rh="true" rel="canonical" href="https://91.life/docs/ai-architecture/tools/"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/" hreflang="en"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="91 Architecture Site RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="91 Architecture Site Atom Feed"><link rel="stylesheet" href="/assets/css/styles.ffe4d919.css">
<script src="/assets/js/runtime~main.fa55924a.js" defer="defer"></script>
<script src="/assets/js/main.5d97154b.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();null!==e?t(e):window.matchMedia("(prefers-color-scheme: dark)").matches?t("dark"):(window.matchMedia("(prefers-color-scheme: light)").matches,t("light"))}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/91icon.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top navbar--primary"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--light_rzkv"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--dark_mMAk"></div></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/intro">Documentation</a></div><div class="navbar__items navbar__items--right"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite" aria-pressed="false"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/intro">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/architecture">Architecture</a><button aria-label="Expand sidebar category &#x27;Architecture&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/decision-records">Decision Records</a><button aria-label="Expand sidebar category &#x27;Decision Records&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/research">Research</a><button aria-label="Expand sidebar category &#x27;Research&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/docs/ai-architecture/api/">ai-architecture</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/api/">API Reference</a><button aria-label="Expand sidebar category &#x27;API Reference&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/implementation/">Implementation Guide</a><button aria-label="Expand sidebar category &#x27;Implementation Guide&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/intro">Introduction</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/support/">Support</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-overview/">Architecture Overview</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-requirements/">R&amp;D Platform Architecture Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/best-practices/development/">best-practices</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/components/ai-models/">components</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/deployment/cloud-providers/">deployment</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/security/authentication/">security</a></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/system-requirements/">System Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item"><div class="menu__list-item-collapsible menu__list-item-collapsible--active"><a class="menu__link menu__link--sublist menu__link--active" aria-current="page" tabindex="0" href="/docs/ai-architecture/tools/">tools</a><button aria-label="Collapse sidebar category &#x27;tools&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-3 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/api/">old</a></div></li></ul></li></ul></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">ai-architecture</span><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">tools</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>tools</h1></header><h2 class="anchor anchorWithStickyNavbar_LWe7" id="tooling-and-libraries">Tooling and Libraries<a href="#tooling-and-libraries" class="hash-link" aria-label="Direct link to Tooling and Libraries" title="Direct link to Tooling and Libraries">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="core-infrastructure">Core Infrastructure<a href="#core-infrastructure" class="hash-link" aria-label="Direct link to Core Infrastructure" title="Direct link to Core Infrastructure">​</a></h3>
<ul>
<li><strong>Kubernetes</strong>: Container orchestration platform</li>
<li><strong>Helm</strong>: Package manager for Kubernetes</li>
<li><strong>Docker</strong>: Container runtime</li>
<li><strong>Istio</strong>: Service mesh for traffic management and security</li>
<li><strong>Cert-Manager</strong>: Certificate management for Kubernetes</li>
<li><strong>External-DNS</strong>: DNS management for Kubernetes resources</li>
<li><strong>NGINX Ingress Controller</strong>: Kubernetes ingress controller</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="data-processing-and-storage">Data Processing and Storage<a href="#data-processing-and-storage" class="hash-link" aria-label="Direct link to Data Processing and Storage" title="Direct link to Data Processing and Storage">​</a></h3>
<ul>
<li><strong>Trino</strong>: Distributed SQL query engine</li>
<li><strong>LakeFS</strong>: Data lake version control</li>
<li><strong>MinIO</strong>: S3-compatible object storage</li>
<li><strong>PostgreSQL</strong>: Relational database for metadata</li>
<li><strong>MongoDB</strong>: Document database for flexible data storage</li>
<li><strong>Redis</strong>: In-memory data store for caching</li>
<li><strong>Apache Kafka</strong>: Distributed event streaming platform</li>
<li><strong>Apache Spark</strong>: Unified analytics engine</li>
<li><strong>Apache Flink</strong>: Stream processing framework</li>
<li><strong>Apache Beam</strong>: Unified programming model for batch and streaming</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="machine-learning-and-ai">Machine Learning and AI<a href="#machine-learning-and-ai" class="hash-link" aria-label="Direct link to Machine Learning and AI" title="Direct link to Machine Learning and AI">​</a></h3>
<ul>
<li><strong>Kubeflow</strong>: ML toolkit for Kubernetes</li>
<li><strong>MLflow</strong>: Experiment tracking and model registry</li>
<li><strong>TensorFlow</strong>: Open-source ML framework</li>
<li><strong>PyTorch</strong>: Deep learning framework</li>
<li><strong>Scikit-learn</strong>: Machine learning library</li>
<li><strong>XGBoost</strong>: Gradient boosting framework</li>
<li><strong>LightGBM</strong>: Gradient boosting framework</li>
<li><strong>Hugging Face Transformers</strong>: State-of-the-art NLP</li>
<li><strong>ONNX</strong>: Open Neural Network Exchange</li>
<li><strong>TensorRT</strong>: High-performance deep learning inference</li>
<li><strong>KServe</strong>: Model serving platform</li>
<li><strong>Seldon Core</strong>: Model serving and monitoring</li>
<li><strong>BentoML</strong>: Model serving framework</li>
<li><strong>Ray</strong>: Distributed computing framework for ML</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="feature-engineering-and-management">Feature Engineering and Management<a href="#feature-engineering-and-management" class="hash-link" aria-label="Direct link to Feature Engineering and Management" title="Direct link to Feature Engineering and Management">​</a></h3>
<ul>
<li><strong>Feast</strong>: Feature store for ML</li>
<li><strong>Hopsworks</strong>: Feature store platform</li>
<li><strong>Tecton</strong>: Enterprise feature platform</li>
<li><strong>Featureform</strong>: Feature store framework</li>
<li><strong>DVC</strong>: Data version control</li>
<li><strong>Great Expectations</strong>: Data quality and validation</li>
<li><strong>Pandas</strong>: Data manipulation and analysis</li>
<li><strong>NumPy</strong>: Scientific computing</li>
<li><strong>Vaex</strong>: Out-of-core DataFrames</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="monitoring-and-observability">Monitoring and Observability<a href="#monitoring-and-observability" class="hash-link" aria-label="Direct link to Monitoring and Observability" title="Direct link to Monitoring and Observability">​</a></h3>
<ul>
<li><strong>Prometheus</strong>: Monitoring and alerting</li>
<li><strong>Grafana</strong>: Visualization and analytics</li>
<li><strong>Jaeger</strong>: Distributed tracing</li>
<li><strong>ELK Stack</strong>: Log management and analysis</li>
<li><strong>OpenTelemetry</strong>: Observability framework</li>
<li><strong>Evidently</strong>: ML monitoring</li>
<li><strong>Arize</strong>: ML observability</li>
<li><strong>Weights &amp; Biases</strong>: Experiment tracking</li>
<li><strong>Neptune</strong>: Experiment tracking</li>
<li><strong>Comet</strong>: Experiment tracking</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="workflow-and-orchestration">Workflow and Orchestration<a href="#workflow-and-orchestration" class="hash-link" aria-label="Direct link to Workflow and Orchestration" title="Direct link to Workflow and Orchestration">​</a></h3>
<ul>
<li><strong>Apache Airflow</strong>: Workflow orchestration</li>
<li><strong>Prefect</strong>: Workflow management</li>
<li><strong>Dagster</strong>: Data orchestration</li>
<li><strong>Kedro</strong>: Data pipeline framework</li>
<li><strong>Metaflow</strong>: ML pipeline framework</li>
<li><strong>Flyte</strong>: Workflow automation platform</li>
<li><strong>Argo</strong>: Kubernetes-native workflow engine</li>
<li><strong>Tekton</strong>: Cloud-native CI/CD</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="security-and-access-control">Security and Access Control<a href="#security-and-access-control" class="hash-link" aria-label="Direct link to Security and Access Control" title="Direct link to Security and Access Control">​</a></h3>
<ul>
<li><strong>Dex</strong>: OIDC provider</li>
<li><strong>Keycloak</strong>: Identity and access management</li>
<li><strong>Vault</strong>: Secrets management</li>
<li><strong>OPA</strong>: Policy enforcement</li>
<li><strong>Falco</strong>: Container security</li>
<li><strong>Trivy</strong>: Container vulnerability scanner</li>
<li><strong>SonarQube</strong>: Code quality and security</li>
<li><strong>Snyk</strong>: Security scanning</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="development-and-testing">Development and Testing<a href="#development-and-testing" class="hash-link" aria-label="Direct link to Development and Testing" title="Direct link to Development and Testing">​</a></h3>
<ul>
<li><strong>Jupyter</strong>: Interactive computing</li>
<li><strong>VS Code</strong>: IDE with ML extensions</li>
<li><strong>Git</strong>: Version control</li>
<li><strong>GitHub Actions</strong>: CI/CD</li>
<li><strong>Pytest</strong>: Testing framework</li>
<li><strong>Black</strong>: Code formatting</li>
<li><strong>Flake8</strong>: Linting</li>
<li><strong>Mypy</strong>: Static type checking</li>
<li><strong>Sphinx</strong>: Documentation generation</li>
<li><strong>Docusaurus</strong>: Documentation website</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="integration-and-apis">Integration and APIs<a href="#integration-and-apis" class="hash-link" aria-label="Direct link to Integration and APIs" title="Direct link to Integration and APIs">​</a></h3>
<ul>
<li><strong>FastAPI</strong>: Modern API framework</li>
<li><strong>gRPC</strong>: High-performance RPC framework</li>
<li><strong>GraphQL</strong>: Query language for APIs</li>
<li><strong>Swagger/OpenAPI</strong>: API documentation</li>
<li><strong>Postman</strong>: API development and testing</li>
<li><strong>Kong</strong>: API gateway</li>
<li><strong>Tyk</strong>: API management</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="data-quality-and-validation">Data Quality and Validation<a href="#data-quality-and-validation" class="hash-link" aria-label="Direct link to Data Quality and Validation" title="Direct link to Data Quality and Validation">​</a></h3>
<ul>
<li><strong>Great Expectations</strong>: Data validation</li>
<li><strong>Deequ</strong>: Data quality</li>
<li><strong>Soda SQL</strong>: Data testing</li>
<li><strong>DataHub</strong>: Metadata platform</li>
<li><strong>Amundsen</strong>: Data discovery</li>
<li><strong>Marquez</strong>: Data lineage</li>
<li><strong>OpenLineage</strong>: Data lineage standard</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="model-management-and-deployment">Model Management and Deployment<a href="#model-management-and-deployment" class="hash-link" aria-label="Direct link to Model Management and Deployment" title="Direct link to Model Management and Deployment">​</a></h3>
<ul>
<li><strong>MLflow</strong>: Model lifecycle management</li>
<li><strong>BentoML</strong>: Model serving</li>
<li><strong>Cortex</strong>: Model serving</li>
<li><strong>Triton</strong>: Inference server</li>
<li><strong>TorchServe</strong>: PyTorch model serving</li>
<li><strong>TensorFlow Serving</strong>: TF model serving</li>
<li><strong>ONNX Runtime</strong>: Model inference</li>
<li><strong>ModelMesh</strong>: Model serving platform</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="development-tools-and-sdks">Development Tools and SDKs<a href="#development-tools-and-sdks" class="hash-link" aria-label="Direct link to Development Tools and SDKs" title="Direct link to Development Tools and SDKs">​</a></h3>
<ul>
<li><strong>Python SDK</strong>: Core development</li>
<li><strong>Java SDK</strong>: Enterprise integration</li>
<li><strong>Go SDK</strong>: High-performance services</li>
<li><strong>Node.js SDK</strong>: Web services</li>
<li><strong>Rust SDK</strong>: Systems programming</li>
<li><strong>CLI Tools</strong>: Command-line interface</li>
<li><strong>REST API</strong>: HTTP interface</li>
<li><strong>gRPC API</strong>: High-performance interface</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="documentation-and-knowledge-base">Documentation and Knowledge Base<a href="#documentation-and-knowledge-base" class="hash-link" aria-label="Direct link to Documentation and Knowledge Base" title="Direct link to Documentation and Knowledge Base">​</a></h3>
<ul>
<li><strong>Docusaurus</strong>: Documentation website</li>
<li><strong>MkDocs</strong>: Documentation generator</li>
<li><strong>Sphinx</strong>: Documentation generator</li>
<li><strong>Swagger</strong>: API documentation</li>
<li><strong>Postman</strong>: API documentation</li>
<li><strong>Confluence</strong>: Knowledge base</li>
<li><strong>Notion</strong>: Documentation</li>
<li><strong>GitBook</strong>: Documentation</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="community-and-support">Community and Support<a href="#community-and-support" class="hash-link" aria-label="Direct link to Community and Support" title="Direct link to Community and Support">​</a></h3>
<ul>
<li><strong>Slack</strong>: Community communication</li>
<li><strong>Discord</strong>: Community platform</li>
<li><strong>GitHub</strong>: Code hosting</li>
<li><strong>Stack Overflow</strong>: Q&amp;A platform</li>
<li><strong>Medium</strong>: Blog platform</li>
<li><strong>YouTube</strong>: Video tutorials</li>
<li><strong>Meetup</strong>: Community events</li>
<li><strong>LinkedIn</strong>: Professional network</li>
</ul></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/tools.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/ai-architecture/system-requirements/"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">System Requirements</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/ai-architecture/tools/old/api/"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">API Overview</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#tooling-and-libraries" class="table-of-contents__link toc-highlight">Tooling and Libraries</a><ul><li><a href="#core-infrastructure" class="table-of-contents__link toc-highlight">Core Infrastructure</a></li><li><a href="#data-processing-and-storage" class="table-of-contents__link toc-highlight">Data Processing and Storage</a></li><li><a href="#machine-learning-and-ai" class="table-of-contents__link toc-highlight">Machine Learning and AI</a></li><li><a href="#feature-engineering-and-management" class="table-of-contents__link toc-highlight">Feature Engineering and Management</a></li><li><a href="#monitoring-and-observability" class="table-of-contents__link toc-highlight">Monitoring and Observability</a></li><li><a href="#workflow-and-orchestration" class="table-of-contents__link toc-highlight">Workflow and Orchestration</a></li><li><a href="#security-and-access-control" class="table-of-contents__link toc-highlight">Security and Access Control</a></li><li><a href="#development-and-testing" class="table-of-contents__link toc-highlight">Development and Testing</a></li><li><a href="#integration-and-apis" class="table-of-contents__link toc-highlight">Integration and APIs</a></li><li><a href="#data-quality-and-validation" class="table-of-contents__link toc-highlight">Data Quality and Validation</a></li><li><a href="#model-management-and-deployment" class="table-of-contents__link toc-highlight">Model Management and Deployment</a></li><li><a href="#development-tools-and-sdks" class="table-of-contents__link toc-highlight">Development Tools and SDKs</a></li><li><a href="#documentation-and-knowledge-base" class="table-of-contents__link toc-highlight">Documentation and Knowledge Base</a></li><li><a href="#community-and-support" class="table-of-contents__link toc-highlight">Community and Support</a></li></ul></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="footer__link-item">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 91 Life, Inc. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>