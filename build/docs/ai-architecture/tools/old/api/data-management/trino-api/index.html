<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-ai-architecture/tools/old/api/data-management/trino-api" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Trino API Documentation | 91 Architecture Site</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://91.life/docs/ai-architecture/tools/old/api/data-management/trino-api"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Trino API Documentation | 91 Architecture Site"><meta data-rh="true" name="description" content="Trino provides a RESTful API for executing SQL queries and managing query execution. This documentation covers all available endpoints, authentication, and usage examples."><meta data-rh="true" property="og:description" content="Trino provides a RESTful API for executing SQL queries and managing query execution. This documentation covers all available endpoints, authentication, and usage examples."><link data-rh="true" rel="icon" href="/img/91icon.svg"><link data-rh="true" rel="canonical" href="https://91.life/docs/ai-architecture/tools/old/api/data-management/trino-api"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/old/api/data-management/trino-api" hreflang="en"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/old/api/data-management/trino-api" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="91 Architecture Site RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="91 Architecture Site Atom Feed"><link rel="stylesheet" href="/assets/css/styles.ffe4d919.css">
<script src="/assets/js/runtime~main.fa55924a.js" defer="defer"></script>
<script src="/assets/js/main.5d97154b.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();null!==e?t(e):window.matchMedia("(prefers-color-scheme: dark)").matches?t("dark"):(window.matchMedia("(prefers-color-scheme: light)").matches,t("light"))}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/91icon.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top navbar--primary"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--light_rzkv"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--dark_mMAk"></div></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/intro">Documentation</a></div><div class="navbar__items navbar__items--right"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite" aria-pressed="false"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/intro">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/architecture">Architecture</a><button aria-label="Expand sidebar category &#x27;Architecture&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/decision-records">Decision Records</a><button aria-label="Expand sidebar category &#x27;Decision Records&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/research">Research</a><button aria-label="Expand sidebar category &#x27;Research&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/docs/ai-architecture/api/">ai-architecture</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/api/">API Reference</a><button aria-label="Expand sidebar category &#x27;API Reference&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/implementation/">Implementation Guide</a><button aria-label="Expand sidebar category &#x27;Implementation Guide&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/intro">Introduction</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/support/">Support</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-overview/">Architecture Overview</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-requirements/">R&amp;D Platform Architecture Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/best-practices/development/">best-practices</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/components/ai-models/">components</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/deployment/cloud-providers/">deployment</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/security/authentication/">security</a></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/system-requirements/">System Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/tools/">tools</a><button aria-label="Collapse sidebar category &#x27;tools&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-3 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" tabindex="0" href="/docs/ai-architecture/tools/old/api/">old</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/tools/old/api/">API Overview</a><button aria-label="Collapse sidebar category &#x27;API Overview&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/api/api-endpoints">MLOps Platform API Endpoints</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/tools/old/api/data-management/">Data Management</a><button aria-label="Collapse sidebar category &#x27;Data Management&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-6 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/api/data-management/data-quality-api">Data Quality API</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-6 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/api/data-management/feature-store-api">Feature Store API</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-6 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/api/data-management/lakefs-api">LakeFS API</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-6 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/api/data-management/minio-api">MinIO API</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-6 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/api/data-management/openmetadata-api">OpenMetadata API</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-6 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/docs/ai-architecture/tools/old/api/data-management/trino-api">Trino API</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/model-management/">Model Management</a><button aria-label="Expand sidebar category &#x27;Model Management&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/monitoring/">Monitoring</a><button aria-label="Expand sidebar category &#x27;Monitoring&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/orchestration/">Orchestration</a><button aria-label="Expand sidebar category &#x27;Orchestration&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/visualization/">Visualization</a><button aria-label="Expand sidebar category &#x27;Visualization&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-detailed-scenarios">architecture</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/mlops/mlops-components">mlops</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/research/research-tools-workflows">research</a></div></li></ul></li></ul></li></ul></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">ai-architecture</span><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/tools/"><span itemprop="name">tools</span></a><meta itemprop="position" content="2"></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">old</span><meta itemprop="position" content="3"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/tools/old/api/"><span itemprop="name">API Overview</span></a><meta itemprop="position" content="4"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/tools/old/api/data-management/"><span itemprop="name">Data Management</span></a><meta itemprop="position" content="5"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Trino API</span><meta itemprop="position" content="6"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>Trino API Documentation</h1></header>
<p>Trino provides a RESTful API for executing SQL queries and managing query execution. This documentation covers all available endpoints, authentication, and usage examples.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="base-url">Base URL<a href="#base-url" class="hash-link" aria-label="Direct link to Base URL" title="Direct link to Base URL">​</a></h2>
<div class="codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-text codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">https://trino.example.com/v1</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="authentication">Authentication<a href="#authentication" class="hash-link" aria-label="Direct link to Authentication" title="Direct link to Authentication">​</a></h2>
<p>All API requests require authentication using OAuth2/OIDC. Include the following header:</p>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">Authorization: Bearer &lt;your-token&gt;</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="api-endpoints">API Endpoints<a href="#api-endpoints" class="hash-link" aria-label="Direct link to API Endpoints" title="Direct link to API Endpoints">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="query-execution">Query Execution<a href="#query-execution" class="hash-link" aria-label="Direct link to Query Execution" title="Direct link to Query Execution">​</a></h3>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="submit-query">Submit Query<a href="#submit-query" class="hash-link" aria-label="Direct link to Submit Query" title="Direct link to Submit Query">​</a></h4>
<div class="language-http codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-http codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">POST /statement</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<p><strong>Headers:</strong></p>
<ul>
<li><code>X-Trino-User</code>: Username</li>
<li><code>X-Trino-Schema</code>: Default schema</li>
<li><code>X-Trino-Catalog</code>: Default catalog</li>
<li><code>X-Trino-Time-Zone</code>: Time zone (e.g., &quot;UTC&quot;)</li>
</ul>
<p><strong>Request Body:</strong></p>
<div class="language-sql codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-sql codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">SELECT</span><span class="token plain"> </span><span class="token operator" style="color:#393A34">*</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">FROM</span><span class="token plain"> my_table </span><span class="token keyword" style="color:#00009f">WHERE</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">date</span><span class="token plain"> </span><span class="token operator" style="color:#393A34">&gt;=</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">DATE</span><span class="token plain"> </span><span class="token string" style="color:#e3116c">&#x27;2024-03-20&#x27;</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<p><strong>Response:</strong></p>
<div class="language-json codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-json codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">{</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;id&quot;: &quot;20240320_100000_00000_abcde&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;infoUri&quot;: &quot;https://trino.example.com/ui/query.html?20240320_100000_00000_abcde&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;nextUri&quot;: &quot;https://trino.example.com/v1/statement/20240320_100000_00000_abcde/1&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;stats&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;state&quot;: &quot;RUNNING&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queued&quot;: false,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;scheduled&quot;: true,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;nodes&quot;: 1,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;totalSplits&quot;: 10,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queuedSplits&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;runningSplits&quot;: 5,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;completedSplits&quot;: 5,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;cpuTimeMillis&quot;: 1000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;wallTimeMillis&quot;: 2000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queuedTimeMillis&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;elapsedTimeMillis&quot;: 2000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;processedRows&quot;: 1000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;processedBytes&quot;: 10000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;peakMemoryBytes&quot;: 1000000</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">}</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="get-query-results">Get Query Results<a href="#get-query-results" class="hash-link" aria-label="Direct link to Get Query Results" title="Direct link to Get Query Results">​</a></h4>
<div class="language-http codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-http codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">GET /statement/{query-id}/{token}</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<p><strong>Response:</strong></p>
<div class="language-json codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-json codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">{</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;id&quot;: &quot;20240320_100000_00000_abcde&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;infoUri&quot;: &quot;https://trino.example.com/ui/query.html?20240320_100000_00000_abcde&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;nextUri&quot;: &quot;https://trino.example.com/v1/statement/20240320_100000_00000_abcde/2&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;data&quot;: [</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        [&quot;2024-03-20&quot;, 100, &quot;value1&quot;],</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        [&quot;2024-03-20&quot;, 200, &quot;value2&quot;]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    ],</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;columns&quot;: [</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;name&quot;: &quot;date&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;type&quot;: &quot;date&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;typeSignature&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;rawType&quot;: &quot;date&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;arguments&quot;: []</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        },</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;name&quot;: &quot;count&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;type&quot;: &quot;bigint&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;typeSignature&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;rawType&quot;: &quot;bigint&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;arguments&quot;: []</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        },</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;name&quot;: &quot;value&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;type&quot;: &quot;varchar&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;typeSignature&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;rawType&quot;: &quot;varchar&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;arguments&quot;: []</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    ],</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;stats&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;state&quot;: &quot;RUNNING&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queued&quot;: false,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;scheduled&quot;: true,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;nodes&quot;: 1,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;totalSplits&quot;: 10,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queuedSplits&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;runningSplits&quot;: 5,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;completedSplits&quot;: 5,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;cpuTimeMillis&quot;: 1000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;wallTimeMillis&quot;: 2000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queuedTimeMillis&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;elapsedTimeMillis&quot;: 2000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;processedRows&quot;: 1000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;processedBytes&quot;: 10000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;peakMemoryBytes&quot;: 1000000</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">}</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="cancel-query">Cancel Query<a href="#cancel-query" class="hash-link" aria-label="Direct link to Cancel Query" title="Direct link to Cancel Query">​</a></h4>
<div class="language-http codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-http codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">DELETE /statement/{query-id}</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<p><strong>Response:</strong></p>
<div class="language-json codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-json codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">{</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;id&quot;: &quot;20240320_100000_00000_abcde&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;infoUri&quot;: &quot;https://trino.example.com/ui/query.html?20240320_100000_00000_abcde&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;stats&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;state&quot;: &quot;CANCELED&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queued&quot;: false,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;scheduled&quot;: false,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;nodes&quot;: 1,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;totalSplits&quot;: 10,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queuedSplits&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;runningSplits&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;completedSplits&quot;: 5,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;cpuTimeMillis&quot;: 1000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;wallTimeMillis&quot;: 2000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;queuedTimeMillis&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;elapsedTimeMillis&quot;: 2000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;processedRows&quot;: 1000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;processedBytes&quot;: 10000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        &quot;peakMemoryBytes&quot;: 1000000</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">}</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="query-management">Query Management<a href="#query-management" class="hash-link" aria-label="Direct link to Query Management" title="Direct link to Query Management">​</a></h3>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="list-queries">List Queries<a href="#list-queries" class="hash-link" aria-label="Direct link to List Queries" title="Direct link to List Queries">​</a></h4>
<div class="language-http codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-http codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">GET /query</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<p><strong>Query Parameters:</strong></p>
<ul>
<li><code>state</code> (optional): Filter by query state</li>
<li><code>user</code> (optional): Filter by user</li>
<li><code>limit</code> (optional): Maximum number of queries to return</li>
</ul>
<p><strong>Response:</strong></p>
<div class="language-json codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-json codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">{</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;queries&quot;: [</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;queryId&quot;: &quot;20240320_100000_00000_abcde&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;session&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;user&quot;: &quot;<EMAIL>&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;schema&quot;: &quot;default&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;catalog&quot;: &quot;hive&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;timeZone&quot;: &quot;UTC&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            },</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;state&quot;: &quot;RUNNING&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;memoryPool&quot;: &quot;general&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;scheduled&quot;: true,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;self&quot;: &quot;https://trino.example.com/v1/query/20240320_100000_00000_abcde&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;query&quot;: &quot;SELECT * FROM my_table WHERE date &gt;= DATE &#x27;2024-03-20&#x27;&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            &quot;queryStats&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;createTime&quot;: &quot;2024-03-20T10:00:00Z&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;endTime&quot;: null,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;elapsedTimeMillis&quot;: 2000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;queuedTimeMillis&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;totalDrivers&quot;: 10,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;queuedDrivers&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;runningDrivers&quot;: 5,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;completedDrivers&quot;: 5,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;blockedDrivers&quot;: 0,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;cumulativeUserMemory&quot;: 1000000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;userMemoryReservation&quot;: 1000000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;peakUserMemoryReservation&quot;: 1000000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;totalCpuTime&quot;: 1000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;totalScheduledTime&quot;: 2000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;peakTotalMemory&quot;: 1000000,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">                &quot;totalAllocation&quot;: 1000000</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">            }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        }</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    ]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">}</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="usage-examples">Usage Examples<a href="#usage-examples" class="hash-link" aria-label="Direct link to Usage Examples" title="Direct link to Usage Examples">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="python-example">Python Example<a href="#python-example" class="hash-link" aria-label="Direct link to Python Example" title="Direct link to Python Example">​</a></h3>
<div class="language-python codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-python codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> trino</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">import</span><span class="token plain"> pandas </span><span class="token keyword" style="color:#00009f">as</span><span class="token plain"> pd</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Initialize client</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">conn </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> trino</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">dbapi</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">connect</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    host</span><span class="token operator" style="color:#393A34">=</span><span class="token string" style="color:#e3116c">&quot;trino.example.com&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    port</span><span class="token operator" style="color:#393A34">=</span><span class="token number" style="color:#36acaa">443</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    user</span><span class="token operator" style="color:#393A34">=</span><span class="token string" style="color:#e3116c">&quot;<EMAIL>&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    catalog</span><span class="token operator" style="color:#393A34">=</span><span class="token string" style="color:#e3116c">&quot;hive&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    schema</span><span class="token operator" style="color:#393A34">=</span><span class="token string" style="color:#e3116c">&quot;default&quot;</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    auth</span><span class="token operator" style="color:#393A34">=</span><span class="token plain">trino</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">auth</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">OAuth2Authentication</span><span class="token punctuation" style="color:#393A34">(</span><span class="token string" style="color:#e3116c">&quot;your-token&quot;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Execute query</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">cursor </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> conn</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">cursor</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">cursor</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">execute</span><span class="token punctuation" style="color:#393A34">(</span><span class="token triple-quoted-string string" style="color:#e3116c">&quot;&quot;&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token triple-quoted-string string" style="color:#e3116c">    SELECT date, count, value</span><br></span><span class="token-line" style="color:#393A34"><span class="token triple-quoted-string string" style="color:#e3116c">    FROM my_table</span><br></span><span class="token-line" style="color:#393A34"><span class="token triple-quoted-string string" style="color:#e3116c">    WHERE date &gt;= DATE &#x27;2024-03-20&#x27;</span><br></span><span class="token-line" style="color:#393A34"><span class="token triple-quoted-string string" style="color:#e3116c">    LIMIT 1000</span><br></span><span class="token-line" style="color:#393A34"><span class="token triple-quoted-string string" style="color:#e3116c">&quot;&quot;&quot;</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Fetch results</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">rows </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> cursor</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">fetchall</span><span class="token punctuation" style="color:#393A34">(</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">columns </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">[</span><span class="token plain">desc</span><span class="token punctuation" style="color:#393A34">[</span><span class="token number" style="color:#36acaa">0</span><span class="token punctuation" style="color:#393A34">]</span><span class="token plain"> </span><span class="token keyword" style="color:#00009f">for</span><span class="token plain"> desc </span><span class="token keyword" style="color:#00009f">in</span><span class="token plain"> cursor</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">description</span><span class="token punctuation" style="color:#393A34">]</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">df </span><span class="token operator" style="color:#393A34">=</span><span class="token plain"> pd</span><span class="token punctuation" style="color:#393A34">.</span><span class="token plain">DataFrame</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">rows</span><span class="token punctuation" style="color:#393A34">,</span><span class="token plain"> columns</span><span class="token operator" style="color:#393A34">=</span><span class="token plain">columns</span><span class="token punctuation" style="color:#393A34">)</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token keyword" style="color:#00009f">print</span><span class="token punctuation" style="color:#393A34">(</span><span class="token plain">df</span><span class="token punctuation" style="color:#393A34">)</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="curl-example">cURL Example<a href="#curl-example" class="hash-link" aria-label="Direct link to cURL Example" title="Direct link to cURL Example">​</a></h3>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain"># Submit query</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">curl -X POST https://trino.example.com/v1/statement \</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  -H &quot;Authorization: Bearer ${TOKEN}&quot; \</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  -H &quot;X-Trino-User: <EMAIL>&quot; \</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  -H &quot;X-Trino-Schema: default&quot; \</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  -H &quot;X-Trino-Catalog: hive&quot; \</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  -H &quot;X-Trino-Time-Zone: UTC&quot; \</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  -d &quot;SELECT * FROM my_table WHERE date &gt;= DATE &#x27;2024-03-20&#x27;&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Get query results</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">curl -X GET https://trino.example.com/v1/statement/20240320_100000_00000_abcde/1 \</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  -H &quot;Authorization: Bearer ${TOKEN}&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Cancel query</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">curl -X DELETE https://trino.example.com/v1/statement/20240320_100000_00000_abcde \</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  -H &quot;Authorization: Bearer ${TOKEN}&quot;</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="error-codes">Error Codes<a href="#error-codes" class="hash-link" aria-label="Direct link to Error Codes" title="Direct link to Error Codes">​</a></h2>
<table><thead><tr><th>Code</th><th>Description</th></tr></thead><tbody><tr><td>400</td><td>Bad Request - Invalid SQL query</td></tr><tr><td>401</td><td>Unauthorized - Invalid or missing authentication</td></tr><tr><td>403</td><td>Forbidden - Insufficient permissions</td></tr><tr><td>404</td><td>Not Found - Query doesn&#x27;t exist</td></tr><tr><td>429</td><td>Too Many Requests - Rate limit exceeded</td></tr><tr><td>500</td><td>Internal Server Error - Server-side error</td></tr></tbody></table>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="rate-limiting">Rate Limiting<a href="#rate-limiting" class="hash-link" aria-label="Direct link to Rate Limiting" title="Direct link to Rate Limiting">​</a></h2>
<ul>
<li>Standard rate limit: 100 requests per minute</li>
<li>Burst rate limit: 200 requests per minute</li>
<li>Rate limit headers:<!-- -->
<ul>
<li><code>X-RateLimit-Limit</code>: Maximum requests per window</li>
<li><code>X-RateLimit-Remaining</code>: Remaining requests in current window</li>
<li><code>X-RateLimit-Reset</code>: Time until rate limit resets</li>
</ul>
</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="best-practices">Best Practices<a href="#best-practices" class="hash-link" aria-label="Direct link to Best Practices" title="Direct link to Best Practices">​</a></h2>
<ol>
<li>
<p><strong>Query Optimization</strong></p>
<ul>
<li>Use appropriate filters</li>
<li>Limit result set size</li>
<li>Use proper indexing</li>
</ul>
</li>
<li>
<p><strong>Resource Management</strong></p>
<ul>
<li>Monitor query execution</li>
<li>Cancel long-running queries</li>
<li>Use appropriate timeouts</li>
</ul>
</li>
<li>
<p><strong>Performance</strong></p>
<ul>
<li>Use prepared statements</li>
<li>Implement proper error handling</li>
<li>Use connection pooling</li>
</ul>
</li>
<li>
<p><strong>Security</strong></p>
<ul>
<li>Use parameterized queries</li>
<li>Implement proper access controls</li>
<li>Monitor query patterns</li>
</ul>
</li>
</ol></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/data-management/trino-api.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/ai-architecture/tools/old/api/data-management/openmetadata-api"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">OpenMetadata API</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/ai-architecture/tools/old/api/model-management/"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">Model Management</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#base-url" class="table-of-contents__link toc-highlight">Base URL</a></li><li><a href="#authentication" class="table-of-contents__link toc-highlight">Authentication</a></li><li><a href="#api-endpoints" class="table-of-contents__link toc-highlight">API Endpoints</a><ul><li><a href="#query-execution" class="table-of-contents__link toc-highlight">Query Execution</a></li><li><a href="#query-management" class="table-of-contents__link toc-highlight">Query Management</a></li></ul></li><li><a href="#usage-examples" class="table-of-contents__link toc-highlight">Usage Examples</a><ul><li><a href="#python-example" class="table-of-contents__link toc-highlight">Python Example</a></li><li><a href="#curl-example" class="table-of-contents__link toc-highlight">cURL Example</a></li></ul></li><li><a href="#error-codes" class="table-of-contents__link toc-highlight">Error Codes</a></li><li><a href="#rate-limiting" class="table-of-contents__link toc-highlight">Rate Limiting</a></li><li><a href="#best-practices" class="table-of-contents__link toc-highlight">Best Practices</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="footer__link-item">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 91 Life, Inc. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>