<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-ai-architecture/tools/old/api/visualization/api-diagrams" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">API Interaction Diagrams | 91 Architecture Site</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://91.life/docs/ai-architecture/tools/old/api/visualization/api-diagrams"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="API Interaction Diagrams | 91 Architecture Site"><meta data-rh="true" name="description" content="This page contains diagrams illustrating the interactions between different components of the MLOps platform."><meta data-rh="true" property="og:description" content="This page contains diagrams illustrating the interactions between different components of the MLOps platform."><link data-rh="true" rel="icon" href="/img/91icon.svg"><link data-rh="true" rel="canonical" href="https://91.life/docs/ai-architecture/tools/old/api/visualization/api-diagrams"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/old/api/visualization/api-diagrams" hreflang="en"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/old/api/visualization/api-diagrams" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="91 Architecture Site RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="91 Architecture Site Atom Feed"><link rel="stylesheet" href="/assets/css/styles.ffe4d919.css">
<script src="/assets/js/runtime~main.fa55924a.js" defer="defer"></script>
<script src="/assets/js/main.5d97154b.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();null!==e?t(e):window.matchMedia("(prefers-color-scheme: dark)").matches?t("dark"):(window.matchMedia("(prefers-color-scheme: light)").matches,t("light"))}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/91icon.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top navbar--primary"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--light_rzkv"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--dark_mMAk"></div></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/intro">Documentation</a></div><div class="navbar__items navbar__items--right"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite" aria-pressed="false"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/intro">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/architecture">Architecture</a><button aria-label="Expand sidebar category &#x27;Architecture&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/decision-records">Decision Records</a><button aria-label="Expand sidebar category &#x27;Decision Records&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/research">Research</a><button aria-label="Expand sidebar category &#x27;Research&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/docs/ai-architecture/api/">ai-architecture</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/api/">API Reference</a><button aria-label="Expand sidebar category &#x27;API Reference&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/implementation/">Implementation Guide</a><button aria-label="Expand sidebar category &#x27;Implementation Guide&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/intro">Introduction</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/support/">Support</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-overview/">Architecture Overview</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-requirements/">R&amp;D Platform Architecture Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/best-practices/development/">best-practices</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/components/ai-models/">components</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/deployment/cloud-providers/">deployment</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/security/authentication/">security</a></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/system-requirements/">System Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/tools/">tools</a><button aria-label="Collapse sidebar category &#x27;tools&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-3 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" tabindex="0" href="/docs/ai-architecture/tools/old/api/">old</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/tools/old/api/">API Overview</a><button aria-label="Collapse sidebar category &#x27;API Overview&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/api/api-endpoints">MLOps Platform API Endpoints</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/data-management/">Data Management</a><button aria-label="Expand sidebar category &#x27;Data Management&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/model-management/">Model Management</a><button aria-label="Expand sidebar category &#x27;Model Management&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/monitoring/">Monitoring</a><button aria-label="Expand sidebar category &#x27;Monitoring&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/orchestration/">Orchestration</a><button aria-label="Expand sidebar category &#x27;Orchestration&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-5 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/tools/old/api/visualization/">Visualization</a><button aria-label="Collapse sidebar category &#x27;Visualization&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-6 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/docs/ai-architecture/tools/old/api/visualization/api-diagrams">API Diagrams</a></li></ul></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-detailed-scenarios">architecture</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/mlops/mlops-components">mlops</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/research/research-tools-workflows">research</a></div></li></ul></li></ul></li></ul></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">ai-architecture</span><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/tools/"><span itemprop="name">tools</span></a><meta itemprop="position" content="2"></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">old</span><meta itemprop="position" content="3"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/tools/old/api/"><span itemprop="name">API Overview</span></a><meta itemprop="position" content="4"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/tools/old/api/visualization/"><span itemprop="name">Visualization</span></a><meta itemprop="position" content="5"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">API Diagrams</span><meta itemprop="position" content="6"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>API Interaction Diagrams</h1></header>
<p>This page contains diagrams illustrating the interactions between different components of the MLOps platform.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="data-flow-architecture">Data Flow Architecture<a href="#data-flow-architecture" class="hash-link" aria-label="Direct link to Data Flow Architecture" title="Direct link to Data Flow Architecture">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Data Sources] --&gt;|Ingest| B[LakeFS]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt;|Version Control| C[MinIO]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt;|Query| D[Trino]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt;|Feature Engineering| E[Feature Store]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt;|Training| F[MLflow]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt;|Deploy| G[KServe]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    H[Kubeflow] --&gt;|Orchestrate| F</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    I[OpenMetadata] --&gt;|Track| A &amp; B &amp; C &amp; D &amp; E &amp; F &amp; G</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="model-training-pipeline">Model Training Pipeline<a href="#model-training-pipeline" class="hash-link" aria-label="Direct link to Model Training Pipeline" title="Direct link to Model Training Pipeline">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">sequenceDiagram</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant D as Data Scientist</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant K as Kubeflow</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant L as LakeFS</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant M as MLflow</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant F as Feature Store</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant S as KServe</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D-&gt;&gt;K: Submit Training Pipeline</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K-&gt;&gt;L: Checkout Data Version</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K-&gt;&gt;F: Get Features</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K-&gt;&gt;M: Start Experiment</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K-&gt;&gt;M: Log Parameters</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K-&gt;&gt;M: Log Metrics</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K-&gt;&gt;M: Register Model</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    M-&gt;&gt;S: Deploy Model</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    S--&gt;&gt;D: Model Endpoint</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="feature-serving-flow">Feature Serving Flow<a href="#feature-serving-flow" class="hash-link" aria-label="Direct link to Feature Serving Flow" title="Direct link to Feature Serving Flow">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">sequenceDiagram</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant C as Client</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant F as Feature Store</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant M as MinIO</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant T as Trino</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant S as KServe</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C-&gt;&gt;F: Request Features</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F-&gt;&gt;T: Query Latest Features</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    T-&gt;&gt;M: Read Feature Data</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    M--&gt;&gt;T: Return Data</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    T--&gt;&gt;F: Process Features</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F--&gt;&gt;C: Return Features</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C-&gt;&gt;S: Model Inference</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    S--&gt;&gt;C: Predictions</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="metadata-management">Metadata Management<a href="#metadata-management" class="hash-link" aria-label="Direct link to Metadata Management" title="Direct link to Metadata Management">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Data Sources] --&gt;|Register| B[OpenMetadata]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C[Models] --&gt;|Register| B</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D[Features] --&gt;|Register| B</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E[Pipelines] --&gt;|Register| B</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt;|Lineage| F[Lineage Graph]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt;|Tags| G[Tag Management]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt;|Search| H[Metadata Search]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="model-deployment-flow">Model Deployment Flow<a href="#model-deployment-flow" class="hash-link" aria-label="Direct link to Model Deployment Flow" title="Direct link to Model Deployment Flow">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">sequenceDiagram</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant D as Data Scientist</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant M as MLflow</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant K as KServe</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant O as OpenMetadata</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant C as Client</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D-&gt;&gt;M: Register Model</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    M-&gt;&gt;K: Deploy Model</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K-&gt;&gt;O: Register Deployment</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    O-&gt;&gt;O: Track Lineage</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C-&gt;&gt;K: Inference Request</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K--&gt;&gt;C: Prediction</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K-&gt;&gt;O: Log Usage</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="data-versioning-flow">Data Versioning Flow<a href="#data-versioning-flow" class="hash-link" aria-label="Direct link to Data Versioning Flow" title="Direct link to Data Versioning Flow">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">sequenceDiagram</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant D as Data Engineer</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant L as LakeFS</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant M as MinIO</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant O as OpenMetadata</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D-&gt;&gt;L: Create Branch</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    L-&gt;&gt;M: Write Data</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    M--&gt;&gt;L: Confirm Write</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    L-&gt;&gt;L: Commit Changes</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    L-&gt;&gt;O: Register Version</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    O-&gt;&gt;O: Update Lineage</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="feature-engineering-pipeline">Feature Engineering Pipeline<a href="#feature-engineering-pipeline" class="hash-link" aria-label="Direct link to Feature Engineering Pipeline" title="Direct link to Feature Engineering Pipeline">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Raw Data] --&gt;|Extract| B[Feature Engineering]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt;|Transform| C[Feature Store]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt;|Validate| D[Feature Registry]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt;|Serve| E[Model Training]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt;|Serve| F[Model Serving]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G[OpenMetadata] --&gt;|Track| A &amp; B &amp; C &amp; D &amp; E &amp; F</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="model-monitoring-flow">Model Monitoring Flow<a href="#model-monitoring-flow" class="hash-link" aria-label="Direct link to Model Monitoring Flow" title="Direct link to Model Monitoring Flow">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">sequenceDiagram</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant C as Client</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant S as KServe</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant M as MLflow</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant O as OpenMetadata</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C-&gt;&gt;S: Inference Request</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    S--&gt;&gt;C: Prediction</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    S-&gt;&gt;M: Log Metrics</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    S-&gt;&gt;O: Log Usage</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    O-&gt;&gt;O: Update Lineage</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    M-&gt;&gt;M: Track Performance</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="data-quality-validation-flow">Data Quality Validation Flow<a href="#data-quality-validation-flow" class="hash-link" aria-label="Direct link to Data Quality Validation Flow" title="Direct link to Data Quality Validation Flow">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">sequenceDiagram</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant D as Data Source</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant V as Validation Service</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant P as Profiling Service</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant M as Monitoring Service</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant A as Alerting Service</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant N as Notification Service</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D-&gt;&gt;V: Submit Data</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    V-&gt;&gt;V: Apply Validation Rules</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    V-&gt;&gt;P: Trigger Profiling</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    P-&gt;&gt;P: Calculate Metrics</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    P-&gt;&gt;M: Log Quality Metrics</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    M-&gt;&gt;A: Check Thresholds</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    alt Quality Issues Detected</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        A-&gt;&gt;N: Send Alert</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        N--&gt;&gt;A: Alert Sent</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    end</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    V--&gt;&gt;D: Validation Results</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<p>These diagrams provide a visual representation of how different components of the MLOps platform interact with each other. They help in understanding:</p>
<ol>
<li>Data flow between components</li>
<li>Sequence of operations in various processes</li>
<li>Dependencies between different services</li>
<li>Integration points in the platform</li>
<li>Monitoring and tracking flows</li>
</ol>
<p>For more detailed information about each component, please refer to their respective API documentation pages.</p></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/visualization/diagrams.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/ai-architecture/tools/old/api/visualization/"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Visualization</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/ai-architecture/tools/old/architecture/medical-device-detailed-scenarios"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">R&amp;D Detailed Scenarios and Flows</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#data-flow-architecture" class="table-of-contents__link toc-highlight">Data Flow Architecture</a></li><li><a href="#model-training-pipeline" class="table-of-contents__link toc-highlight">Model Training Pipeline</a></li><li><a href="#feature-serving-flow" class="table-of-contents__link toc-highlight">Feature Serving Flow</a></li><li><a href="#metadata-management" class="table-of-contents__link toc-highlight">Metadata Management</a></li><li><a href="#model-deployment-flow" class="table-of-contents__link toc-highlight">Model Deployment Flow</a></li><li><a href="#data-versioning-flow" class="table-of-contents__link toc-highlight">Data Versioning Flow</a></li><li><a href="#feature-engineering-pipeline" class="table-of-contents__link toc-highlight">Feature Engineering Pipeline</a></li><li><a href="#model-monitoring-flow" class="table-of-contents__link toc-highlight">Model Monitoring Flow</a></li><li><a href="#data-quality-validation-flow" class="table-of-contents__link toc-highlight">Data Quality Validation Flow</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="footer__link-item">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 91 Life, Inc. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>