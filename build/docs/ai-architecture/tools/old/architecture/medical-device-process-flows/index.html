<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-ai-architecture/tools/old/architecture/medical-device-process-flows" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Medical Device R&amp;D Process Flows | 91 Architecture Site</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://91.life/docs/ai-architecture/tools/old/architecture/medical-device-process-flows"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Medical Device R&amp;D Process Flows | 91 Architecture Site"><meta data-rh="true" name="description" content="1. Research and Development Process"><meta data-rh="true" property="og:description" content="1. Research and Development Process"><link data-rh="true" rel="icon" href="/img/91icon.svg"><link data-rh="true" rel="canonical" href="https://91.life/docs/ai-architecture/tools/old/architecture/medical-device-process-flows"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/old/architecture/medical-device-process-flows" hreflang="en"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/old/architecture/medical-device-process-flows" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="91 Architecture Site RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="91 Architecture Site Atom Feed"><link rel="stylesheet" href="/assets/css/styles.ffe4d919.css">
<script src="/assets/js/runtime~main.fa55924a.js" defer="defer"></script>
<script src="/assets/js/main.5d97154b.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();null!==e?t(e):window.matchMedia("(prefers-color-scheme: dark)").matches?t("dark"):(window.matchMedia("(prefers-color-scheme: light)").matches,t("light"))}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/91icon.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top navbar--primary"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--light_rzkv"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--dark_mMAk"></div></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/intro">Documentation</a></div><div class="navbar__items navbar__items--right"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite" aria-pressed="false"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/intro">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/architecture">Architecture</a><button aria-label="Expand sidebar category &#x27;Architecture&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/decision-records">Decision Records</a><button aria-label="Expand sidebar category &#x27;Decision Records&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/research">Research</a><button aria-label="Expand sidebar category &#x27;Research&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/docs/ai-architecture/api/">ai-architecture</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/api/">API Reference</a><button aria-label="Expand sidebar category &#x27;API Reference&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/implementation/">Implementation Guide</a><button aria-label="Expand sidebar category &#x27;Implementation Guide&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/intro">Introduction</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/support/">Support</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-overview/">Architecture Overview</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-requirements/">R&amp;D Platform Architecture Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/best-practices/development/">best-practices</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/components/ai-models/">components</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/deployment/cloud-providers/">deployment</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/security/authentication/">security</a></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/system-requirements/">System Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/tools/">tools</a><button aria-label="Collapse sidebar category &#x27;tools&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-3 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" tabindex="0" href="/docs/ai-architecture/tools/old/api/">old</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/">API Overview</a><button aria-label="Expand sidebar category &#x27;API Overview&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-detailed-scenarios">architecture</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-detailed-scenarios">R&amp;D Detailed Scenarios and Flows</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-ml-platform">Medical Device ML Platform Architecture</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-process-flows">Medical Device R&amp;D Process Flows</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-rd-conceptual">R&amp;D Conceptual Architecture</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-rd-detailed">R&amp;D Detailed Architecture</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-rd-platform">R&amp;D Platform Architecture</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-rd-technical-spec">Medical Device R&amp;D Platform Technical Specification</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/mlops/mlops-components">mlops</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/research/research-tools-workflows">research</a></div></li></ul></li></ul></li></ul></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">ai-architecture</span><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/tools/"><span itemprop="name">tools</span></a><meta itemprop="position" content="2"></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">old</span><meta itemprop="position" content="3"></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">architecture</span><meta itemprop="position" content="4"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Medical Device R&amp;D Process Flows</span><meta itemprop="position" content="5"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>Medical Device R&amp;D Process Flows</h1></header>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="1-research-and-development-process">1. Research and Development Process<a href="#1-research-and-development-process" class="hash-link" aria-label="Direct link to 1. Research and Development Process" title="Direct link to 1. Research and Development Process">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="11-research-initiation-flow">1.1 Research Initiation Flow<a href="#11-research-initiation-flow" class="hash-link" aria-label="Direct link to 1.1 Research Initiation Flow" title="Direct link to 1.1 Research Initiation Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Research Concept] --&gt; B[Initial Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Resource Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Team Formation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Environment Setup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Project Kickoff]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[Technical Feasibility]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Market Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Regulatory Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Budget Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Timeline Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Resource Allocation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Team Selection]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Role Assignment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Training Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[Development Environment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Testing Environment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Documentation System]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="12-data-collection-and-management-flow">1.2 Data Collection and Management Flow<a href="#12-data-collection-and-management-flow" class="hash-link" aria-label="Direct link to 1.2 Data Collection and Management Flow" title="Direct link to 1.2 Data Collection and Management Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Data Source Identification] --&gt; B[Collection Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Data Ingestion]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Data Processing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Data Storage]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Data Access]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[Collection Protocol]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Quality Criteria]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Validation Rules]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Source Connection]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Data Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Error Handling]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Cleaning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Transformation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Enrichment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[Versioning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Backup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Security]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F1[Access Control]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F2[Audit Trail]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F3[Usage Monitoring]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="2-model-development-process">2. Model Development Process<a href="#2-model-development-process" class="hash-link" aria-label="Direct link to 2. Model Development Process" title="Direct link to 2. Model Development Process">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="21-model-development-flow">2.1 Model Development Flow<a href="#21-model-development-flow" class="hash-link" aria-label="Direct link to 2.1 Model Development Flow" title="Direct link to 2.1 Model Development Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Model Design] --&gt; B[Data Preparation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Feature Engineering]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Model Training]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Model Evaluation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Model Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; G[Model Deployment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A1[Architecture Design]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A2[Parameter Selection]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A3[Performance Criteria]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[Data Splitting]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Data Augmentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Data Balancing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Feature Selection]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Feature Extraction]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Feature Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Training Setup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Training Execution]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Training Monitoring]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[Performance Metrics]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Error Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Model Comparison]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F1[Clinical Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F2[Regulatory Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F3[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G1[Deployment Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G2[Environment Setup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G3[Monitoring Setup]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="22-experiment-tracking-flow">2.2 Experiment Tracking Flow<a href="#22-experiment-tracking-flow" class="hash-link" aria-label="Direct link to 2.2 Experiment Tracking Flow" title="Direct link to 2.2 Experiment Tracking Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Experiment Initiation] --&gt; B[Parameter Tracking]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Result Recording]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A1[Experiment Design]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A2[Resource Allocation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A3[Timeline Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[Input Parameters]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Configuration]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Environment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Performance Metrics]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Error Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Resource Usage]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Result Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Comparison]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Insights]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[Technical Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Process Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Result Documentation]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="3-validation-process">3. Validation Process<a href="#3-validation-process" class="hash-link" aria-label="Direct link to 3. Validation Process" title="Direct link to 3. Validation Process">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="31-validation-flow">3.1 Validation Flow<a href="#31-validation-flow" class="hash-link" aria-label="Direct link to 3.1 Validation Flow" title="Direct link to 3.1 Validation Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Validation Planning] --&gt; B[Requirements Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Design Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Implementation Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Testing Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Performance Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; G[Clinical Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A1[Validation Strategy]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A2[Resource Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A3[Timeline Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[Requirements Review]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Compliance Check]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Design Review]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Architecture Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Code Review]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Unit Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Integration Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[System Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Performance Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Security Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F1[Performance Metrics]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F2[Stability Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F3[Scalability Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G1[Clinical Protocol]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G2[Patient Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G3[Result Analysis]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="32-clinical-validation-flow">3.2 Clinical Validation Flow<a href="#32-clinical-validation-flow" class="hash-link" aria-label="Direct link to 3.2 Clinical Validation Flow" title="Direct link to 3.2 Clinical Validation Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Protocol Development] --&gt; B[Ethics Approval]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Patient Recruitment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Data Collection]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Data Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Results Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; G[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A1[Study Design]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A2[Endpoints Definition]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A3[Safety Criteria]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[IRB Submission]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Review Process]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Approval]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Patient Screening]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Informed Consent]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Enrollment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Data Collection]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Quality Control]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Monitoring]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[Statistical Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Safety Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Efficacy Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F1[Result Review]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F2[Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F3[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G1[Clinical Report]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G2[Regulatory Submission]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    G --&gt; G3[Publication]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="4-deployment-process">4. Deployment Process<a href="#4-deployment-process" class="hash-link" aria-label="Direct link to 4. Deployment Process" title="Direct link to 4. Deployment Process">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="41-deployment-flow">4.1 Deployment Flow<a href="#41-deployment-flow" class="hash-link" aria-label="Direct link to 4.1 Deployment Flow" title="Direct link to 4.1 Deployment Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Deployment Planning] --&gt; B[Environment Setup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Configuration]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Deployment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Verification]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Monitoring]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A1[Strategy Development]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A2[Resource Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A3[Timeline Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[Infrastructure Setup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Security Setup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Monitoring Setup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[System Configuration]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Security Configuration]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Monitoring Configuration]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Deployment Execution]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Health Checks]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Rollback Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[System Verification]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Performance Verification]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Security Verification]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F1[Performance Monitoring]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F2[Health Monitoring]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F3[Security Monitoring]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="42-monitoring-flow">4.2 Monitoring Flow<a href="#42-monitoring-flow" class="hash-link" aria-label="Direct link to 4.2 Monitoring Flow" title="Direct link to 4.2 Monitoring Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Monitoring Setup] --&gt; B[Data Collection]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Alerting]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Response]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A1[Metrics Definition]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A2[Thresholds Setup]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A3[Alert Configuration]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[Performance Data]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Health Data]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Security Data]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Trend Analysis]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Anomaly Detection]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Pattern Recognition]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Alert Generation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Alert Distribution]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Alert Tracking]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[Issue Investigation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Resolution]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Prevention]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F1[Incident Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F2[Resolution Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F3[Prevention Documentation]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="5-maintenance-process">5. Maintenance Process<a href="#5-maintenance-process" class="hash-link" aria-label="Direct link to 5. Maintenance Process" title="Direct link to 5. Maintenance Process">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="51-maintenance-flow">5.1 Maintenance Flow<a href="#51-maintenance-flow" class="hash-link" aria-label="Direct link to 5.1 Maintenance Flow" title="Direct link to 5.1 Maintenance Flow">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Maintenance Planning] --&gt; B[System Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Update Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Implementation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Verification]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A1[Schedule Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A2[Resource Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A3[Risk Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[Performance Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Health Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Security Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Update Strategy]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Resource Allocation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Timeline Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Update Execution]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[System Verification]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Performance Verification]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Security Verification]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F1[Update Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F2[Process Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F3[Result Documentation]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="52-update-process">5.2 Update Process<a href="#52-update-process" class="hash-link" aria-label="Direct link to 5.2 Update Process" title="Direct link to 5.2 Update Process">​</a></h3>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Update Initiation] --&gt; B[Impact Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; C[Development]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; D[Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; E[Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; F[Deployment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A1[Change Request]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A2[Priority Assessment]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A --&gt; A3[Resource Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B1[System Impact]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B2[Process Impact]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt; B3[Documentation Impact]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C1[Code Development]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C2[Documentation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; C3[Review]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D1[Unit Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D2[Integration Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt; D3[System Testing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E1[Performance Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E2[Security Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt; E3[Compliance Validation]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F1[Deployment Planning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F2[Deployment Execution]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; F3[Post-Deployment Verification]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="conclusion">Conclusion<a href="#conclusion" class="hash-link" aria-label="Direct link to Conclusion" title="Direct link to Conclusion">​</a></h2>
<p>These process flows provide a detailed visualization of the various processes involved in medical device R&amp;D, from initial research to ongoing maintenance. Each flow diagram shows the relationships between different steps and sub-processes, helping to understand the complete lifecycle of medical device development and deployment.</p></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/architecture/medical-device-process-flows.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/ai-architecture/tools/old/architecture/medical-device-ml-platform"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Medical Device ML Platform Architecture</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/ai-architecture/tools/old/architecture/medical-device-rd-conceptual"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">R&amp;D Conceptual Architecture</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#1-research-and-development-process" class="table-of-contents__link toc-highlight">1. Research and Development Process</a><ul><li><a href="#11-research-initiation-flow" class="table-of-contents__link toc-highlight">1.1 Research Initiation Flow</a></li><li><a href="#12-data-collection-and-management-flow" class="table-of-contents__link toc-highlight">1.2 Data Collection and Management Flow</a></li></ul></li><li><a href="#2-model-development-process" class="table-of-contents__link toc-highlight">2. Model Development Process</a><ul><li><a href="#21-model-development-flow" class="table-of-contents__link toc-highlight">2.1 Model Development Flow</a></li><li><a href="#22-experiment-tracking-flow" class="table-of-contents__link toc-highlight">2.2 Experiment Tracking Flow</a></li></ul></li><li><a href="#3-validation-process" class="table-of-contents__link toc-highlight">3. Validation Process</a><ul><li><a href="#31-validation-flow" class="table-of-contents__link toc-highlight">3.1 Validation Flow</a></li><li><a href="#32-clinical-validation-flow" class="table-of-contents__link toc-highlight">3.2 Clinical Validation Flow</a></li></ul></li><li><a href="#4-deployment-process" class="table-of-contents__link toc-highlight">4. Deployment Process</a><ul><li><a href="#41-deployment-flow" class="table-of-contents__link toc-highlight">4.1 Deployment Flow</a></li><li><a href="#42-monitoring-flow" class="table-of-contents__link toc-highlight">4.2 Monitoring Flow</a></li></ul></li><li><a href="#5-maintenance-process" class="table-of-contents__link toc-highlight">5. Maintenance Process</a><ul><li><a href="#51-maintenance-flow" class="table-of-contents__link toc-highlight">5.1 Maintenance Flow</a></li><li><a href="#52-update-process" class="table-of-contents__link toc-highlight">5.2 Update Process</a></li></ul></li><li><a href="#conclusion" class="table-of-contents__link toc-highlight">Conclusion</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="footer__link-item">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 91 Life, Inc. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>