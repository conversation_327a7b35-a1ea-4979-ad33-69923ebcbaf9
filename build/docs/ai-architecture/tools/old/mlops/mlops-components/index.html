<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-ai-architecture/tools/old/mlops/mlops-components" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">MLOps Components | 91 Architecture Site</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://91.life/docs/ai-architecture/tools/old/mlops/mlops-components"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="MLOps Components | 91 Architecture Site"><meta data-rh="true" name="description" content="This guide provides a comprehensive overview of the components that make up a production-grade MLOps platform, categorizing them as either essential (MUST HAVE) or optional based on your specific needs."><meta data-rh="true" property="og:description" content="This guide provides a comprehensive overview of the components that make up a production-grade MLOps platform, categorizing them as either essential (MUST HAVE) or optional based on your specific needs."><link data-rh="true" rel="icon" href="/img/91icon.svg"><link data-rh="true" rel="canonical" href="https://91.life/docs/ai-architecture/tools/old/mlops/mlops-components"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/old/mlops/mlops-components" hreflang="en"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/tools/old/mlops/mlops-components" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="91 Architecture Site RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="91 Architecture Site Atom Feed"><link rel="stylesheet" href="/assets/css/styles.ffe4d919.css">
<script src="/assets/js/runtime~main.fa55924a.js" defer="defer"></script>
<script src="/assets/js/main.5d97154b.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();null!==e?t(e):window.matchMedia("(prefers-color-scheme: dark)").matches?t("dark"):(window.matchMedia("(prefers-color-scheme: light)").matches,t("light"))}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/91icon.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top navbar--primary"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--light_rzkv"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--dark_mMAk"></div></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/intro">Documentation</a></div><div class="navbar__items navbar__items--right"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite" aria-pressed="false"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/intro">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/architecture">Architecture</a><button aria-label="Expand sidebar category &#x27;Architecture&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/decision-records">Decision Records</a><button aria-label="Expand sidebar category &#x27;Decision Records&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/research">Research</a><button aria-label="Expand sidebar category &#x27;Research&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/docs/ai-architecture/api/">ai-architecture</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/api/">API Reference</a><button aria-label="Expand sidebar category &#x27;API Reference&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/implementation/">Implementation Guide</a><button aria-label="Expand sidebar category &#x27;Implementation Guide&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/intro">Introduction</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/support/">Support</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-overview/">Architecture Overview</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-requirements/">R&amp;D Platform Architecture Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/best-practices/development/">best-practices</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/components/ai-models/">components</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/deployment/cloud-providers/">deployment</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/security/authentication/">security</a></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/system-requirements/">System Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/tools/">tools</a><button aria-label="Collapse sidebar category &#x27;tools&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-3 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" tabindex="0" href="/docs/ai-architecture/tools/old/api/">old</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/old/api/">API Overview</a><button aria-label="Expand sidebar category &#x27;API Overview&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/architecture/medical-device-detailed-scenarios">architecture</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" tabindex="0" href="/docs/ai-architecture/tools/old/mlops/mlops-components">mlops</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-5 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/docs/ai-architecture/tools/old/mlops/mlops-components">MLOps Components</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-4 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/tools/old/research/research-tools-workflows">research</a></div></li></ul></li></ul></li></ul></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">ai-architecture</span><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/tools/"><span itemprop="name">tools</span></a><meta itemprop="position" content="2"></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">old</span><meta itemprop="position" content="3"></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">mlops</span><meta itemprop="position" content="4"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">MLOps Components</span><meta itemprop="position" content="5"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>MLOps Components: Essential vs Optional</h1></header>
<p>This guide provides a comprehensive overview of the components that make up a production-grade MLOps platform, categorizing them as either essential (MUST HAVE) or optional based on your specific needs.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="component-overview-diagram">Component Overview Diagram<a href="#component-overview-diagram" class="hash-link" aria-label="Direct link to Component Overview Diagram" title="Direct link to Component Overview Diagram">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    subgraph &quot;Essential Components&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        A[Data Versioning] --&gt; B[Feature Store]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        B --&gt; C[ML Pipeline Orchestration]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        C --&gt; D[Model Registry]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        D --&gt; E[Model Serving]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        F[Monitoring] --&gt; A</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        F --&gt; B</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        F --&gt; C</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        F --&gt; D</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        F --&gt; E</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    end</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    subgraph &quot;Optional Components&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        G[Data Catalog] -.-&gt; A</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        H[Experiment Tracking] -.-&gt; C</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        I[Model Testing] -.-&gt; D</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        J[Feature Monitoring] -.-&gt; B</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        K[Model Explainability] -.-&gt; E</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    end</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="essential-must-have-components">Essential (MUST HAVE) Components<a href="#essential-must-have-components" class="hash-link" aria-label="Direct link to Essential (MUST HAVE) Components" title="Direct link to Essential (MUST HAVE) Components">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-data-versioning">1. Data Versioning<a href="#1-data-versioning" class="hash-link" aria-label="Direct link to 1. Data Versioning" title="Direct link to 1. Data Versioning">​</a></h3>
<p><strong>Purpose</strong>: Track and manage different versions of datasets
<strong>Key Features</strong>:</p>
<ul>
<li>Git-like versioning for datasets</li>
<li>Rollback capabilities</li>
<li>Data lineage tracking
<strong>Tools</strong>: LakeFS, DVC
<strong>Why Essential</strong>: Ensures reproducibility and compliance</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-feature-store">2. Feature Store<a href="#2-feature-store" class="hash-link" aria-label="Direct link to 2. Feature Store" title="Direct link to 2. Feature Store">​</a></h3>
<p><strong>Purpose</strong>: Centralized storage and serving of ML features
<strong>Key Features</strong>:</p>
<ul>
<li>Feature versioning</li>
<li>Online/offline serving</li>
<li>Feature computation pipeline
<strong>Tools</strong>: Feast, Tecton, Hopsworks
<strong>Why Essential</strong>: Ensures consistent feature serving in training and production</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-ml-pipeline-orchestration">3. ML Pipeline Orchestration<a href="#3-ml-pipeline-orchestration" class="hash-link" aria-label="Direct link to 3. ML Pipeline Orchestration" title="Direct link to 3. ML Pipeline Orchestration">​</a></h3>
<p><strong>Purpose</strong>: Automate and manage ML workflows
<strong>Key Features</strong>:</p>
<ul>
<li>Pipeline versioning</li>
<li>Dependency management</li>
<li>Error handling
<strong>Tools</strong>: Kubeflow, Airflow, MLflow Pipelines
<strong>Why Essential</strong>: Enables reproducible and scalable ML workflows</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="4-model-registry">4. Model Registry<a href="#4-model-registry" class="hash-link" aria-label="Direct link to 4. Model Registry" title="Direct link to 4. Model Registry">​</a></h3>
<p><strong>Purpose</strong>: Track and manage model versions
<strong>Key Features</strong>:</p>
<ul>
<li>Model versioning</li>
<li>Model metadata storage</li>
<li>Model stage transitions
<strong>Tools</strong>: MLflow, DVC
<strong>Why Essential</strong>: Ensures model traceability and governance</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="5-model-serving">5. Model Serving<a href="#5-model-serving" class="hash-link" aria-label="Direct link to 5. Model Serving" title="Direct link to 5. Model Serving">​</a></h3>
<p><strong>Purpose</strong>: Deploy and serve models in production
<strong>Key Features</strong>:</p>
<ul>
<li>REST/gRPC endpoints</li>
<li>A/B testing</li>
<li>Canary deployments
<strong>Tools</strong>: KServe, TensorFlow Serving, TorchServe
<strong>Why Essential</strong>: Enables model deployment and inference</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="6-monitoring">6. Monitoring<a href="#6-monitoring" class="hash-link" aria-label="Direct link to 6. Monitoring" title="Direct link to 6. Monitoring">​</a></h3>
<p><strong>Purpose</strong>: Track system and model performance
<strong>Key Features</strong>:</p>
<ul>
<li>Model performance metrics</li>
<li>System health checks</li>
<li>Alerting
<strong>Tools</strong>: Prometheus, Grafana, Evidently
<strong>Why Essential</strong>: Ensures system reliability and model performance</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="optional-components">Optional Components<a href="#optional-components" class="hash-link" aria-label="Direct link to Optional Components" title="Direct link to Optional Components">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-data-catalog">1. Data Catalog<a href="#1-data-catalog" class="hash-link" aria-label="Direct link to 1. Data Catalog" title="Direct link to 1. Data Catalog">​</a></h3>
<p><strong>Purpose</strong>: Document and discover datasets
<strong>Key Features</strong>:</p>
<ul>
<li>Dataset documentation</li>
<li>Data discovery</li>
<li>Data quality metrics
<strong>Tools</strong>: OpenMetadata, Amundsen
<strong>When to Use</strong>: When you need better data discovery and documentation</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-experiment-tracking">2. Experiment Tracking<a href="#2-experiment-tracking" class="hash-link" aria-label="Direct link to 2. Experiment Tracking" title="Direct link to 2. Experiment Tracking">​</a></h3>
<p><strong>Purpose</strong>: Track ML experiments and their results
<strong>Key Features</strong>:</p>
<ul>
<li>Experiment logging</li>
<li>Parameter tracking</li>
<li>Metric visualization
<strong>Tools</strong>: MLflow, Weights &amp; Biases
<strong>When to Use</strong>: When you need detailed experiment tracking and comparison</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-model-testing">3. Model Testing<a href="#3-model-testing" class="hash-link" aria-label="Direct link to 3. Model Testing" title="Direct link to 3. Model Testing">​</a></h3>
<p><strong>Purpose</strong>: Validate model behavior and performance
<strong>Key Features</strong>:</p>
<ul>
<li>Unit tests for models</li>
<li>Integration tests</li>
<li>Performance benchmarks
<strong>Tools</strong>: Great Expectations, ModelUnit
<strong>When to Use</strong>: When you need rigorous model validation</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="4-feature-monitoring">4. Feature Monitoring<a href="#4-feature-monitoring" class="hash-link" aria-label="Direct link to 4. Feature Monitoring" title="Direct link to 4. Feature Monitoring">​</a></h3>
<p><strong>Purpose</strong>: Track feature drift and quality
<strong>Key Features</strong>:</p>
<ul>
<li>Feature drift detection</li>
<li>Data quality monitoring</li>
<li>Statistical analysis
<strong>Tools</strong>: Evidently, WhyLogs
<strong>When to Use</strong>: When you need to ensure feature stability</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="5-model-explainability">5. Model Explainability<a href="#5-model-explainability" class="hash-link" aria-label="Direct link to 5. Model Explainability" title="Direct link to 5. Model Explainability">​</a></h3>
<p><strong>Purpose</strong>: Understand model predictions
<strong>Key Features</strong>:</p>
<ul>
<li>Feature importance</li>
<li>Prediction explanations</li>
<li>Model interpretability
<strong>Tools</strong>: SHAP, LIME, Captum
<strong>When to Use</strong>: When you need to explain model decisions</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="component-integration-diagram">Component Integration Diagram<a href="#component-integration-diagram" class="hash-link" aria-label="Direct link to Component Integration Diagram" title="Direct link to Component Integration Diagram">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">sequenceDiagram</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant Data</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant Features</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant Pipeline</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant Model</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant Serving</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant Monitor</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Data-&gt;&gt;Features: Version &amp; Process</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Features-&gt;&gt;Pipeline: Train</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Pipeline-&gt;&gt;Model: Register</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Model-&gt;&gt;Serving: Deploy</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Monitor-&gt;&gt;Data: Track Quality</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Monitor-&gt;&gt;Features: Track Drift</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Monitor-&gt;&gt;Model: Track Performance</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Monitor-&gt;&gt;Serving: Track Latency</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="best-practices-for-component-selection">Best Practices for Component Selection<a href="#best-practices-for-component-selection" class="hash-link" aria-label="Direct link to Best Practices for Component Selection" title="Direct link to Best Practices for Component Selection">​</a></h2>
<ol>
<li>
<p><strong>Start with Essentials</strong></p>
<ul>
<li>Begin with the six essential components</li>
<li>Ensure they are properly integrated</li>
<li>Establish monitoring from day one</li>
</ul>
</li>
<li>
<p><strong>Add Optional Components Based on Needs</strong></p>
<ul>
<li>Evaluate your specific requirements</li>
<li>Consider team size and expertise</li>
<li>Assess compliance and regulatory needs</li>
</ul>
</li>
<li>
<p><strong>Integration Considerations</strong></p>
<ul>
<li>Ensure components can communicate</li>
<li>Maintain consistent versioning</li>
<li>Implement proper security measures</li>
</ul>
</li>
<li>
<p><strong>Scaling Strategy</strong></p>
<ul>
<li>Start simple, add complexity as needed</li>
<li>Monitor component performance</li>
<li>Plan for future growth</li>
</ul>
</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="component-selection-decision-tree">Component Selection Decision Tree<a href="#component-selection-decision-tree" class="hash-link" aria-label="Direct link to Component Selection Decision Tree" title="Direct link to Component Selection Decision Tree">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Start MLOps Implementation] --&gt; B{Need Data Versioning?}</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt;|Yes| C[Implement Data Versioning]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt;|No| D[Skip]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt; E{Need Feature Store?}</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt;|Yes| F[Implement Feature Store]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt;|No| G[Skip]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt; H{Need Pipeline Orchestration?}</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    H --&gt;|Yes| I[Implement Pipeline Orchestration]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    H --&gt;|No| J[Skip]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    I --&gt; K{Need Model Registry?}</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K --&gt;|Yes| L[Implement Model Registry]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    K --&gt;|No| M[Skip]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    L --&gt; N{Need Model Serving?}</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    N --&gt;|Yes| O[Implement Model Serving]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    N --&gt;|No| P[Skip]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    O --&gt; Q{Need Monitoring?}</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Q --&gt;|Yes| R[Implement Monitoring]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Q --&gt;|No| S[Skip]</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<p>The success of your MLOps implementation depends not just on the components you choose, but on how well they are integrated and maintained.</p></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/mlops/components.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/ai-architecture/tools/old/architecture/medical-device-rd-technical-spec"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Medical Device R&amp;D Platform Technical Specification</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/ai-architecture/tools/old/research/research-tools-workflows"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">Research Tools and Workflows</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#component-overview-diagram" class="table-of-contents__link toc-highlight">Component Overview Diagram</a></li><li><a href="#essential-must-have-components" class="table-of-contents__link toc-highlight">Essential (MUST HAVE) Components</a><ul><li><a href="#1-data-versioning" class="table-of-contents__link toc-highlight">1. Data Versioning</a></li><li><a href="#2-feature-store" class="table-of-contents__link toc-highlight">2. Feature Store</a></li><li><a href="#3-ml-pipeline-orchestration" class="table-of-contents__link toc-highlight">3. ML Pipeline Orchestration</a></li><li><a href="#4-model-registry" class="table-of-contents__link toc-highlight">4. Model Registry</a></li><li><a href="#5-model-serving" class="table-of-contents__link toc-highlight">5. Model Serving</a></li><li><a href="#6-monitoring" class="table-of-contents__link toc-highlight">6. Monitoring</a></li></ul></li><li><a href="#optional-components" class="table-of-contents__link toc-highlight">Optional Components</a><ul><li><a href="#1-data-catalog" class="table-of-contents__link toc-highlight">1. Data Catalog</a></li><li><a href="#2-experiment-tracking" class="table-of-contents__link toc-highlight">2. Experiment Tracking</a></li><li><a href="#3-model-testing" class="table-of-contents__link toc-highlight">3. Model Testing</a></li><li><a href="#4-feature-monitoring" class="table-of-contents__link toc-highlight">4. Feature Monitoring</a></li><li><a href="#5-model-explainability" class="table-of-contents__link toc-highlight">5. Model Explainability</a></li></ul></li><li><a href="#component-integration-diagram" class="table-of-contents__link toc-highlight">Component Integration Diagram</a></li><li><a href="#best-practices-for-component-selection" class="table-of-contents__link toc-highlight">Best Practices for Component Selection</a></li><li><a href="#component-selection-decision-tree" class="table-of-contents__link toc-highlight">Component Selection Decision Tree</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="footer__link-item">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 91 Life, Inc. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>