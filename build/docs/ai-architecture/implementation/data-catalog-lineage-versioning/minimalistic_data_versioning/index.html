<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.7.0">
<title data-rh="true">Data Versioning Implementation | 91 Architecture Site</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://91.life/img/docusaurus-social-card.jpg"><meta data-rh="true" property="og:url" content="https://91.life/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Data Versioning Implementation | 91 Architecture Site"><meta data-rh="true" name="description" content="Overview"><meta data-rh="true" property="og:description" content="Overview"><link data-rh="true" rel="icon" href="/img/91icon.svg"><link data-rh="true" rel="canonical" href="https://91.life/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning" hreflang="en"><link data-rh="true" rel="alternate" href="https://91.life/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning" hreflang="x-default"><link rel="alternate" type="application/rss+xml" href="/blog/rss.xml" title="91 Architecture Site RSS Feed">
<link rel="alternate" type="application/atom+xml" href="/blog/atom.xml" title="91 Architecture Site Atom Feed"><link rel="stylesheet" href="/assets/css/styles.ffe4d919.css">
<script src="/assets/js/runtime~main.fa55924a.js" defer="defer"></script>
<script src="/assets/js/main.5d97154b.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();null!==e?t(e):window.matchMedia("(prefers-color-scheme: dark)").matches?t("dark"):(window.matchMedia("(prefers-color-scheme: light)").matches,t("light"))}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/91icon.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top navbar--primary"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--light_rzkv"><img src="/img/91icon.svg" alt="91 Architecture" class="themedComponent__dKv themedComponent--dark_mMAk"></div></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/docs/intro">Documentation</a></div><div class="navbar__items navbar__items--right"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite" aria-pressed="false"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/docs/intro">Introduction</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/architecture">Architecture</a><button aria-label="Expand sidebar category &#x27;Architecture&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/decision-records">Decision Records</a><button aria-label="Expand sidebar category &#x27;Decision Records&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" href="/docs/category/research">Research</a><button aria-label="Expand sidebar category &#x27;Research&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/docs/ai-architecture/api/">ai-architecture</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/api/">API Reference</a><button aria-label="Expand sidebar category &#x27;API Reference&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--active" tabindex="0" href="/docs/ai-architecture/implementation/">Implementation Guide</a><button aria-label="Collapse sidebar category &#x27;Implementation Guide&#x27;" aria-expanded="true" type="button" class="clean-btn menu__caret"></button></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-3 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/implementation/model-development/">Model Development</a><button aria-label="Expand sidebar category &#x27;Model Development&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-3 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" tabindex="0" href="/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_catalog">data-catalog-lineage-versioning</a></div><ul style="display:block;overflow:visible;height:auto" class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-4 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_catalog">Data Catalog Implementation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-4 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_lineage">Data Lineage Implementation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-4 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning">Data Versioning Implementation</a></li></ul></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-3 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/implementation/feature-store/minimalistic_feature_monitoring">feature-store</a></div></li></ul></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/intro">Introduction</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/support/">Support</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-overview/">Architecture Overview</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/architecture-requirements/">R&amp;D Platform Architecture Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/best-practices/development/">best-practices</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/components/ai-models/">components</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/deployment/cloud-providers/">deployment</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" tabindex="0" href="/docs/ai-architecture/security/authentication/">security</a></div></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/docs/ai-architecture/system-requirements/">System Requirements</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist" tabindex="0" href="/docs/ai-architecture/tools/">tools</a><button aria-label="Expand sidebar category &#x27;tools&#x27;" aria-expanded="false" type="button" class="clean-btn menu__caret"></button></div></li></ul></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">ai-architecture</span><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item"><a class="breadcrumbs__link" itemprop="item" href="/docs/ai-architecture/implementation/"><span itemprop="name">Implementation Guide</span></a><meta itemprop="position" content="2"></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">data-catalog-lineage-versioning</span><meta itemprop="position" content="3"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Data Versioning Implementation</span><meta itemprop="position" content="4"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>Data Versioning Implementation</h1></header>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="overview">Overview<a href="#overview" class="hash-link" aria-label="Direct link to Overview" title="Direct link to Overview">​</a></h2>
<p>This document outlines a minimalistic approach to data versioning that leverages existing infrastructure while providing robust version control capabilities.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="architecture-diagram">Architecture Diagram<a href="#architecture-diagram" class="hash-link" aria-label="Direct link to Architecture Diagram" title="Direct link to Architecture Diagram">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">graph TD</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    A[Data Sources] --&gt;|Ingest| B[Data Processing]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    B --&gt;|Version| C[Version Control System]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt;|Store| D[MinIO Storage]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    C --&gt;|Metadata| E[PostgreSQL/MongoDB]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    D --&gt;|Retrieve| F[Data Access Layer]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    E --&gt;|Query| F</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    F --&gt;|Serve| G[Applications]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    subgraph &quot;Version Control System&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        C1[Git LFS] --&gt; C2[Version Manager]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        C2 --&gt; C3[Hash Generator]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        C3 --&gt; C4[Metadata Extractor]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    end</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    subgraph &quot;Storage Layer&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        D1[Object Storage] --&gt; D2[Version Tracking]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        D2 --&gt; D3[Access Control]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    end</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    subgraph &quot;Metadata Layer&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        E1[Version Info] --&gt; E2[Lineage Tracking]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        E2 --&gt; E3[Access Logs]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    end</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="data-flow-sequence">Data Flow Sequence<a href="#data-flow-sequence" class="hash-link" aria-label="Direct link to Data Flow Sequence" title="Direct link to Data Flow Sequence">​</a></h2>
<div class="language-mermaid codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-mermaid codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">sequenceDiagram</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant User</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant VCS as Version Control</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant Storage as MinIO</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant DB as PostgreSQL/MongoDB</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    participant App as Application</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    User-&gt;&gt;VCS: Request Version Creation</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    VCS-&gt;&gt;Storage: Store Data Object</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Storage--&gt;&gt;VCS: Object ID</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    VCS-&gt;&gt;DB: Store Metadata</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    DB--&gt;&gt;VCS: Confirmation</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    VCS--&gt;&gt;User: Version Hash</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    User-&gt;&gt;App: Request Data</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    App-&gt;&gt;VCS: Get Version</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    VCS-&gt;&gt;DB: Query Metadata</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    DB--&gt;&gt;VCS: Version Info</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    VCS-&gt;&gt;Storage: Retrieve Object</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    Storage--&gt;&gt;VCS: Data</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    VCS--&gt;&gt;App: Versioned Data</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    App--&gt;&gt;User: Processed Data</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="core-components">Core Components<a href="#core-components" class="hash-link" aria-label="Direct link to Core Components" title="Direct link to Core Components">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-git--git-lfs">1. Git + Git LFS<a href="#1-git--git-lfs" class="hash-link" aria-label="Direct link to 1. Git + Git LFS" title="Direct link to 1. Git + Git LFS">​</a></h3>
<ul>
<li><strong>Purpose</strong>: Version control for code and small files</li>
<li><strong>Implementation</strong>:<!-- -->
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain"># Initialize Git LFS</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git lfs install</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Track large files</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git lfs track &quot;*.csv&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git lfs track &quot;*.parquet&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git lfs track &quot;*.json&quot;</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Add and commit</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git add .gitattributes</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">git commit -m &quot;Configure Git LFS&quot;</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
</li>
<li><strong>Benefits</strong>:<!-- -->
<ul>
<li>Familiar workflow</li>
<li>Built-in branching</li>
<li>Efficient storage</li>
<li>Large file handling</li>
</ul>
</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-minio-integration">2. MinIO Integration<a href="#2-minio-integration" class="hash-link" aria-label="Direct link to 2. MinIO Integration" title="Direct link to 2. MinIO Integration">​</a></h3>
<ul>
<li>
<p><strong>Purpose</strong>: Object storage for versioned data</p>
</li>
<li>
<p><strong>Features</strong>:</p>
<ul>
<li>S3-compatible API</li>
<li>Version tracking</li>
<li>Access control</li>
<li>Object lifecycle management</li>
</ul>
</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-postgresqlmongodb">3. PostgreSQL/MongoDB<a href="#3-postgresqlmongodb" class="hash-link" aria-label="Direct link to 3. PostgreSQL/MongoDB" title="Direct link to 3. PostgreSQL/MongoDB">​</a></h3>
<ul>
<li><strong>Purpose</strong>: Metadata and version information storage</li>
<li><strong>Features</strong>:<!-- -->
<ul>
<li>Version tracking</li>
<li>Metadata storage</li>
<li>Lineage tracking</li>
<li>Query capabilities</li>
</ul>
</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="implementation-alternatives">Implementation Alternatives<a href="#implementation-alternatives" class="hash-link" aria-label="Direct link to Implementation Alternatives" title="Direct link to Implementation Alternatives">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-dvc-based-implementation">1. DVC-based Implementation<a href="#1-dvc-based-implementation" class="hash-link" aria-label="Direct link to 1. DVC-based Implementation" title="Direct link to 1. DVC-based Implementation">​</a></h3>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-custom-hash-based-implementation">2. Custom Hash-based Implementation<a href="#2-custom-hash-based-implementation" class="hash-link" aria-label="Direct link to 2. Custom Hash-based Implementation" title="Direct link to 2. Custom Hash-based Implementation">​</a></h3>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-lakefs-based-implementation">3. LakeFS-based Implementation<a href="#3-lakefs-based-implementation" class="hash-link" aria-label="Direct link to 3. LakeFS-based Implementation" title="Direct link to 3. LakeFS-based Implementation">​</a></h3>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="4-git-lfs--custom-metadata">4. Git LFS + Custom Metadata<a href="#4-git-lfs--custom-metadata" class="hash-link" aria-label="Direct link to 4. Git LFS + Custom Metadata" title="Direct link to 4. Git LFS + Custom Metadata">​</a></h3>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="integration-patterns">Integration Patterns<a href="#integration-patterns" class="hash-link" aria-label="Direct link to Integration Patterns" title="Direct link to Integration Patterns">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="1-airflow-integration">1. Airflow Integration<a href="#1-airflow-integration" class="hash-link" aria-label="Direct link to 1. Airflow Integration" title="Direct link to 1. Airflow Integration">​</a></h3>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="2-kubeflow-integration">2. Kubeflow Integration<a href="#2-kubeflow-integration" class="hash-link" aria-label="Direct link to 2. Kubeflow Integration" title="Direct link to 2. Kubeflow Integration">​</a></h3>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="3-api-integration">3. API Integration<a href="#3-api-integration" class="hash-link" aria-label="Direct link to 3. API Integration" title="Direct link to 3. API Integration">​</a></h3>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="best-practices">Best Practices<a href="#best-practices" class="hash-link" aria-label="Direct link to Best Practices" title="Direct link to Best Practices">​</a></h2>
<ol>
<li>
<p><strong>Version Naming</strong>:</p>
<ul>
<li>Use semantic versioning for releases</li>
<li>Use content hashes for development versions</li>
<li>Include metadata in version names</li>
</ul>
</li>
<li>
<p><strong>Metadata Management</strong>:</p>
<ul>
<li>Store comprehensive metadata</li>
<li>Include lineage information</li>
<li>Track data quality metrics</li>
</ul>
</li>
<li>
<p><strong>Storage Optimization</strong>:</p>
<ul>
<li>Use compression for large files</li>
<li>Implement cleanup policies</li>
<li>Monitor storage usage</li>
</ul>
</li>
<li>
<p><strong>Access Control</strong>:</p>
<ul>
<li>Implement role-based access</li>
<li>Track access logs</li>
<li>Enforce version policies</li>
</ul>
</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="monitoring-and-maintenance">Monitoring and Maintenance<a href="#monitoring-and-maintenance" class="hash-link" aria-label="Direct link to Monitoring and Maintenance" title="Direct link to Monitoring and Maintenance">​</a></h2>
<ol>
<li><strong>Health Checks</strong>:</li>
<li><strong>Cleanup Procedures</strong>:</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="future-enhancements">Future Enhancements<a href="#future-enhancements" class="hash-link" aria-label="Direct link to Future Enhancements" title="Direct link to Future Enhancements">​</a></h2>
<ol>
<li>
<p><strong>Data Quality Integration</strong>:</p>
<ul>
<li>Add validation checks</li>
<li>Track quality metrics</li>
<li>Implement quality gates</li>
</ul>
</li>
<li>
<p><strong>Advanced Lineage</strong>:</p>
<ul>
<li>Track data dependencies</li>
<li>Visualize data flow</li>
<li>Impact analysis</li>
</ul>
</li>
<li>
<p><strong>Performance Optimization</strong>:</p>
<ul>
<li>Implement caching</li>
<li>Add parallel processing</li>
<li>Optimize storage</li>
</ul>
</li>
<li>
<p><strong>Security Enhancements</strong>:</p>
<ul>
<li>Add encryption</li>
<li>Implement audit trails</li>
<li>Enhance access control</li>
</ul>
</li>
</ol></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://bitbucket.org/arhm/architecture/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_lineage"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Data Lineage Implementation</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/docs/ai-architecture/implementation/feature-store/minimalistic_feature_monitoring"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">Feature Monitoring Implementation</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#overview" class="table-of-contents__link toc-highlight">Overview</a></li><li><a href="#architecture-diagram" class="table-of-contents__link toc-highlight">Architecture Diagram</a></li><li><a href="#data-flow-sequence" class="table-of-contents__link toc-highlight">Data Flow Sequence</a></li><li><a href="#core-components" class="table-of-contents__link toc-highlight">Core Components</a><ul><li><a href="#1-git--git-lfs" class="table-of-contents__link toc-highlight">1. Git + Git LFS</a></li><li><a href="#2-minio-integration" class="table-of-contents__link toc-highlight">2. MinIO Integration</a></li><li><a href="#3-postgresqlmongodb" class="table-of-contents__link toc-highlight">3. PostgreSQL/MongoDB</a></li></ul></li><li><a href="#implementation-alternatives" class="table-of-contents__link toc-highlight">Implementation Alternatives</a><ul><li><a href="#1-dvc-based-implementation" class="table-of-contents__link toc-highlight">1. DVC-based Implementation</a></li><li><a href="#2-custom-hash-based-implementation" class="table-of-contents__link toc-highlight">2. Custom Hash-based Implementation</a></li><li><a href="#3-lakefs-based-implementation" class="table-of-contents__link toc-highlight">3. LakeFS-based Implementation</a></li><li><a href="#4-git-lfs--custom-metadata" class="table-of-contents__link toc-highlight">4. Git LFS + Custom Metadata</a></li></ul></li><li><a href="#integration-patterns" class="table-of-contents__link toc-highlight">Integration Patterns</a><ul><li><a href="#1-airflow-integration" class="table-of-contents__link toc-highlight">1. Airflow Integration</a></li><li><a href="#2-kubeflow-integration" class="table-of-contents__link toc-highlight">2. Kubeflow Integration</a></li><li><a href="#3-api-integration" class="table-of-contents__link toc-highlight">3. API Integration</a></li></ul></li><li><a href="#best-practices" class="table-of-contents__link toc-highlight">Best Practices</a></li><li><a href="#monitoring-and-maintenance" class="table-of-contents__link toc-highlight">Monitoring and Maintenance</a></li><li><a href="#future-enhancements" class="table-of-contents__link toc-highlight">Future Enhancements</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="col footer__col"><div class="footer__title">More</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://bitbucket.org/arhm/architecture" target="_blank" rel="noopener noreferrer" class="footer__link-item">Bitbucket<svg width="13.5" height="13.5" aria-hidden="true" viewBox="0 0 24 24" class="iconExternalLink_nPIU"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"></path></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 91 Life, Inc. Built with Docusaurus.</div></div></div></footer></div>
</body>
</html>