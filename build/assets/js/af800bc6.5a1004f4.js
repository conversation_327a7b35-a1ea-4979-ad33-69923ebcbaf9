"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[9367],{756:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>r,contentTitle:()=>l,default:()=>p,frontMatter:()=>c,metadata:()=>t,toc:()=>d});const t=JSON.parse('{"id":"ai-architecture/api/data/governance","title":"Data Governance","description":"Manage data access, compliance, and security policies.","source":"@site/docs/ai-architecture/api/data/governance.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/governance","permalink":"/docs/ai-architecture/api/data/governance","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/governance.md","tags":[],"version":"current","sidebarPosition":10,"frontMatter":{"sidebar_position":10},"sidebar":"tutorialSidebar","previous":{"title":"Data Monitoring","permalink":"/docs/ai-architecture/api/data/monitoring"},"next":{"title":"List Models","permalink":"/docs/ai-architecture/api/models/list-models"}}');var s=i(4848),a=i(8453);const c={sidebar_position:10},l="Data Governance",r={},d=[{value:"Create Access Policy",id:"create-access-policy",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Policy Details",id:"get-policy-details",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"List Policies",id:"list-policies",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Policy Status",id:"policy-status",level:2},{value:"Policy Types",id:"policy-types",level:2},{value:"Access Policies",id:"access-policies",level:3},{value:"Compliance Policies",id:"compliance-policies",level:3},{value:"Security Policies",id:"security-policies",level:3},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Governance Best Practices",id:"governance-best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,a.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"data-governance",children:"Data Governance"})}),"\n",(0,s.jsx)(n.p,{children:"Manage data access, compliance, and security policies."}),"\n",(0,s.jsx)(n.h2,{id:"create-access-policy",children:"Create Access Policy"}),"\n",(0,s.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"POST /v1/data/datasets/{dataset_id}/policies\n"})}),"\n",(0,s.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n  "name": "restricted_access",\n  "description": "Restrict access to sensitive data",\n  "type": "access",\n  "rules": [\n    {\n      "role": "data_scientist",\n      "permissions": ["read", "write"],\n      "conditions": {\n        "ip_range": ["10.0.0.0/24"],\n        "time_window": {\n          "start": "09:00",\n          "end": "17:00",\n          "timezone": "UTC"\n        }\n      }\n    },\n    {\n      "role": "analyst",\n      "permissions": ["read"],\n      "conditions": {\n        "ip_range": ["10.0.0.0/24"],\n        "time_window": {\n          "start": "09:00",\n          "end": "17:00",\n          "timezone": "UTC"\n        }\n      }\n    }\n  ],\n  "metadata": {\n    "compliance": ["GDPR", "HIPAA"],\n    "data_classification": "sensitive",\n    "retention_period": "365d"\n  }\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "policy": {\n      "id": "policy_123",\n      "dataset_id": "dataset_123",\n      "name": "restricted_access",\n      "type": "access",\n      "status": "active",\n      "created_at": "2024-03-14T12:00:00Z",\n      "rules": [\n        {\n          "role": "data_scientist",\n          "permissions": ["read", "write"]\n        }\n      ]\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"get-policy-details",children:"Get Policy Details"}),"\n",(0,s.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/policies/{policy_id}\n"})}),"\n",(0,s.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "policy": {\n      "id": "policy_123",\n      "dataset_id": "dataset_123",\n      "name": "restricted_access",\n      "type": "access",\n      "status": "active",\n      "created_at": "2024-03-14T12:00:00Z",\n      "updated_at": "2024-03-14T12:00:00Z",\n      "rules": [\n        {\n          "role": "data_scientist",\n          "permissions": ["read", "write"],\n          "conditions": {\n            "ip_range": ["10.0.0.0/24"],\n            "time_window": {\n              "start": "09:00",\n              "end": "17:00",\n              "timezone": "UTC"\n            }\n          }\n        }\n      ],\n      "metadata": {\n        "compliance": ["GDPR", "HIPAA"],\n        "data_classification": "sensitive",\n        "retention_period": "365d"\n      },\n      "audit_log": [\n        {\n          "timestamp": "2024-03-14T12:00:00Z",\n          "action": "create",\n          "user": "<EMAIL>"\n        }\n      ]\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"list-policies",children:"List Policies"}),"\n",(0,s.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/policies\n"})}),"\n",(0,s.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Parameter"}),(0,s.jsx)(n.th,{children:"Type"}),(0,s.jsx)(n.th,{children:"Description"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"page"}),(0,s.jsx)(n.td,{children:"integer"}),(0,s.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"limit"}),(0,s.jsx)(n.td,{children:"integer"}),(0,s.jsx)(n.td,{children:"Items per page (default: 10)"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"type"}),(0,s.jsx)(n.td,{children:"string"}),(0,s.jsx)(n.td,{children:"Filter by policy type"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"status"}),(0,s.jsx)(n.td,{children:"string"}),(0,s.jsx)(n.td,{children:"Filter by status"})]})]})]}),"\n",(0,s.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "policies": [\n      {\n        "id": "policy_123",\n        "dataset_id": "dataset_123",\n        "name": "restricted_access",\n        "type": "access",\n        "status": "active",\n        "created_at": "2024-03-14T12:00:00Z",\n        "metadata": {\n          "compliance": ["GDPR", "HIPAA"],\n          "data_classification": "sensitive"\n        }\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"policy-status",children:"Policy Status"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Status"}),(0,s.jsx)(n.th,{children:"Description"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"active"}),(0,s.jsx)(n.td,{children:"Policy is active"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"inactive"}),(0,s.jsx)(n.td,{children:"Policy is inactive"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"draft"}),(0,s.jsx)(n.td,{children:"Policy is in draft"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"archived"}),(0,s.jsx)(n.td,{children:"Policy is archived"})]})]})]}),"\n",(0,s.jsx)(n.h2,{id:"policy-types",children:"Policy Types"}),"\n",(0,s.jsx)(n.h3,{id:"access-policies",children:"Access Policies"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Role-based access control"}),"\n",(0,s.jsx)(n.li,{children:"IP-based restrictions"}),"\n",(0,s.jsx)(n.li,{children:"Time-based restrictions"}),"\n",(0,s.jsx)(n.li,{children:"Data masking rules"}),"\n",(0,s.jsx)(n.li,{children:"Encryption requirements"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"compliance-policies",children:"Compliance Policies"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Data retention rules"}),"\n",(0,s.jsx)(n.li,{children:"Privacy requirements"}),"\n",(0,s.jsx)(n.li,{children:"Regulatory compliance"}),"\n",(0,s.jsx)(n.li,{children:"Data classification"}),"\n",(0,s.jsx)(n.li,{children:"Audit requirements"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"security-policies",children:"Security Policies"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Encryption standards"}),"\n",(0,s.jsx)(n.li,{children:"Authentication requirements"}),"\n",(0,s.jsx)(n.li,{children:"Network security"}),"\n",(0,s.jsx)(n.li,{children:"Data protection"}),"\n",(0,s.jsx)(n.li,{children:"Incident response"}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,s.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create policy\npolicy = client.data.create_policy(\n    "dataset_123",\n    name="restricted_access",\n    type="access",\n    rules=[\n        {\n            "role": "data_scientist",\n            "permissions": ["read", "write"]\n        }\n    ]\n)\n\n# Get policy details\ndetails = client.data.get_policy(\n    "dataset_123",\n    "policy_123"\n)\n\n# List policies\npolicies = client.data.list_policies(\n    "dataset_123",\n    page=1,\n    limit=10\n)\n'})}),"\n",(0,s.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create policy\nconst policy = await client.data.createPolicy('dataset_123', {\n  name: 'restricted_access',\n  type: 'access',\n  rules: [\n    {\n      role: 'data_scientist',\n      permissions: ['read', 'write']\n    }\n  ]\n});\n\n// Get policy details\nconst details = await client.data.getPolicy(\n  'dataset_123',\n  'policy_123'\n);\n\n// List policies\nconst policies = await client.data.listPolicies('dataset_123', {\n  page: 1,\n  limit: 10\n});\n"})}),"\n",(0,s.jsx)(n.h2,{id:"governance-best-practices",children:"Governance Best Practices"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsx)(n.li,{children:"Define clear policies"}),"\n",(0,s.jsx)(n.li,{children:"Implement role-based access"}),"\n",(0,s.jsx)(n.li,{children:"Monitor compliance"}),"\n",(0,s.jsx)(n.li,{children:"Regular audits"}),"\n",(0,s.jsx)(n.li,{children:"Data classification"}),"\n",(0,s.jsx)(n.li,{children:"Security measures"}),"\n",(0,s.jsx)(n.li,{children:"Documentation"}),"\n",(0,s.jsx)(n.li,{children:"Training programs"}),"\n"]})]})}function p(e={}){const{wrapper:n}={...(0,a.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(o,{...e})}):o(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>c,x:()=>l});var t=i(6540);const s={},a=t.createContext(s);function c(e){const n=t.useContext(a);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:c(e.components),t.createElement(a.Provider,{value:n},e.children)}}}]);