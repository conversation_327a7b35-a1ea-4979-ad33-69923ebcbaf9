"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[5696],{479:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>l,contentTitle:()=>s,default:()=>h,frontMatter:()=>i,metadata:()=>a,toc:()=>c});const a=JSON.parse('{"id":"ai-architecture/api/data/upload","title":"Data Upload","description":"Upload and manage your training and inference data.","source":"@site/docs/ai-architecture/api/data/upload.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/upload","permalink":"/docs/ai-architecture/api/data/upload","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/upload.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"API Tokens","permalink":"/docs/ai-architecture/api/auth/tokens"},"next":{"title":"Data Management","permalink":"/docs/ai-architecture/api/data/management"}}');var d=n(4848),r=n(8453);const i={sidebar_position:1},s="Data Upload",l={},c=[{value:"Endpoint",id:"endpoint",level:2},{value:"Request Body",id:"request-body",level:2},{value:"Example Request",id:"example-request",level:2},{value:"Example Response",id:"example-response",level:2},{value:"Supported Data Types",id:"supported-data-types",level:2},{value:"Error Codes",id:"error-codes",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Best Practices",id:"best-practices",level:2},{value:"Storage Limits",id:"storage-limits",level:2}];function o(e){const t={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,r.R)(),...e.components};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(t.header,{children:(0,d.jsx)(t.h1,{id:"data-upload",children:"Data Upload"})}),"\n",(0,d.jsx)(t.p,{children:"Upload and manage your training and inference data."}),"\n",(0,d.jsx)(t.h2,{id:"endpoint",children:"Endpoint"}),"\n",(0,d.jsx)(t.pre,{children:(0,d.jsx)(t.code,{children:"POST /v1/data/upload\n"})}),"\n",(0,d.jsx)(t.h2,{id:"request-body",children:"Request Body"}),"\n",(0,d.jsx)(t.pre,{children:(0,d.jsx)(t.code,{className:"language-json",children:'{\n  "name": "training_dataset",\n  "type": "text",\n  "format": "json",\n  "description": "Training data for language model",\n  "metadata": {\n    "source": "customer_feedback",\n    "language": "en"\n  }\n}\n'})}),"\n",(0,d.jsx)(t.h2,{id:"example-request",children:"Example Request"}),"\n",(0,d.jsx)(t.pre,{children:(0,d.jsx)(t.code,{className:"language-bash",children:'curl -X POST \\\n     -H "Authorization: Bearer YOUR_API_KEY" \\\n     -H "Content-Type: application/json" \\\n     -d \'{\n       "name": "training_dataset",\n       "type": "text",\n       "format": "json",\n       "description": "Training data for language model"\n     }\' \\\n     "https://api.ai-platform.example.com/v1/data/upload"\n'})}),"\n",(0,d.jsx)(t.h2,{id:"example-response",children:"Example Response"}),"\n",(0,d.jsx)(t.pre,{children:(0,d.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "upload": {\n      "id": "upload_123",\n      "name": "training_dataset",\n      "type": "text",\n      "format": "json",\n      "status": "processing",\n      "created_at": "2024-03-14T12:00:00Z",\n      "upload_url": "https://storage.ai-platform.example.com/upload_123"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,d.jsx)(t.h2,{id:"supported-data-types",children:"Supported Data Types"}),"\n",(0,d.jsxs)(t.table,{children:[(0,d.jsx)(t.thead,{children:(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.th,{children:"Type"}),(0,d.jsx)(t.th,{children:"Description"}),(0,d.jsx)(t.th,{children:"Formats"})]})}),(0,d.jsxs)(t.tbody,{children:[(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"text"}),(0,d.jsx)(t.td,{children:"Text data"}),(0,d.jsx)(t.td,{children:"json, csv, txt"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"image"}),(0,d.jsx)(t.td,{children:"Image data"}),(0,d.jsx)(t.td,{children:"jpg, png, tiff"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"audio"}),(0,d.jsx)(t.td,{children:"Audio data"}),(0,d.jsx)(t.td,{children:"mp3, wav, flac"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"video"}),(0,d.jsx)(t.td,{children:"Video data"}),(0,d.jsx)(t.td,{children:"mp4, avi, mov"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"tabular"}),(0,d.jsx)(t.td,{children:"Tabular data"}),(0,d.jsx)(t.td,{children:"csv, parquet, excel"})]})]})]}),"\n",(0,d.jsx)(t.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,d.jsxs)(t.table,{children:[(0,d.jsx)(t.thead,{children:(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.th,{children:"Code"}),(0,d.jsx)(t.th,{children:"Description"})]})}),(0,d.jsxs)(t.tbody,{children:[(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"invalid_request"}),(0,d.jsx)(t.td,{children:"Invalid input parameters"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"invalid_format"}),(0,d.jsx)(t.td,{children:"Unsupported data format"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"quota_exceeded"}),(0,d.jsx)(t.td,{children:"Storage quota exceeded"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"rate_limit_exceeded"}),(0,d.jsx)(t.td,{children:"Too many requests"})]})]})]}),"\n",(0,d.jsx)(t.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,d.jsx)(t.h3,{id:"python",children:"Python"}),"\n",(0,d.jsx)(t.pre,{children:(0,d.jsx)(t.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Upload data\nupload = client.data.upload(\n    name="training_dataset",\n    type="text",\n    format="json",\n    description="Training data for language model"\n)\n'})}),"\n",(0,d.jsx)(t.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,d.jsx)(t.pre,{children:(0,d.jsx)(t.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Upload data\nconst upload = await client.data.upload({\n  name: 'training_dataset',\n  type: 'text',\n  format: 'json',\n  description: 'Training data for language model'\n});\n"})}),"\n",(0,d.jsx)(t.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,d.jsxs)(t.ol,{children:["\n",(0,d.jsx)(t.li,{children:"Validate data before uploading"}),"\n",(0,d.jsx)(t.li,{children:"Use appropriate data formats"}),"\n",(0,d.jsx)(t.li,{children:"Include meaningful metadata"}),"\n",(0,d.jsx)(t.li,{children:"Monitor upload progress"}),"\n",(0,d.jsx)(t.li,{children:"Handle errors gracefully"}),"\n"]}),"\n",(0,d.jsx)(t.h2,{id:"storage-limits",children:"Storage Limits"}),"\n",(0,d.jsxs)(t.table,{children:[(0,d.jsx)(t.thead,{children:(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.th,{children:"Plan"}),(0,d.jsx)(t.th,{children:"Storage Limit"})]})}),(0,d.jsxs)(t.tbody,{children:[(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"Standard"}),(0,d.jsx)(t.td,{children:"10 GB"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"Pro"}),(0,d.jsx)(t.td,{children:"100 GB"})]}),(0,d.jsxs)(t.tr,{children:[(0,d.jsx)(t.td,{children:"Enterprise"}),(0,d.jsx)(t.td,{children:"Custom"})]})]})]})]})}function h(e={}){const{wrapper:t}={...(0,r.R)(),...e.components};return t?(0,d.jsx)(t,{...e,children:(0,d.jsx)(o,{...e})}):o(e)}},8453:(e,t,n)=>{n.d(t,{R:()=>i,x:()=>s});var a=n(6540);const d={},r=a.createContext(d);function i(e){const t=a.useContext(r);return a.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function s(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(d):e.components||d:i(e.components),a.createElement(r.Provider,{value:t},e.children)}}}]);