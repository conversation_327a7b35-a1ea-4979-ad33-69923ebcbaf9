"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7810],{7319:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>l,contentTitle:()=>a,default:()=>p,frontMatter:()=>d,metadata:()=>r,toc:()=>o});const r=JSON.parse('{"id":"ai-architecture/api/monitoring/reports","title":"Monitoring Reports","description":"Generate and schedule monitoring reports.","source":"@site/docs/ai-architecture/api/monitoring/reports.md","sourceDirName":"ai-architecture/api/monitoring","slug":"/ai-architecture/api/monitoring/reports","permalink":"/docs/ai-architecture/api/monitoring/reports","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/monitoring/reports.md","tags":[],"version":"current","sidebarPosition":5,"frontMatter":{"sidebar_position":5},"sidebar":"tutorialSidebar","previous":{"title":"Monitoring Dashboards","permalink":"/docs/ai-architecture/api/monitoring/dashboards"},"next":{"title":"Implementation Guide","permalink":"/docs/ai-architecture/implementation/"}}');var i=t(4848),s=t(8453);const d={sidebar_position:5},a="Monitoring Reports",l={},o=[{value:"Create Report",id:"create-report",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Report",id:"get-report",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"List Reports",id:"list-reports",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Report Types",id:"report-types",level:2},{value:"Report Formats",id:"report-formats",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Report Best Practices",id:"report-best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,s.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"monitoring-reports",children:"Monitoring Reports"})}),"\n",(0,i.jsx)(n.p,{children:"Generate and schedule monitoring reports."}),"\n",(0,i.jsx)(n.h2,{id:"create-report",children:"Create Report"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"POST /v1/monitoring/reports\n"})}),"\n",(0,i.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "name": "Weekly Model Performance Report",\n  "description": "Weekly summary of model performance metrics",\n  "type": "scheduled",\n  "schedule": {\n    "frequency": "weekly",\n    "day": "monday",\n    "time": "09:00",\n    "timezone": "UTC"\n  },\n  "content": {\n    "sections": [\n      {\n        "title": "Model Performance",\n        "metrics": ["model_accuracy", "model_latency", "model_errors"],\n        "type": "summary",\n        "time_range": "7d"\n      },\n      {\n        "title": "Data Quality",\n        "metrics": ["data_quality", "data_freshness"],\n        "type": "trend",\n        "time_range": "7d"\n      },\n      {\n        "title": "System Health",\n        "metrics": ["cpu_usage", "memory_usage", "error_count"],\n        "type": "summary",\n        "time_range": "7d"\n      }\n    ],\n    "format": "pdf",\n    "include_charts": true,\n    "include_tables": true\n  },\n  "recipients": [\n    {\n      "type": "email",\n      "address": "<EMAIL>"\n    },\n    {\n      "type": "slack",\n      "channel": "#model-monitoring"\n    }\n  ]\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "report": {\n      "id": "report_123",\n      "name": "Weekly Model Performance Report",\n      "description": "Weekly summary of model performance metrics",\n      "type": "scheduled",\n      "schedule": {\n        "frequency": "weekly",\n        "day": "monday",\n        "time": "09:00",\n        "timezone": "UTC",\n        "next_run": "2024-03-18T09:00:00Z"\n      },\n      "content": {\n        "sections": [\n          {\n            "id": "section_123",\n            "title": "Model Performance",\n            "metrics": ["model_accuracy", "model_latency", "model_errors"],\n            "type": "summary",\n            "time_range": "7d"\n          }\n        ],\n        "format": "pdf",\n        "include_charts": true,\n        "include_tables": true\n      },\n      "recipients": [\n        {\n          "type": "email",\n          "address": "<EMAIL>"\n        }\n      ],\n      "status": "active",\n      "created_at": "2024-03-14T12:00:00Z",\n      "updated_at": "2024-03-14T12:00:00Z"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"get-report",children:"Get Report"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/monitoring/reports/{report_id}\n"})}),"\n",(0,i.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "report": {\n      "id": "report_123",\n      "name": "Weekly Model Performance Report",\n      "description": "Weekly summary of model performance metrics",\n      "type": "scheduled",\n      "schedule": {\n        "frequency": "weekly",\n        "day": "monday",\n        "time": "09:00",\n        "timezone": "UTC",\n        "next_run": "2024-03-18T09:00:00Z",\n        "last_run": "2024-03-11T09:00:00Z"\n      },\n      "content": {\n        "sections": [\n          {\n            "id": "section_123",\n            "title": "Model Performance",\n            "metrics": ["model_accuracy", "model_latency", "model_errors"],\n            "type": "summary",\n            "time_range": "7d",\n            "data": {\n              "summary": {\n                "model_accuracy": 95.5,\n                "model_latency": 150.5,\n                "model_errors": 10\n              }\n            }\n          }\n        ],\n        "format": "pdf",\n        "include_charts": true,\n        "include_tables": true\n      },\n      "recipients": [\n        {\n          "type": "email",\n          "address": "<EMAIL>"\n        }\n      ],\n      "status": "active",\n      "created_at": "2024-03-14T12:00:00Z",\n      "updated_at": "2024-03-14T12:00:00Z"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"list-reports",children:"List Reports"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/monitoring/reports\n"})}),"\n",(0,i.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Parameter"}),(0,i.jsx)(n.th,{children:"Type"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"page"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"limit"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Items per page (default: 100)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"type"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Report type"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"status"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Report status"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"search"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Search query"})]})]})]}),"\n",(0,i.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "reports": [\n      {\n        "id": "report_123",\n        "name": "Weekly Model Performance Report",\n        "type": "scheduled",\n        "status": "active",\n        "schedule": {\n          "frequency": "weekly",\n          "next_run": "2024-03-18T09:00:00Z"\n        },\n        "created_at": "2024-03-14T12:00:00Z",\n        "updated_at": "2024-03-14T12:00:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 100,\n      "page": 1,\n      "limit": 100,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"report-types",children:"Report Types"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Type"}),(0,i.jsx)(n.th,{children:"Description"}),(0,i.jsx)(n.th,{children:"Use Case"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"scheduled"}),(0,i.jsx)(n.td,{children:"Regular automated reports"}),(0,i.jsx)(n.td,{children:"Weekly/monthly summaries"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"on-demand"}),(0,i.jsx)(n.td,{children:"Generated when requested"}),(0,i.jsx)(n.td,{children:"Ad-hoc analysis"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"triggered"}),(0,i.jsx)(n.td,{children:"Generated on specific events"}),(0,i.jsx)(n.td,{children:"Alerts and notifications"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"report-formats",children:"Report Formats"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Format"}),(0,i.jsx)(n.th,{children:"Description"}),(0,i.jsx)(n.th,{children:"Use Case"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"pdf"}),(0,i.jsx)(n.td,{children:"Portable Document Format"}),(0,i.jsx)(n.td,{children:"Formal reports"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"html"}),(0,i.jsx)(n.td,{children:"Web page format"}),(0,i.jsx)(n.td,{children:"Interactive reports"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"csv"}),(0,i.jsx)(n.td,{children:"Comma-separated values"}),(0,i.jsx)(n.td,{children:"Data analysis"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"json"}),(0,i.jsx)(n.td,{children:"JavaScript Object Notation"}),(0,i.jsx)(n.td,{children:"API integration"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create report\nreport = client.monitoring.create_report(\n    name="Weekly Model Performance Report",\n    type="scheduled",\n    schedule={\n        "frequency": "weekly",\n        "day": "monday",\n        "time": "09:00",\n        "timezone": "UTC"\n    },\n    content={\n        "sections": [\n            {\n                "title": "Model Performance",\n                "metrics": ["model_accuracy", "model_latency"],\n                "type": "summary",\n                "time_range": "7d"\n            }\n        ],\n        "format": "pdf"\n    }\n)\n\n# Get report\nreport_details = client.monitoring.get_report("report_123")\n\n# List reports\nreports = client.monitoring.list_reports(\n    page=1,\n    limit=100,\n    type="scheduled"\n)\n'})}),"\n",(0,i.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create report\nconst report = await client.monitoring.createReport({\n  name: 'Weekly Model Performance Report',\n  type: 'scheduled',\n  schedule: {\n    frequency: 'weekly',\n    day: 'monday',\n    time: '09:00',\n    timezone: 'UTC'\n  },\n  content: {\n    sections: [\n      {\n        title: 'Model Performance',\n        metrics: ['model_accuracy', 'model_latency'],\n        type: 'summary',\n        timeRange: '7d'\n      }\n    ],\n    format: 'pdf'\n  }\n});\n\n// Get report\nconst reportDetails = await client.monitoring.getReport('report_123');\n\n// List reports\nconst reports = await client.monitoring.listReports({\n  page: 1,\n  limit: 100,\n  type: 'scheduled'\n});\n"})}),"\n",(0,i.jsx)(n.h2,{id:"report-best-practices",children:"Report Best Practices"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"Define clear objectives"}),"\n",(0,i.jsx)(n.li,{children:"Include relevant metrics"}),"\n",(0,i.jsx)(n.li,{children:"Set appropriate schedules"}),"\n",(0,i.jsx)(n.li,{children:"Choose right format"}),"\n",(0,i.jsx)(n.li,{children:"Add context and insights"}),"\n",(0,i.jsx)(n.li,{children:"Use visualizations"}),"\n",(0,i.jsx)(n.li,{children:"Include recommendations"}),"\n",(0,i.jsx)(n.li,{children:"Review and update regularly"}),"\n"]})]})}function p(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(c,{...e})}):c(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>d,x:()=>a});var r=t(6540);const i={},s=r.createContext(i);function d(e){const n=r.useContext(s);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:d(e.components),r.createElement(s.Provider,{value:n},e.children)}}}]);