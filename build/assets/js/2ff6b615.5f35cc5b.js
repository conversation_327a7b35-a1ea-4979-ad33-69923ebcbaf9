"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[489],{3471:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>c,default:()=>h,frontMatter:()=>d,metadata:()=>s,toc:()=>t});const s=JSON.parse('{"id":"ai-architecture/tools/old/api/api-endpoints","title":"MLOps Platform API Endpoints","description":"This document provides a comprehensive list of all available endpoints for the MLOps platform services.","source":"@site/docs/ai-architecture/tools/old/api/api-endpoints.md","sourceDirName":"ai-architecture/tools/old/api","slug":"/ai-architecture/tools/old/api/api-endpoints","permalink":"/docs/ai-architecture/tools/old/api/api-endpoints","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/api-endpoints.md","tags":[],"version":"current","frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"API Overview","permalink":"/docs/ai-architecture/tools/old/api/"},"next":{"title":"Data Management","permalink":"/docs/ai-architecture/tools/old/api/data-management/"}}');var r=i(4848),l=i(8453);const d={},c="MLOps Platform API Endpoints",o={},t=[{value:"PostgreSQL",id:"postgresql",level:2},{value:"Connection Details",id:"connection-details",level:3},{value:"MongoDB",id:"mongodb",level:2},{value:"Connection Details",id:"connection-details-1",level:3},{value:"MinIO",id:"minio",level:2},{value:"Authentication",id:"authentication",level:3},{value:"Endpoints",id:"endpoints",level:3},{value:"Common Operations",id:"common-operations",level:3},{value:"LakeFS",id:"lakefs",level:2},{value:"Authentication",id:"authentication-1",level:3},{value:"Endpoints",id:"endpoints-1",level:3},{value:"Trino",id:"trino",level:2},{value:"Authentication",id:"authentication-2",level:3},{value:"Endpoints",id:"endpoints-2",level:3},{value:"Example Queries",id:"example-queries",level:3},{value:"OpenMetadata",id:"openmetadata",level:2},{value:"Authentication",id:"authentication-3",level:3},{value:"Endpoints",id:"endpoints-3",level:3},{value:"Orchestration &amp; Integration",id:"orchestration--integration",level:2},{value:"3. Example API/Service Endpoints",id:"3-example-apiservice-endpoints",level:2},{value:"4. Security and Error Handling",id:"4-security-and-error-handling",level:2},{value:"Error Handling",id:"error-handling",level:2}];function a(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,l.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"mlops-platform-api-endpoints",children:"MLOps Platform API Endpoints"})}),"\n",(0,r.jsx)(n.p,{children:"This document provides a comprehensive list of all available endpoints for the MLOps platform services."}),"\n",(0,r.jsx)(n.h2,{id:"postgresql",children:"PostgreSQL"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Base URL"}),": ",(0,r.jsx)(n.code,{children:"postgresql:5432"})]}),"\n",(0,r.jsx)(n.h3,{id:"connection-details",children:"Connection Details"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Host: ",(0,r.jsx)(n.code,{children:"postgresql"})]}),"\n",(0,r.jsxs)(n.li,{children:["Port: ",(0,r.jsx)(n.code,{children:"5432"})]}),"\n",(0,r.jsxs)(n.li,{children:["Default Database: ",(0,r.jsx)(n.code,{children:"${POSTGRES_DB}"})]}),"\n",(0,r.jsxs)(n.li,{children:["Username: ",(0,r.jsx)(n.code,{children:"${POSTGRES_USER}"})]}),"\n",(0,r.jsxs)(n.li,{children:["Password: ",(0,r.jsx)(n.code,{children:"${POSTGRES_PASSWORD}"})]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"mongodb",children:"MongoDB"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Base URL"}),": ",(0,r.jsx)(n.code,{children:"mongodb:27017"})]}),"\n",(0,r.jsx)(n.h3,{id:"connection-details-1",children:"Connection Details"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Host: ",(0,r.jsx)(n.code,{children:"mongodb"})]}),"\n",(0,r.jsxs)(n.li,{children:["Port: ",(0,r.jsx)(n.code,{children:"27017"})]}),"\n",(0,r.jsxs)(n.li,{children:["Database: ",(0,r.jsx)(n.code,{children:"${MONGODB_DATABASE}"})]}),"\n",(0,r.jsxs)(n.li,{children:["Username: ",(0,r.jsx)(n.code,{children:"${MONGODB_USERNAME}"})]}),"\n",(0,r.jsxs)(n.li,{children:["Password: ",(0,r.jsx)(n.code,{children:"${MONGODB_PASSWORD}"})]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"minio",children:"MinIO"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Base URL"}),": ",(0,r.jsx)(n.code,{children:"http://minio:9000"})]}),"\n",(0,r.jsx)(n.h3,{id:"authentication",children:"Authentication"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Access Key: ",(0,r.jsx)(n.code,{children:"${MINIO_ACCESS_KEY}"})]}),"\n",(0,r.jsxs)(n.li,{children:["Secret Key: ",(0,r.jsx)(n.code,{children:"${MINIO_SECRET_KEY}"})]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"endpoints",children:"Endpoints"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Console: ",(0,r.jsx)(n.code,{children:"http://minio:9001"})]}),"\n",(0,r.jsxs)(n.li,{children:["API: ",(0,r.jsx)(n.code,{children:"http://minio:9000"})]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"common-operations",children:"Common Operations"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Create Bucket"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"PUT /{bucket-name}\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Upload Object"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"PUT /{bucket-name}/{object-name}\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Download Object"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /{bucket-name}/{object-name}\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"List Objects"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /{bucket-name}?prefix={prefix}&delimiter={delimiter}\n"})}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"lakefs",children:"LakeFS"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Base URL"}),": ",(0,r.jsx)(n.code,{children:"http://lakefs:8000"})]}),"\n",(0,r.jsx)(n.h3,{id:"authentication-1",children:"Authentication"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Access Key: ",(0,r.jsx)(n.code,{children:"${LAKEFS_ACCESS_KEY}"})]}),"\n",(0,r.jsxs)(n.li,{children:["Secret Key: ",(0,r.jsx)(n.code,{children:"${LAKEFS_SECRET_KEY}"})]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"endpoints-1",children:"Endpoints"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Create Repository"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"POST /repositories\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"List Repositories"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /repositories\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Create Branch"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"POST /repositories/{repository}/branches\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"List Objects"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /repositories/{repository}/refs/{ref}/objects/ls\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Upload Object"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"PUT /repositories/{repository}/refs/{ref}/objects\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Download Object"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /repositories/{repository}/refs/{ref}/objects\n"})}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"trino",children:"Trino"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Base URL"}),": ",(0,r.jsx)(n.code,{children:"http://trino:8080"})]}),"\n",(0,r.jsx)(n.h3,{id:"authentication-2",children:"Authentication"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Username: ",(0,r.jsx)(n.code,{children:"${TRINO_USER}"})]}),"\n",(0,r.jsxs)(n.li,{children:["Password: ",(0,r.jsx)(n.code,{children:"${TRINO_PASSWORD}"})]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"endpoints-2",children:"Endpoints"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Execute Query"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"POST /v1/statement\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Get Query Status"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /v1/query/{queryId}\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Cancel Query"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"DELETE /v1/query/{queryId}\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Get Query Results"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /v1/query/{queryId}/results\n"})}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"example-queries",children:"Example Queries"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Query PostgreSQL"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-sql",children:"SELECT * FROM postgresql.public.your_table;\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Query MongoDB"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-sql",children:"SELECT * FROM mongodb.your_database.your_collection;\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Query MinIO (via LakeFS)"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-sql",children:"SELECT * FROM lakefs.your_repository.your_branch.your_table;\n"})}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"openmetadata",children:"OpenMetadata"}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Base URL"}),": ",(0,r.jsx)(n.code,{children:"http://openmetadata:8585"})]}),"\n",(0,r.jsx)(n.h3,{id:"authentication-3",children:"Authentication"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Username: ",(0,r.jsx)(n.code,{children:"${OPENMETADATA_USER}"})]}),"\n",(0,r.jsxs)(n.li,{children:["Password: ",(0,r.jsx)(n.code,{children:"${OPENMETADATA_PASSWORD}"})]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"endpoints-3",children:"Endpoints"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Create Service"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"POST /api/v1/services\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"List Services"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /api/v1/services\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Create Pipeline"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"POST /api/v1/pipelines\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"List Pipelines"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /api/v1/pipelines\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Create Dashboard"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"POST /api/v1/dashboards\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"List Dashboards"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /api/v1/dashboards\n"})}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"orchestration--integration",children:"Orchestration & Integration"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Airflow"}),": Orchestrates ETL and ML pipelines, triggers Trino queries, manages data movement, and triggers Kubeflow pipelines."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Trino"}),": Performs distributed SQL queries on data in MinIO, LakeFS, PostgreSQL, and MongoDB."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"LakeFS"}),": Provides version control for all ingested and processed data."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"MinIO"}),": Stores raw, processed, and model artifact data."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Feature Store"}),": Manages and versions features for ML pipelines (integrated with LakeFS/MinIO)."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Kubeflow"}),": Runs ML pipelines, reads versioned data/features, logs experiments to MLflow, and deploys models via KServe."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"MLflow"}),": Tracks experiments, metrics, and manages model registry."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"OpenMetadata"}),": Catalogs all data, features, and models, and tracks lineage."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"KServe"}),": Serves models for inference and sends metrics to monitoring."]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Monitoring/Alerting"}),": Receives metrics and alerts from all major components."]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"3-example-apiservice-endpoints",children:"3. Example API/Service Endpoints"}),"\n",(0,r.jsx)(n.p,{children:'Keep your existing endpoint documentation, but consider grouping them by flow (e.g., "Data Ingestion APIs", "Feature Store APIs", "ML Pipeline APIs", "Model Serving APIs", etc.) for clarity.'}),"\n",(0,r.jsx)(n.h2,{id:"4-security-and-error-handling",children:"4. Security and Error Handling"}),"\n",(0,r.jsx)(n.p,{children:"Keep your security and error handling notes as they are\u2014they are already professional and comprehensive."}),"\n",(0,r.jsx)(n.h2,{id:"error-handling",children:"Error Handling"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"All endpoints return appropriate HTTP status codes"}),"\n",(0,r.jsx)(n.li,{children:"Error responses include detailed messages"}),"\n",(0,r.jsx)(n.li,{children:"Implement proper retry mechanisms for transient failures"}),"\n",(0,r.jsx)(n.li,{children:"Log all API errors for monitoring and debugging"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(a,{...e})}):a(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>d,x:()=>c});var s=i(6540);const r={},l=s.createContext(r);function d(e){const n=s.useContext(l);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function c(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:d(e.components),s.createElement(l.Provider,{value:n},e.children)}}}]);