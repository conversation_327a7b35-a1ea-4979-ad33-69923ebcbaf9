"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[5632],{5929:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>a,contentTitle:()=>o,default:()=>u,frontMatter:()=>l,metadata:()=>s,toc:()=>d});const s=JSON.parse('{"id":"adrs/platform/go-libraries","title":"4. Go Libraries and Components","description":"Date: 2024-03-19","source":"@site/docs/adrs/platform/0008-go-libraries.md","sourceDirName":"adrs/platform","slug":"/adrs/platform/go-libraries","permalink":"/docs/adrs/platform/go-libraries","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/platform/0008-go-libraries.md","tags":[],"version":"current","sidebarPosition":8,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"7. Vite as the Build Tool for React applications","permalink":"/docs/adrs/platform/vite"},"next":{"title":"5. Database Communication in Go","permalink":"/docs/adrs/platform/go-database-communication"}}');var r=i(4848),t=i(8453);const l={},o="4. Go Libraries and Components",a={},d=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2},{value:"Benefits",id:"benefits",level:3},{value:"Risks &amp; Mitigation",id:"risks--mitigation",level:3},{value:"Implementation Notes",id:"implementation-notes",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"4-go-libraries-and-components",children:"4. Go Libraries and Components"})}),"\n",(0,r.jsx)(n.p,{children:"Date: 2024-03-19"}),"\n",(0,r.jsx)(n.h2,{id:"status",children:"Status"}),"\n",(0,r.jsx)(n.p,{children:"Proposed"}),"\n",(0,r.jsx)(n.h2,{id:"context",children:"Context"}),"\n",(0,r.jsx)(n.p,{children:"Our Go services need standardized libraries for logging, dependency injection, and configuration management to ensure consistency and maintainability."}),"\n",(0,r.jsx)(n.h2,{id:"decision",children:"Decision"}),"\n",(0,r.jsx)(n.p,{children:"We will use:"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Logging"}),": ",(0,r.jsx)(n.code,{children:"uber-go/zap"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"High-performance structured logging"}),"\n",(0,r.jsx)(n.li,{children:"Built-in log levels and structured fields"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Dependency Injection"}),": ",(0,r.jsx)(n.code,{children:"uber-go/fx"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Modular application design"}),"\n",(0,r.jsx)(n.li,{children:"Lifecycle management"}),"\n",(0,r.jsx)(n.li,{children:"Testing support"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Configuration"}),": ",(0,r.jsx)(n.code,{children:"caarlos0/env/v11"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Type-safe environment variables"}),"\n",(0,r.jsx)(n.li,{children:"Custom types and validation"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"consequences",children:"Consequences"}),"\n",(0,r.jsx)(n.h3,{id:"benefits",children:"Benefits"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Consistent libraries across services"}),"\n",(0,r.jsx)(n.li,{children:"High performance where needed"}),"\n",(0,r.jsx)(n.li,{children:"Strong community support"}),"\n",(0,r.jsx)(n.li,{children:"Well-documented components"}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"risks--mitigation",children:"Risks & Mitigation"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Version management: Pin versions and regular updates"}),"\n",(0,r.jsx)(n.li,{children:"Learning curve: Provide documentation and training"}),"\n",(0,r.jsx)(n.li,{children:"Dependencies: Regular audits and updates"}),"\n",(0,r.jsx)(n.li,{children:"Migration: Gradual approach with documentation"}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"implementation-notes",children:"Implementation Notes"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsx)(n.li,{children:"Use in all new services"}),"\n",(0,r.jsx)(n.li,{children:"Gradual migration for existing services"}),"\n",(0,r.jsx)(n.li,{children:"Version pinning in go.mod"}),"\n",(0,r.jsx)(n.li,{children:"Regular security audits"}),"\n",(0,r.jsx)(n.li,{children:"Maintain usage documentation"}),"\n"]})]})}function u(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(c,{...e})}):c(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>o});var s=i(6540);const r={},t=s.createContext(r);function l(e){const n=s.useContext(t);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function o(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:l(e.components),s.createElement(t.Provider,{value:n},e.children)}}}]);