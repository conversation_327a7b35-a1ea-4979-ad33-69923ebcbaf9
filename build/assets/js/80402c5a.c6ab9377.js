"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[1336],{5361:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>d,contentTitle:()=>a,default:()=>h,frontMatter:()=>s,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/api/monitoring/alerts","title":"Monitoring Alerts","description":"Configure and manage monitoring alerts for your AI models and data.","source":"@site/docs/ai-architecture/api/monitoring/alerts.md","sourceDirName":"ai-architecture/api/monitoring","slug":"/ai-architecture/api/monitoring/alerts","permalink":"/docs/ai-architecture/api/monitoring/alerts","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/monitoring/alerts.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"Model Predictions","permalink":"/docs/ai-architecture/api/models/predictions"},"next":{"title":"Monitoring Logs","permalink":"/docs/ai-architecture/api/monitoring/logs"}}');var i=t(4848),l=t(8453);const s={sidebar_position:1},a="Monitoring Alerts",d={},c=[{value:"Create Alert",id:"create-alert",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"List Alerts",id:"list-alerts",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Get Alert Details",id:"get-alert-details",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Alert Types",id:"alert-types",level:2},{value:"Alert Status",id:"alert-status",level:2},{value:"Notification Channels",id:"notification-channels",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Alert Best Practices",id:"alert-best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,l.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"monitoring-alerts",children:"Monitoring Alerts"})}),"\n",(0,i.jsx)(n.p,{children:"Configure and manage monitoring alerts for your AI models and data."}),"\n",(0,i.jsx)(n.h2,{id:"create-alert",children:"Create Alert"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"POST /v1/monitoring/alerts\n"})}),"\n",(0,i.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "name": "model_performance_alert",\n  "description": "Alert on model performance degradation",\n  "type": "model",\n  "model_id": "model_123",\n  "conditions": {\n    "metric": "accuracy",\n    "operator": "below",\n    "threshold": 0.95,\n    "window": "1h",\n    "min_samples": 100\n  },\n  "notifications": {\n    "channels": [\n      {\n        "type": "email",\n        "recipients": ["<EMAIL>"]\n      },\n      {\n        "type": "slack",\n        "webhook": "https://hooks.slack.com/services/xxx"\n      }\n    ],\n    "cooldown": "1h"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "alert": {\n      "id": "alert_123",\n      "name": "model_performance_alert",\n      "type": "model",\n      "status": "active",\n      "created_at": "2024-03-14T12:00:00Z",\n      "conditions": {\n        "metric": "accuracy",\n        "operator": "below",\n        "threshold": 0.95\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"list-alerts",children:"List Alerts"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/monitoring/alerts\n"})}),"\n",(0,i.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Parameter"}),(0,i.jsx)(n.th,{children:"Type"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"page"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"limit"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Items per page (default: 10)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"type"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Filter by alert type"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"status"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Filter by status"})]})]})]}),"\n",(0,i.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "alerts": [\n      {\n        "id": "alert_123",\n        "name": "model_performance_alert",\n        "type": "model",\n        "status": "active",\n        "created_at": "2024-03-14T12:00:00Z",\n        "last_triggered": "2024-03-14T11:30:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"get-alert-details",children:"Get Alert Details"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/monitoring/alerts/{alert_id}\n"})}),"\n",(0,i.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "alert": {\n      "id": "alert_123",\n      "name": "model_performance_alert",\n      "type": "model",\n      "status": "active",\n      "created_at": "2024-03-14T12:00:00Z",\n      "conditions": {\n        "metric": "accuracy",\n        "operator": "below",\n        "threshold": 0.95,\n        "window": "1h",\n        "min_samples": 100\n      },\n      "notifications": {\n        "channels": [\n          {\n            "type": "email",\n            "recipients": ["<EMAIL>"]\n          }\n        ],\n        "cooldown": "1h"\n      },\n      "history": [\n        {\n          "timestamp": "2024-03-14T11:30:00Z",\n          "status": "triggered",\n          "value": 0.94,\n          "threshold": 0.95\n        }\n      ]\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"alert-types",children:"Alert Types"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Type"}),(0,i.jsx)(n.th,{children:"Description"}),(0,i.jsx)(n.th,{children:"Metrics"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"model"}),(0,i.jsx)(n.td,{children:"Model performance alerts"}),(0,i.jsx)(n.td,{children:"accuracy, latency, throughput"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"data"}),(0,i.jsx)(n.td,{children:"Data quality alerts"}),(0,i.jsx)(n.td,{children:"drift, anomalies, completeness"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"system"}),(0,i.jsx)(n.td,{children:"System health alerts"}),(0,i.jsx)(n.td,{children:"CPU, memory, disk usage"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"custom"}),(0,i.jsx)(n.td,{children:"Custom metric alerts"}),(0,i.jsx)(n.td,{children:"User-defined metrics"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"alert-status",children:"Alert Status"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Status"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"active"}),(0,i.jsx)(n.td,{children:"Alert is active"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"paused"}),(0,i.jsx)(n.td,{children:"Alert is paused"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"inactive"}),(0,i.jsx)(n.td,{children:"Alert is inactive"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"error"}),(0,i.jsx)(n.td,{children:"Alert has errors"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"notification-channels",children:"Notification Channels"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Channel"}),(0,i.jsx)(n.th,{children:"Description"}),(0,i.jsx)(n.th,{children:"Configuration"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"email"}),(0,i.jsx)(n.td,{children:"Email notifications"}),(0,i.jsx)(n.td,{children:"Recipients list"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"slack"}),(0,i.jsx)(n.td,{children:"Slack notifications"}),(0,i.jsx)(n.td,{children:"Webhook URL"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"webhook"}),(0,i.jsx)(n.td,{children:"Custom webhook"}),(0,i.jsx)(n.td,{children:"Webhook URL and headers"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"pagerduty"}),(0,i.jsx)(n.td,{children:"PagerDuty integration"}),(0,i.jsx)(n.td,{children:"Service key"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create alert\nalert = client.monitoring.create_alert(\n    name="model_performance_alert",\n    type="model",\n    model_id="model_123",\n    conditions={\n        "metric": "accuracy",\n        "operator": "below",\n        "threshold": 0.95\n    }\n)\n\n# List alerts\nalerts = client.monitoring.list_alerts(\n    page=1,\n    limit=10\n)\n\n# Get alert details\ndetails = client.monitoring.get_alert("alert_123")\n'})}),"\n",(0,i.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create alert\nconst alert = await client.monitoring.createAlert({\n  name: 'model_performance_alert',\n  type: 'model',\n  modelId: 'model_123',\n  conditions: {\n    metric: 'accuracy',\n    operator: 'below',\n    threshold: 0.95\n  }\n});\n\n// List alerts\nconst alerts = await client.monitoring.listAlerts({\n  page: 1,\n  limit: 10\n});\n\n// Get alert details\nconst details = await client.monitoring.getAlert('alert_123');\n"})}),"\n",(0,i.jsx)(n.h2,{id:"alert-best-practices",children:"Alert Best Practices"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"Set appropriate thresholds"}),"\n",(0,i.jsx)(n.li,{children:"Use meaningful alert names"}),"\n",(0,i.jsx)(n.li,{children:"Configure notification channels"}),"\n",(0,i.jsx)(n.li,{children:"Implement alert cooldowns"}),"\n",(0,i.jsx)(n.li,{children:"Monitor alert history"}),"\n",(0,i.jsx)(n.li,{children:"Regular alert reviews"}),"\n",(0,i.jsx)(n.li,{children:"Document alert policies"}),"\n",(0,i.jsx)(n.li,{children:"Test alert configurations"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(o,{...e})}):o(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>s,x:()=>a});var r=t(6540);const i={},l=r.createContext(i);function s(e){const n=r.useContext(l);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:s(e.components),r.createElement(l.Provider,{value:n},e.children)}}}]);