"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8209],{1248:(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});a(6540);var s=a(4164),r=a(539);const l=()=>(0,r.T)({id:"theme.tags.tagsPageTitle",message:"Tags",description:"The title of the tag list page"});var n=a(4737),c=a(5279),i=a(569),g=a(3953),o=a(9303);const u={tag:"tag_Nnez"};var h=a(4848);function d({letterEntry:e}){return(0,h.jsxs)("article",{children:[(0,h.jsx)(o.A,{as:"h2",id:e.letter,children:e.letter}),(0,h.jsx)("ul",{className:"padding--none",children:e.tags.map((e=>(0,h.jsx)("li",{className:u.tag,children:(0,h.jsx)(g.A,{...e})},e.permalink)))}),(0,h.jsx)("hr",{})]})}function p({tags:e}){const t=function(e){const t={};return Object.values(e).forEach((e=>{const a=function(e){return e[0].toUpperCase()}(e.label);t[a]??=[],t[a].push(e)})),Object.entries(t).sort((([e],[t])=>e.localeCompare(t))).map((([e,t])=>({letter:e,tags:t.sort(((e,t)=>e.label.localeCompare(t.label)))})))}(e);return(0,h.jsx)("section",{className:"margin-vert--lg",children:t.map((e=>(0,h.jsx)(d,{letterEntry:e},e.letter)))})}var j=a(7220);function m({tags:e,sidebar:t}){const a=l();return(0,h.jsxs)(n.e3,{className:(0,s.A)(c.G.wrapper.blogPages,c.G.page.blogTagsListPage),children:[(0,h.jsx)(n.be,{title:a}),(0,h.jsx)(j.A,{tag:"blog_tags_list"}),(0,h.jsxs)(i.A,{sidebar:t,children:[(0,h.jsx)(o.A,{as:"h1",children:a}),(0,h.jsx)(p,{tags:e})]})]})}},3953:(e,t,a)=>{a.d(t,{A:()=>c});a(6540);var s=a(4164),r=a(6289);const l={tag:"tag_zVej",tagRegular:"tagRegular_sFm0",tagWithCount:"tagWithCount_h2kH"};var n=a(4848);function c({permalink:e,label:t,count:a,description:c}){return(0,n.jsxs)(r.A,{href:e,title:c,className:(0,s.A)(l.tag,a?l.tagWithCount:l.tagRegular),children:[t,a&&(0,n.jsx)("span",{children:a})]})}}}]);