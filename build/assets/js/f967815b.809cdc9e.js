"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[9921],{5878:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>a,contentTitle:()=>d,default:()=>h,frontMatter:()=>l,metadata:()=>s,toc:()=>c});const s=JSON.parse('{"id":"ai-architecture/api/models/list-models","title":"List Models","description":"Retrieve a list of all available AI models in your account.","source":"@site/docs/ai-architecture/api/models/list-models.md","sourceDirName":"ai-architecture/api/models","slug":"/ai-architecture/api/models/list-models","permalink":"/docs/ai-architecture/api/models/list-models","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/models/list-models.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"Data Governance","permalink":"/docs/ai-architecture/api/data/governance"},"next":{"title":"Model Details","permalink":"/docs/ai-architecture/api/models/model-details"}}');var i=n(4848),r=n(8453);const l={sidebar_position:1},d="List Models",a={},c=[{value:"Endpoint",id:"endpoint",level:2},{value:"Query Parameters",id:"query-parameters",level:2},{value:"Example Request",id:"example-request",level:2},{value:"Example Response",id:"example-response",level:2},{value:"Error Codes",id:"error-codes",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3}];function o(e){const t={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,r.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(t.header,{children:(0,i.jsx)(t.h1,{id:"list-models",children:"List Models"})}),"\n",(0,i.jsx)(t.p,{children:"Retrieve a list of all available AI models in your account."}),"\n",(0,i.jsx)(t.h2,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{children:"GET /v1/models\n"})}),"\n",(0,i.jsx)(t.h2,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,i.jsxs)(t.table,{children:[(0,i.jsx)(t.thead,{children:(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.th,{children:"Parameter"}),(0,i.jsx)(t.th,{children:"Type"}),(0,i.jsx)(t.th,{children:"Required"}),(0,i.jsx)(t.th,{children:"Description"})]})}),(0,i.jsxs)(t.tbody,{children:[(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"page"}),(0,i.jsx)(t.td,{children:"integer"}),(0,i.jsx)(t.td,{children:"No"}),(0,i.jsx)(t.td,{children:"Page number (default: 1)"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"limit"}),(0,i.jsx)(t.td,{children:"integer"}),(0,i.jsx)(t.td,{children:"No"}),(0,i.jsx)(t.td,{children:"Items per page (default: 10)"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"type"}),(0,i.jsx)(t.td,{children:"string"}),(0,i.jsx)(t.td,{children:"No"}),(0,i.jsx)(t.td,{children:"Filter by model type"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"status"}),(0,i.jsx)(t.td,{children:"string"}),(0,i.jsx)(t.td,{children:"No"}),(0,i.jsx)(t.td,{children:"Filter by model status"})]})]})]}),"\n",(0,i.jsx)(t.h2,{id:"example-request",children:"Example Request"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{className:"language-bash",children:'curl -H "Authorization: Bearer YOUR_API_KEY" \\\n     "https://api.ai-platform.example.com/v1/models?page=1&limit=10"\n'})}),"\n",(0,i.jsx)(t.h2,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "models": [\n      {\n        "id": "model_123",\n        "name": "GPT-4",\n        "type": "language",\n        "status": "active",\n        "created_at": "2024-03-14T12:00:00Z",\n        "updated_at": "2024-03-14T12:00:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 100,\n      "page": 1,\n      "limit": 10,\n      "pages": 10\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(t.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,i.jsxs)(t.table,{children:[(0,i.jsx)(t.thead,{children:(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.th,{children:"Code"}),(0,i.jsx)(t.th,{children:"Description"})]})}),(0,i.jsxs)(t.tbody,{children:[(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"invalid_request"}),(0,i.jsx)(t.td,{children:"Invalid query parameters"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"unauthorized"}),(0,i.jsx)(t.td,{children:"Invalid or missing API key"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"rate_limit_exceeded"}),(0,i.jsx)(t.td,{children:"Too many requests"})]})]})]}),"\n",(0,i.jsx)(t.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(t.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# List all models\nmodels = client.models.list(\n    page=1,\n    limit=10,\n    type="language",\n    status="active"\n)\n'})}),"\n",(0,i.jsx)(t.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// List all models\nconst models = await client.models.list({\n  page: 1,\n  limit: 10,\n  type: 'language',\n  status: 'active'\n});\n"})})]})}function h(e={}){const{wrapper:t}={...(0,r.R)(),...e.components};return t?(0,i.jsx)(t,{...e,children:(0,i.jsx)(o,{...e})}):o(e)}},8453:(e,t,n)=>{n.d(t,{R:()=>l,x:()=>d});var s=n(6540);const i={},r=s.createContext(i);function l(e){const t=s.useContext(r);return s.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function d(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:l(e.components),s.createElement(r.Provider,{value:t},e.children)}}}]);