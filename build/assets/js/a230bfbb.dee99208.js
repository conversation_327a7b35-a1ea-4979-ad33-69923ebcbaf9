"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[1255],{8453:(e,n,s)=>{s.d(n,{R:()=>a,x:()=>p});var t=s(6540);const i={},r=t.createContext(i);function a(e){const n=t.useContext(r);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function p(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:a(e.components),t.createElement(r.Provider,{value:n},e.children)}},8581:(e,n,s)=>{s.r(n),s.d(n,{assets:()=>l,contentTitle:()=>p,default:()=>o,frontMatter:()=>a,metadata:()=>t,toc:()=>d});const t=JSON.parse('{"id":"ai-architecture/api/data/preprocessing","title":"Data Preprocessing","description":"Transform and prepare your data for model training and inference.","source":"@site/docs/ai-architecture/api/data/preprocessing.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/preprocessing","permalink":"/docs/ai-architecture/api/data/preprocessing","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/preprocessing.md","tags":[],"version":"current","sidebarPosition":5,"frontMatter":{"sidebar_position":5},"sidebar":"tutorialSidebar","previous":{"title":"Data Validation","permalink":"/docs/ai-architecture/api/data/validation"},"next":{"title":"Data Augmentation","permalink":"/docs/ai-architecture/api/data/augmentation"}}');var i=s(4848),r=s(8453);const a={sidebar_position:5},p="Data Preprocessing",l={},d=[{value:"Create Preprocessing Pipeline",id:"create-preprocessing-pipeline",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Run Preprocessing",id:"run-preprocessing",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Request Body",id:"request-body-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Get Preprocessing Status",id:"get-preprocessing-status",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"List Preprocessing Pipelines",id:"list-preprocessing-pipelines",level:2},{value:"Endpoint",id:"endpoint-3",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-3",level:3},{value:"Preprocessing Status",id:"preprocessing-status",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Preprocessing Best Practices",id:"preprocessing-best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,r.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"data-preprocessing",children:"Data Preprocessing"})}),"\n",(0,i.jsx)(n.p,{children:"Transform and prepare your data for model training and inference."}),"\n",(0,i.jsx)(n.h2,{id:"create-preprocessing-pipeline",children:"Create Preprocessing Pipeline"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"POST /v1/data/datasets/{dataset_id}/preprocessing\n"})}),"\n",(0,i.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "name": "text_preprocessing",\n  "description": "Standard text preprocessing pipeline",\n  "steps": [\n    {\n      "name": "text_cleaning",\n      "type": "text_clean",\n      "params": {\n        "remove_urls": true,\n        "remove_emails": true,\n        "remove_numbers": false,\n        "remove_special_chars": true\n      }\n    },\n    {\n      "name": "tokenization",\n      "type": "tokenize",\n      "params": {\n        "method": "word",\n        "lowercase": true,\n        "remove_stopwords": true,\n        "language": "en"\n      }\n    },\n    {\n      "name": "normalization",\n      "type": "normalize",\n      "params": {\n        "method": "lemmatization",\n        "language": "en"\n      }\n    }\n  ],\n  "options": {\n    "save_intermediate": true,\n    "parallel_processing": true\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "pipeline": {\n      "id": "pipeline_123",\n      "name": "text_preprocessing",\n      "dataset_id": "dataset_123",\n      "status": "created",\n      "created_at": "2024-03-14T12:00:00Z",\n      "steps": [\n        {\n          "name": "text_cleaning",\n          "type": "text_clean",\n          "params": {\n            "remove_urls": true,\n            "remove_emails": true\n          }\n        }\n      ]\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"run-preprocessing",children:"Run Preprocessing"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"POST /v1/data/datasets/{dataset_id}/preprocessing/{pipeline_id}/run\n"})}),"\n",(0,i.jsx)(n.h3,{id:"request-body-1",children:"Request Body"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "options": {\n    "batch_size": 1000,\n    "max_workers": 4,\n    "save_checkpoints": true\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "preprocessing": {\n      "id": "preprocessing_123",\n      "pipeline_id": "pipeline_123",\n      "dataset_id": "dataset_123",\n      "status": "processing",\n      "created_at": "2024-03-14T12:00:00Z",\n      "options": {\n        "batch_size": 1000,\n        "max_workers": 4\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"get-preprocessing-status",children:"Get Preprocessing Status"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/preprocessing/{preprocessing_id}\n"})}),"\n",(0,i.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "preprocessing": {\n      "id": "preprocessing_123",\n      "pipeline_id": "pipeline_123",\n      "dataset_id": "dataset_123",\n      "status": "completed",\n      "created_at": "2024-03-14T12:00:00Z",\n      "completed_at": "2024-03-14T12:05:00Z",\n      "progress": {\n        "total_samples": 10000,\n        "processed_samples": 10000,\n        "percentage": 100\n      },\n      "statistics": {\n        "text_cleaning": {\n          "urls_removed": 500,\n          "emails_removed": 200,\n          "special_chars_removed": 1000\n        },\n        "tokenization": {\n          "average_tokens": 15,\n          "total_tokens": 150000\n        },\n        "normalization": {\n          "unique_lemmas": 5000\n        }\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:05:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"list-preprocessing-pipelines",children:"List Preprocessing Pipelines"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-3",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/preprocessing\n"})}),"\n",(0,i.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Parameter"}),(0,i.jsx)(n.th,{children:"Type"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"page"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"limit"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Items per page (default: 10)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"status"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Filter by status"})]})]})]}),"\n",(0,i.jsx)(n.h3,{id:"example-response-3",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "pipelines": [\n      {\n        "id": "pipeline_123",\n        "name": "text_preprocessing",\n        "dataset_id": "dataset_123",\n        "status": "active",\n        "created_at": "2024-03-14T12:00:00Z",\n        "last_run": "2024-03-14T12:05:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:05:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"preprocessing-status",children:"Preprocessing Status"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Status"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"created"}),(0,i.jsx)(n.td,{children:"Pipeline created"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"processing"}),(0,i.jsx)(n.td,{children:"Preprocessing running"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"completed"}),(0,i.jsx)(n.td,{children:"Preprocessing completed"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"failed"}),(0,i.jsx)(n.td,{children:"Preprocessing failed"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"cancelled"}),(0,i.jsx)(n.td,{children:"Preprocessing cancelled"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create preprocessing pipeline\npipeline = client.data.create_preprocessing_pipeline(\n    "dataset_123",\n    name="text_preprocessing",\n    steps=[\n        {\n            "name": "text_cleaning",\n            "type": "text_clean",\n            "params": {\n                "remove_urls": True,\n                "remove_emails": True\n            }\n        }\n    ]\n)\n\n# Run preprocessing\npreprocessing = client.data.run_preprocessing(\n    "dataset_123",\n    "pipeline_123",\n    options={\n        "batch_size": 1000,\n        "max_workers": 4\n    }\n)\n\n# Get preprocessing status\nstatus = client.data.get_preprocessing_status(\n    "dataset_123",\n    "preprocessing_123"\n)\n\n# List pipelines\npipelines = client.data.list_preprocessing_pipelines(\n    "dataset_123",\n    page=1,\n    limit=10\n)\n'})}),"\n",(0,i.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create preprocessing pipeline\nconst pipeline = await client.data.createPreprocessingPipeline('dataset_123', {\n  name: 'text_preprocessing',\n  steps: [\n    {\n      name: 'text_cleaning',\n      type: 'text_clean',\n      params: {\n        removeUrls: true,\n        removeEmails: true\n      }\n    }\n  ]\n});\n\n// Run preprocessing\nconst preprocessing = await client.data.runPreprocessing(\n  'dataset_123',\n  'pipeline_123',\n  {\n    options: {\n      batchSize: 1000,\n      maxWorkers: 4\n    }\n  }\n);\n\n// Get preprocessing status\nconst status = await client.data.getPreprocessingStatus(\n  'dataset_123',\n  'preprocessing_123'\n);\n\n// List pipelines\nconst pipelines = await client.data.listPreprocessingPipelines('dataset_123', {\n  page: 1,\n  limit: 10\n});\n"})}),"\n",(0,i.jsx)(n.h2,{id:"preprocessing-best-practices",children:"Preprocessing Best Practices"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"Define clear preprocessing steps"}),"\n",(0,i.jsx)(n.li,{children:"Use appropriate parameters"}),"\n",(0,i.jsx)(n.li,{children:"Monitor preprocessing progress"}),"\n",(0,i.jsx)(n.li,{children:"Save intermediate results"}),"\n",(0,i.jsx)(n.li,{children:"Implement error handling"}),"\n",(0,i.jsx)(n.li,{children:"Use parallel processing"}),"\n",(0,i.jsx)(n.li,{children:"Validate output quality"}),"\n",(0,i.jsx)(n.li,{children:"Document preprocessing steps"}),"\n"]})]})}function o(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(c,{...e})}):c(e)}}}]);