"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8285],{7386:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>l,contentTitle:()=>d,default:()=>p,frontMatter:()=>r,metadata:()=>a,toc:()=>o});const a=JSON.parse('{"id":"ai-architecture/api/data/augmentation","title":"Data Augmentation","description":"Enhance your datasets through various augmentation techniques.","source":"@site/docs/ai-architecture/api/data/augmentation.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/augmentation","permalink":"/docs/ai-architecture/api/data/augmentation","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/augmentation.md","tags":[],"version":"current","sidebarPosition":6,"frontMatter":{"sidebar_position":6},"sidebar":"tutorialSidebar","previous":{"title":"Data Preprocessing","permalink":"/docs/ai-architecture/api/data/preprocessing"},"next":{"title":"Data Export","permalink":"/docs/ai-architecture/api/data/export"}}');var i=t(4848),s=t(8453);const r={sidebar_position:6},d="Data Augmentation",l={},o=[{value:"Create Augmentation Pipeline",id:"create-augmentation-pipeline",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Run Augmentation",id:"run-augmentation",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Request Body",id:"request-body-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Get Augmentation Status",id:"get-augmentation-status",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"List Augmentation Pipelines",id:"list-augmentation-pipelines",level:2},{value:"Endpoint",id:"endpoint-3",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-3",level:3},{value:"Augmentation Status",id:"augmentation-status",level:2},{value:"Supported Techniques",id:"supported-techniques",level:2},{value:"Text Augmentation",id:"text-augmentation",level:3},{value:"Image Augmentation",id:"image-augmentation",level:3},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Augmentation Best Practices",id:"augmentation-best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,s.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"data-augmentation",children:"Data Augmentation"})}),"\n",(0,i.jsx)(n.p,{children:"Enhance your datasets through various augmentation techniques."}),"\n",(0,i.jsx)(n.h2,{id:"create-augmentation-pipeline",children:"Create Augmentation Pipeline"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"POST /v1/data/datasets/{dataset_id}/augmentation\n"})}),"\n",(0,i.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "name": "text_augmentation",\n  "description": "Standard text augmentation pipeline",\n  "techniques": [\n    {\n      "name": "synonym_replacement",\n      "type": "text",\n      "params": {\n        "max_replacements": 3,\n        "language": "en",\n        "preserve_pos": true\n      }\n    },\n    {\n      "name": "back_translation",\n      "type": "text",\n      "params": {\n        "intermediate_languages": ["fr", "de"],\n        "preserve_meaning": true\n      }\n    },\n    {\n      "name": "random_insertion",\n      "type": "text",\n      "params": {\n        "max_insertions": 2,\n        "insertion_type": "synonym"\n      }\n    }\n  ],\n  "options": {\n    "augmentation_factor": 2.0,\n    "preserve_original": true,\n    "seed": 42\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "pipeline": {\n      "id": "pipeline_123",\n      "name": "text_augmentation",\n      "dataset_id": "dataset_123",\n      "status": "created",\n      "created_at": "2024-03-14T12:00:00Z",\n      "techniques": [\n        {\n          "name": "synonym_replacement",\n          "type": "text",\n          "params": {\n            "max_replacements": 3,\n            "language": "en"\n          }\n        }\n      ]\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"run-augmentation",children:"Run Augmentation"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"POST /v1/data/datasets/{dataset_id}/augmentation/{pipeline_id}/run\n"})}),"\n",(0,i.jsx)(n.h3,{id:"request-body-1",children:"Request Body"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "options": {\n    "batch_size": 1000,\n    "max_workers": 4,\n    "save_checkpoints": true\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "augmentation": {\n      "id": "augmentation_123",\n      "pipeline_id": "pipeline_123",\n      "dataset_id": "dataset_123",\n      "status": "processing",\n      "created_at": "2024-03-14T12:00:00Z",\n      "options": {\n        "batch_size": 1000,\n        "max_workers": 4\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"get-augmentation-status",children:"Get Augmentation Status"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/augmentation/{augmentation_id}\n"})}),"\n",(0,i.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "augmentation": {\n      "id": "augmentation_123",\n      "pipeline_id": "pipeline_123",\n      "dataset_id": "dataset_123",\n      "status": "completed",\n      "created_at": "2024-03-14T12:00:00Z",\n      "completed_at": "2024-03-14T12:05:00Z",\n      "progress": {\n        "total_samples": 10000,\n        "processed_samples": 10000,\n        "percentage": 100\n      },\n      "statistics": {\n        "original_samples": 10000,\n        "augmented_samples": 20000,\n        "techniques": {\n          "synonym_replacement": {\n            "samples_processed": 10000,\n            "average_replacements": 2.5\n          },\n          "back_translation": {\n            "samples_processed": 10000,\n            "languages_used": ["fr", "de"]\n          },\n          "random_insertion": {\n            "samples_processed": 10000,\n            "average_insertions": 1.8\n          }\n        }\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:05:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"list-augmentation-pipelines",children:"List Augmentation Pipelines"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-3",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/augmentation\n"})}),"\n",(0,i.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Parameter"}),(0,i.jsx)(n.th,{children:"Type"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"page"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"limit"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Items per page (default: 10)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"status"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Filter by status"})]})]})]}),"\n",(0,i.jsx)(n.h3,{id:"example-response-3",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "pipelines": [\n      {\n        "id": "pipeline_123",\n        "name": "text_augmentation",\n        "dataset_id": "dataset_123",\n        "status": "active",\n        "created_at": "2024-03-14T12:00:00Z",\n        "last_run": "2024-03-14T12:05:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:05:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"augmentation-status",children:"Augmentation Status"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Status"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"created"}),(0,i.jsx)(n.td,{children:"Pipeline created"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"processing"}),(0,i.jsx)(n.td,{children:"Augmentation running"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"completed"}),(0,i.jsx)(n.td,{children:"Augmentation completed"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"failed"}),(0,i.jsx)(n.td,{children:"Augmentation failed"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"cancelled"}),(0,i.jsx)(n.td,{children:"Augmentation cancelled"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"supported-techniques",children:"Supported Techniques"}),"\n",(0,i.jsx)(n.h3,{id:"text-augmentation",children:"Text Augmentation"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Technique"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Synonym Replacement"}),(0,i.jsx)(n.td,{children:"Replace words with synonyms"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Back Translation"}),(0,i.jsx)(n.td,{children:"Translate to another language and back"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Random Insertion"}),(0,i.jsx)(n.td,{children:"Insert new words"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Random Deletion"}),(0,i.jsx)(n.td,{children:"Delete random words"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Random Swap"}),(0,i.jsx)(n.td,{children:"Swap word positions"})]})]})]}),"\n",(0,i.jsx)(n.h3,{id:"image-augmentation",children:"Image Augmentation"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Technique"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Rotation"}),(0,i.jsx)(n.td,{children:"Rotate image"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Flip"}),(0,i.jsx)(n.td,{children:"Horizontal/vertical flip"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Crop"}),(0,i.jsx)(n.td,{children:"Random crop"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Color Jitter"}),(0,i.jsx)(n.td,{children:"Adjust color properties"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Noise Addition"}),(0,i.jsx)(n.td,{children:"Add random noise"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create augmentation pipeline\npipeline = client.data.create_augmentation_pipeline(\n    "dataset_123",\n    name="text_augmentation",\n    techniques=[\n        {\n            "name": "synonym_replacement",\n            "type": "text",\n            "params": {\n                "max_replacements": 3,\n                "language": "en"\n            }\n        }\n    ]\n)\n\n# Run augmentation\naugmentation = client.data.run_augmentation(\n    "dataset_123",\n    "pipeline_123",\n    options={\n        "batch_size": 1000,\n        "max_workers": 4\n    }\n)\n\n# Get augmentation status\nstatus = client.data.get_augmentation_status(\n    "dataset_123",\n    "augmentation_123"\n)\n\n# List pipelines\npipelines = client.data.list_augmentation_pipelines(\n    "dataset_123",\n    page=1,\n    limit=10\n)\n'})}),"\n",(0,i.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create augmentation pipeline\nconst pipeline = await client.data.createAugmentationPipeline('dataset_123', {\n  name: 'text_augmentation',\n  techniques: [\n    {\n      name: 'synonym_replacement',\n      type: 'text',\n      params: {\n        maxReplacements: 3,\n        language: 'en'\n      }\n    }\n  ]\n});\n\n// Run augmentation\nconst augmentation = await client.data.runAugmentation(\n  'dataset_123',\n  'pipeline_123',\n  {\n    options: {\n      batchSize: 1000,\n      maxWorkers: 4\n    }\n  }\n);\n\n// Get augmentation status\nconst status = await client.data.getAugmentationStatus(\n  'dataset_123',\n  'augmentation_123'\n);\n\n// List pipelines\nconst pipelines = await client.data.listAugmentationPipelines('dataset_123', {\n  page: 1,\n  limit: 10\n});\n"})}),"\n",(0,i.jsx)(n.h2,{id:"augmentation-best-practices",children:"Augmentation Best Practices"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"Choose appropriate techniques"}),"\n",(0,i.jsx)(n.li,{children:"Maintain data quality"}),"\n",(0,i.jsx)(n.li,{children:"Preserve original meaning"}),"\n",(0,i.jsx)(n.li,{children:"Use reasonable augmentation factors"}),"\n",(0,i.jsx)(n.li,{children:"Monitor augmentation results"}),"\n",(0,i.jsx)(n.li,{children:"Validate augmented data"}),"\n",(0,i.jsx)(n.li,{children:"Document augmentation steps"}),"\n",(0,i.jsx)(n.li,{children:"Consider computational costs"}),"\n"]})]})}function p(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(c,{...e})}):c(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>r,x:()=>d});var a=t(6540);const i={},s=a.createContext(i);function r(e){const n=a.useContext(s);return a.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function d(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:r(e.components),a.createElement(s.Provider,{value:n},e.children)}}}]);