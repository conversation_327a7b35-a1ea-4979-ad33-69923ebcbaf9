"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6920],{2661:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>a,contentTitle:()=>o,default:()=>h,frontMatter:()=>s,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/tools/old/api/orchestration/orchestration","title":"Orchestration APIs","description":"This section contains documentation for APIs related to workflow orchestration and pipeline management. These APIs provide comprehensive functionality for managing machine learning workflows, model serving, and experiment tracking.","source":"@site/docs/ai-architecture/tools/old/api/orchestration/index.md","sourceDirName":"ai-architecture/tools/old/api/orchestration","slug":"/ai-architecture/tools/old/api/orchestration/","permalink":"/docs/ai-architecture/tools/old/api/orchestration/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/orchestration/index.md","tags":[],"version":"current","frontMatter":{"id":"orchestration","title":"Orchestration APIs","sidebar_label":"Orchestration"},"sidebar":"tutorialSidebar","previous":{"title":"Monitoring API","permalink":"/docs/ai-architecture/tools/old/api/monitoring/monitoring-api"},"next":{"title":"KServe API","permalink":"/docs/ai-architecture/tools/old/api/orchestration/kserve-api"}}');var t=i(4848),l=i(8453);const s={id:"orchestration",title:"Orchestration APIs",sidebar_label:"Orchestration"},o="Orchestration APIs",a={},c=[{value:"Pipeline Management",id:"pipeline-management",level:2},{value:"Model Serving Orchestration",id:"model-serving-orchestration",level:2},{value:"Experiment Management",id:"experiment-management",level:2},{value:"Best Practices",id:"best-practices",level:2},{value:"Related Resources",id:"related-resources",level:2}];function d(e){const n={a:"a",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,l.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"orchestration-apis",children:"Orchestration APIs"})}),"\n",(0,t.jsx)(n.p,{children:"This section contains documentation for APIs related to workflow orchestration and pipeline management. These APIs provide comprehensive functionality for managing machine learning workflows, model serving, and experiment tracking."}),"\n",(0,t.jsx)(n.h2,{id:"pipeline-management",children:"Pipeline Management"}),"\n",(0,t.jsx)(n.p,{children:"APIs for managing machine learning pipelines and workflows."}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.a,{href:"/docs/ai-architecture/tools/old/api/orchestration/kubeflow-api",children:"Kubeflow API"})," - Endpoints for managing ML pipelines and workflows","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Pipeline definition"}),"\n",(0,t.jsx)(n.li,{children:"Workflow execution"}),"\n",(0,t.jsx)(n.li,{children:"Resource management"}),"\n",(0,t.jsx)(n.li,{children:"Pipeline monitoring"}),"\n",(0,t.jsx)(n.li,{children:"Component management"}),"\n",(0,t.jsx)(n.li,{children:"Pipeline versioning"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"model-serving-orchestration",children:"Model Serving Orchestration"}),"\n",(0,t.jsx)(n.p,{children:"APIs for managing model serving infrastructure."}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.a,{href:"/docs/ai-architecture/tools/old/api/orchestration/kserve-api",children:"KServe API"})," - Endpoints for model serving orchestration","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Model deployment"}),"\n",(0,t.jsx)(n.li,{children:"Traffic management"}),"\n",(0,t.jsx)(n.li,{children:"Scaling configuration"}),"\n",(0,t.jsx)(n.li,{children:"Canary deployments"}),"\n",(0,t.jsx)(n.li,{children:"A/B testing"}),"\n",(0,t.jsx)(n.li,{children:"Model rollback"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"experiment-management",children:"Experiment Management"}),"\n",(0,t.jsx)(n.p,{children:"APIs for managing experiments and model lifecycle."}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.a,{href:"/docs/ai-architecture/tools/old/api/orchestration/mlflow-api",children:"MLflow API"})," - Endpoints for experiment tracking and model lifecycle management","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Experiment tracking"}),"\n",(0,t.jsx)(n.li,{children:"Model registry"}),"\n",(0,t.jsx)(n.li,{children:"Artifact storage"}),"\n",(0,t.jsx)(n.li,{children:"Metric logging"}),"\n",(0,t.jsx)(n.li,{children:"Model packaging"}),"\n",(0,t.jsx)(n.li,{children:"Deployment tracking"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Pipeline Management"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Use version control"}),"\n",(0,t.jsx)(n.li,{children:"Implement proper error handling"}),"\n",(0,t.jsx)(n.li,{children:"Monitor pipeline health"}),"\n",(0,t.jsx)(n.li,{children:"Optimize resource usage"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Model Deployment"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Implement canary deployments"}),"\n",(0,t.jsx)(n.li,{children:"Set up proper monitoring"}),"\n",(0,t.jsx)(n.li,{children:"Configure auto-scaling"}),"\n",(0,t.jsx)(n.li,{children:"Plan for rollbacks"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Experiment Tracking"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Track all parameters"}),"\n",(0,t.jsx)(n.li,{children:"Log comprehensive metrics"}),"\n",(0,t.jsx)(n.li,{children:"Document experiment purpose"}),"\n",(0,t.jsx)(n.li,{children:"Manage artifacts properly"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"related-resources",children:"Related Resources"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.a,{href:"/docs/ai-architecture/tools/old/api/model-management/",children:"Model Management"})," - Manage model deployments"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.a,{href:"/docs/ai-architecture/tools/old/api/monitoring/",children:"Monitoring"})," - Monitor pipeline health"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.a,{href:"/docs/ai-architecture/tools/old/api/data-management/",children:"Data Management"})," - Access training data"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(d,{...e})}):d(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>s,x:()=>o});var r=i(6540);const t={},l=r.createContext(t);function s(e){const n=r.useContext(l);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function o(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:s(e.components),r.createElement(l.Provider,{value:n},e.children)}}}]);