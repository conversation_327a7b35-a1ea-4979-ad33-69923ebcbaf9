"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2678],{600:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>d,contentTitle:()=>a,default:()=>h,frontMatter:()=>s,metadata:()=>i,toc:()=>l});const i=JSON.parse('{"id":"adrs/global/mfe-and-bff","title":"3. Micro-frontends and BFF","description":"Date: 2025-03-10","source":"@site/docs/adrs/global/0003-mfe-and-bff.md","sourceDirName":"adrs/global","slug":"/adrs/global/mfe-and-bff","permalink":"/docs/adrs/global/mfe-and-bff","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/global/0003-mfe-and-bff.md","tags":[],"version":"current","sidebarPosition":3,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"2. Monorepo per Project","permalink":"/docs/adrs/global/monorepo"},"next":{"title":"4. ODRL as Policy and Entitlement Definition Language","permalink":"/docs/adrs/global/odrl"}}');var o=t(4848),r=t(8453);const s={},a="3. Micro-frontends and BFF",d={},l=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2},{value:"Benefits",id:"benefits",level:3},{value:"Risks &amp; Mitigation",id:"risks--mitigation",level:3}];function c(e){const n={a:"a",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.header,{children:(0,o.jsx)(n.h1,{id:"3-micro-frontends-and-bff",children:"3. Micro-frontends and BFF"})}),"\n",(0,o.jsx)(n.p,{children:"Date: 2025-03-10"}),"\n",(0,o.jsx)(n.h2,{id:"status",children:"Status"}),"\n",(0,o.jsx)(n.p,{children:"Proposed"}),"\n",(0,o.jsx)(n.h2,{id:"context",children:"Context"}),"\n",(0,o.jsx)(n.p,{children:"As our applications grow, we need a scalable frontend architecture that allows independent development and deployment of\nfeatures. The growing complexity of the platform requires a way to modularize the frontend and ensure optimized\nbackend communication for each part of the application. We also need a solution to handle multiple teams working\non different frontend components, ensuring consistent integration and efficient data handling."}),"\n",(0,o.jsx)(n.h2,{id:"decision",children:"Decision"}),"\n",(0,o.jsxs)(n.p,{children:["We will adopt a ",(0,o.jsx)(n.strong,{children:"micro-frontend architecture"})," for the frontend and utilize ",(0,o.jsx)(n.strong,{children:"Backend-for-Frontend (BFF)"})," to provide\ntailored backend services for each micro-frontend. This approach will:"]}),"\n",(0,o.jsxs)(n.ul,{children:["\n",(0,o.jsx)(n.li,{children:"Enable independent development and deployment of frontend modules."}),"\n",(0,o.jsx)(n.li,{children:"Ensure optimized communication between frontend modules and the backend with customized API layers."}),"\n"]}),"\n",(0,o.jsxs)(n.p,{children:["A similar architecture is described by AWS ",(0,o.jsx)(n.a,{href:"https://docs.aws.amazon.com/prescriptive-guidance/latest/micro-frontends-aws/introduction.html",children:"here"}),"."]}),"\n",(0,o.jsx)(n.h2,{id:"consequences",children:"Consequences"}),"\n",(0,o.jsx)(n.h3,{id:"benefits",children:"Benefits"}),"\n",(0,o.jsxs)(n.ul,{children:["\n",(0,o.jsx)(n.li,{children:"Independent Development and Deployment"}),"\n",(0,o.jsx)(n.li,{children:"Scalable Frontend Architecture"}),"\n",(0,o.jsx)(n.li,{children:"Tailored Backend Services"}),"\n",(0,o.jsx)(n.li,{children:"Faster Iteration and Deployment Cycles"}),"\n"]}),"\n",(0,o.jsx)(n.h3,{id:"risks--mitigation",children:"Risks & Mitigation"}),"\n",(0,o.jsxs)(n.ul,{children:["\n",(0,o.jsxs)(n.li,{children:[(0,o.jsx)(n.strong,{children:"Versioning & Compatibility"}),": It can be challenging to manage different versions of micro-frontends. We will\nimplement strict versioning policies and automated integration tests to ensure compatibility."]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,o.jsx)(n,{...e,children:(0,o.jsx)(c,{...e})}):c(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>s,x:()=>a});var i=t(6540);const o={},r=i.createContext(o);function s(e){const n=i.useContext(r);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(o):e.components||o:s(e.components),i.createElement(r.Provider,{value:n},e.children)}}}]);