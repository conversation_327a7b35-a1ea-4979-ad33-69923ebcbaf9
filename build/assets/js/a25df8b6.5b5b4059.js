"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[9987],{5385:(e,i,t)=>{t.r(i),t.d(i,{assets:()=>l,contentTitle:()=>s,default:()=>h,frontMatter:()=>c,metadata:()=>a,toc:()=>o});const a=JSON.parse('{"id":"ai-architecture/api/index","title":"API Reference","description":"Welcome to the 91.life AI Platform API documentation. This section provides comprehensive documentation for all available API endpoints, authentication methods, and usage examples.","source":"@site/docs/ai-architecture/api/index.md","sourceDirName":"ai-architecture/api","slug":"/ai-architecture/api/","permalink":"/docs/ai-architecture/api/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/index.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"FHIR Database Strategy","permalink":"/docs/research/fhir/"},"next":{"title":"API Overview","permalink":"/docs/ai-architecture/api/api-overview"}}');var n=t(4848),r=t(8453);const c={sidebar_position:1},s="API Reference",l={},o=[{value:"Getting Started",id:"getting-started",level:2},{value:"Available Endpoints",id:"available-endpoints",level:2},{value:"Authentication",id:"authentication",level:3},{value:"Data Management",id:"data-management",level:3},{value:"Model Management",id:"model-management",level:3},{value:"Monitoring",id:"monitoring",level:3},{value:"Support",id:"support",level:2}];function d(e){const i={a:"a",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",ul:"ul",...(0,r.R)(),...e.components};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.header,{children:(0,n.jsx)(i.h1,{id:"api-reference",children:"API Reference"})}),"\n",(0,n.jsx)(i.p,{children:"Welcome to the 91.life AI Platform API documentation. This section provides comprehensive documentation for all available API endpoints, authentication methods, and usage examples."}),"\n",(0,n.jsx)(i.h2,{id:"getting-started",children:"Getting Started"}),"\n",(0,n.jsx)(i.p,{children:"To get started with the API:"}),"\n",(0,n.jsxs)(i.ol,{children:["\n",(0,n.jsxs)(i.li,{children:["Review the ",(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/auth/authentication",children:"Authentication"})," guide"]}),"\n",(0,n.jsxs)(i.li,{children:["Check out the ",(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/api-overview",children:"API Overview"})]}),"\n",(0,n.jsx)(i.li,{children:"Explore the available endpoints"}),"\n"]}),"\n",(0,n.jsx)(i.h2,{id:"available-endpoints",children:"Available Endpoints"}),"\n",(0,n.jsx)(i.h3,{id:"authentication",children:"Authentication"}),"\n",(0,n.jsxs)(i.ul,{children:["\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/auth/authentication",children:"Authentication"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/auth/authorization",children:"Authorization"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/auth/tokens",children:"Tokens"})}),"\n"]}),"\n",(0,n.jsx)(i.h3,{id:"data-management",children:"Data Management"}),"\n",(0,n.jsxs)(i.ul,{children:["\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/data/upload",children:"Data Upload"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/data/processing",children:"Data Processing"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/data/validation",children:"Data Validation"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/data/versioning",children:"Data Versioning"})}),"\n"]}),"\n",(0,n.jsx)(i.h3,{id:"model-management",children:"Model Management"}),"\n",(0,n.jsxs)(i.ul,{children:["\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/models/list-models",children:"List Models"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/models/model-details",children:"Model Details"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/models/predictions",children:"Predictions"})}),"\n"]}),"\n",(0,n.jsx)(i.h3,{id:"monitoring",children:"Monitoring"}),"\n",(0,n.jsxs)(i.ul,{children:["\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/monitoring/metrics",children:"Metrics"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/monitoring/logs",children:"Logs"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/monitoring/alerts",children:"Alerts"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/monitoring/dashboards",children:"Dashboards"})}),"\n",(0,n.jsx)(i.li,{children:(0,n.jsx)(i.a,{href:"/docs/ai-architecture/api/monitoring/reports",children:"Reports"})}),"\n"]}),"\n",(0,n.jsx)(i.h2,{id:"support",children:"Support"}),"\n",(0,n.jsxs)(i.p,{children:["For API support and questions, please contact us at ",(0,n.jsx)(i.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"}),"."]})]})}function h(e={}){const{wrapper:i}={...(0,r.R)(),...e.components};return i?(0,n.jsx)(i,{...e,children:(0,n.jsx)(d,{...e})}):d(e)}},8453:(e,i,t)=>{t.d(i,{R:()=>c,x:()=>s});var a=t(6540);const n={},r=a.createContext(n);function c(e){const i=a.useContext(r);return a.useMemo((function(){return"function"==typeof e?e(i):{...i,...e}}),[i,e])}function s(e){let i;return i=e.disableParentContext?"function"==typeof e.components?e.components(n):e.components||n:c(e.components),a.createElement(r.Provider,{value:i},e.children)}}}]);