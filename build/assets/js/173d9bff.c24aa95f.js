"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[4447],{1820:(e,n,s)=>{s.r(n),s.d(n,{assets:()=>o,contentTitle:()=>l,default:()=>h,frontMatter:()=>t,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/tools/old/api/data-management/lakefs-api","title":"LakeFS API Documentation","description":"LakeFS provides a RESTful API for managing data versioning and branching in your data lake. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/data-management/lakefs-api.md","sourceDirName":"ai-architecture/tools/old/api/data-management","slug":"/ai-architecture/tools/old/api/data-management/lakefs-api","permalink":"/docs/ai-architecture/tools/old/api/data-management/lakefs-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/data-management/lakefs-api.md","tags":[],"version":"current","frontMatter":{"id":"lakefs-api","title":"LakeFS API Documentation","sidebar_label":"LakeFS API"},"sidebar":"tutorialSidebar","previous":{"title":"Feature Store API","permalink":"/docs/ai-architecture/tools/old/api/data-management/feature-store-api"},"next":{"title":"MinIO API","permalink":"/docs/ai-architecture/tools/old/api/data-management/minio-api"}}');var i=s(4848),a=s(8453);const t={id:"lakefs-api",title:"LakeFS API Documentation",sidebar_label:"LakeFS API"},l="LakeFS API Documentation",o={},c=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Repository Management",id:"repository-management",level:3},{value:"Create Repository",id:"create-repository",level:4},{value:"List Repositories",id:"list-repositories",level:4},{value:"Branch Management",id:"branch-management",level:3},{value:"Create Branch",id:"create-branch",level:4},{value:"List Branches",id:"list-branches",level:4},{value:"Object Operations",id:"object-operations",level:3},{value:"Upload Object",id:"upload-object",level:4},{value:"Get Object",id:"get-object",level:4},{value:"Commit Management",id:"commit-management",level:3},{value:"Create Commit",id:"create-commit",level:4},{value:"List Commits",id:"list-commits",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function d(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,a.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"lakefs-api-documentation",children:"LakeFS API Documentation"})}),"\n",(0,i.jsx)(n.p,{children:"LakeFS provides a RESTful API for managing data versioning and branching in your data lake. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,i.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"https://lakefs.example.com/api/v1\n"})}),"\n",(0,i.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,i.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,i.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,i.jsx)(n.h3,{id:"repository-management",children:"Repository Management"}),"\n",(0,i.jsx)(n.h4,{id:"create-repository",children:"Create Repository"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-http",children:"POST /repositories\n"})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "name": "my-repo",\n    "storage_namespace": "s3://my-bucket/my-repo",\n    "default_branch": "main"\n}\n'})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Response:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "id": "my-repo",\n    "creation_date": "2024-03-20T10:00:00Z",\n    "default_branch": "main",\n    "storage_namespace": "s3://my-bucket/my-repo"\n}\n'})}),"\n",(0,i.jsx)(n.h4,{id:"list-repositories",children:"List Repositories"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-http",children:"GET /repositories\n"})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"prefix"})," (optional): Filter repositories by prefix"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"after"})," (optional): Pagination token"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"amount"})," (optional): Number of results per page"]}),"\n"]}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Response:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "results": [\n        {\n            "id": "my-repo",\n            "creation_date": "2024-03-20T10:00:00Z",\n            "default_branch": "main",\n            "storage_namespace": "s3://my-bucket/my-repo"\n        }\n    ],\n    "pagination": {\n        "has_more": false,\n        "next_offset": ""\n    }\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"branch-management",children:"Branch Management"}),"\n",(0,i.jsx)(n.h4,{id:"create-branch",children:"Create Branch"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-http",children:"POST /repositories/{repository}/branches\n"})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "name": "feature-branch",\n    "source": "main"\n}\n'})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Response:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "id": "feature-branch",\n    "commit_id": "abc123...",\n    "creation_date": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,i.jsx)(n.h4,{id:"list-branches",children:"List Branches"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-http",children:"GET /repositories/{repository}/branches\n"})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"prefix"})," (optional): Filter branches by prefix"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"after"})," (optional): Pagination token"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"amount"})," (optional): Number of results per page"]}),"\n"]}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Response:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "results": [\n        {\n            "id": "main",\n            "commit_id": "abc123...",\n            "creation_date": "2024-03-20T10:00:00Z"\n        }\n    ],\n    "pagination": {\n        "has_more": false,\n        "next_offset": ""\n    }\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"object-operations",children:"Object Operations"}),"\n",(0,i.jsx)(n.h4,{id:"upload-object",children:"Upload Object"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-http",children:"PUT /repositories/{repository}/branches/{branch}/objects\n"})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Headers:"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"Content-Type"}),": application/octet-stream"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"X-Lakefs-Content-Type"}),": (optional) MIME type of the object"]}),"\n"]}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Request Body:"}),"\nBinary content of the object"]}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Response:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "path": "path/to/object",\n    "size_bytes": 1024,\n    "checksum": "abc123..."\n}\n'})}),"\n",(0,i.jsx)(n.h4,{id:"get-object",children:"Get Object"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-http",children:"GET /repositories/{repository}/branches/{branch}/objects\n"})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"path"}),": Path to the object"]}),"\n"]}),"\n",(0,i.jsxs)(n.p,{children:[(0,i.jsx)(n.strong,{children:"Response:"}),"\nBinary content of the object"]}),"\n",(0,i.jsx)(n.h3,{id:"commit-management",children:"Commit Management"}),"\n",(0,i.jsx)(n.h4,{id:"create-commit",children:"Create Commit"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-http",children:"POST /repositories/{repository}/branches/{branch}/commits\n"})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "message": "Commit message",\n    "metadata": {\n        "key": "value"\n    }\n}\n'})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Response:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "id": "abc123...",\n    "parents": ["def456..."],\n    "committer": "<EMAIL>",\n    "message": "Commit message",\n    "creation_date": "2024-03-20T10:00:00Z",\n    "meta_range_id": "ghi789...",\n    "metadata": {\n        "key": "value"\n    }\n}\n'})}),"\n",(0,i.jsx)(n.h4,{id:"list-commits",children:"List Commits"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-http",children:"GET /repositories/{repository}/branches/{branch}/commits\n"})}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"after"})," (optional): Pagination token"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"amount"})," (optional): Number of results per page"]}),"\n"]}),"\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Response:"})}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n    "results": [\n        {\n            "id": "abc123...",\n            "parents": ["def456..."],\n            "committer": "<EMAIL>",\n            "message": "Commit message",\n            "creation_date": "2024-03-20T10:00:00Z",\n            "meta_range_id": "ghi789...",\n            "metadata": {\n                "key": "value"\n            }\n        }\n    ],\n    "pagination": {\n        "has_more": false,\n        "next_offset": ""\n    }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,i.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'import requests\n\ndef create_repository(name, storage_namespace, token):\n    url = "https://lakefs.example.com/api/v1/repositories"\n    headers = {\n        "Authorization": f"Bearer {token}",\n        "Content-Type": "application/json"\n    }\n    data = {\n        "name": name,\n        "storage_namespace": storage_namespace,\n        "default_branch": "main"\n    }\n    response = requests.post(url, headers=headers, json=data)\n    return response.json()\n\ndef upload_object(repo, branch, path, content, token):\n    url = f"https://lakefs.example.com/api/v1/repositories/{repo}/branches/{branch}/objects"\n    headers = {\n        "Authorization": f"Bearer {token}",\n        "Content-Type": "application/octet-stream"\n    }\n    response = requests.put(url, headers=headers, data=content)\n    return response.json()\n'})}),"\n",(0,i.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-bash",children:'# Create repository\ncurl -X POST https://lakefs.example.com/api/v1/repositories \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "my-repo",\n    "storage_namespace": "s3://my-bucket/my-repo",\n    "default_branch": "main"\n  }\'\n\n# Upload object\ncurl -X PUT https://lakefs.example.com/api/v1/repositories/my-repo/branches/main/objects \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/octet-stream" \\\n  --data-binary @file.txt\n'})}),"\n",(0,i.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Code"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"400"}),(0,i.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"401"}),(0,i.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"403"}),(0,i.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"404"}),(0,i.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"409"}),(0,i.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"429"}),(0,i.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"500"}),(0,i.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,i.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,i.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,i.jsxs)(n.li,{children:[(0,i.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,i.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Error Handling"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Implement retry logic with exponential backoff"}),"\n",(0,i.jsx)(n.li,{children:"Handle rate limiting appropriately"}),"\n",(0,i.jsx)(n.li,{children:"Check response status codes"}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Performance"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Use appropriate chunk sizes for large uploads"}),"\n",(0,i.jsx)(n.li,{children:"Implement parallel uploads for multiple objects"}),"\n",(0,i.jsx)(n.li,{children:"Cache frequently accessed objects"}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Security"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Rotate authentication tokens regularly"}),"\n",(0,i.jsx)(n.li,{children:"Use HTTPS for all API calls"}),"\n",(0,i.jsx)(n.li,{children:"Implement proper access controls"}),"\n"]}),"\n"]}),"\n",(0,i.jsxs)(n.li,{children:["\n",(0,i.jsx)(n.p,{children:(0,i.jsx)(n.strong,{children:"Monitoring"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Monitor API response times"}),"\n",(0,i.jsx)(n.li,{children:"Track error rates"}),"\n",(0,i.jsx)(n.li,{children:"Set up alerts for unusual patterns"}),"\n"]}),"\n"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,a.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(d,{...e})}):d(e)}},8453:(e,n,s)=>{s.d(n,{R:()=>t,x:()=>l});var r=s(6540);const i={},a=r.createContext(i);function t(e){const n=r.useContext(a);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:t(e.components),r.createElement(a.Provider,{value:n},e.children)}}}]);