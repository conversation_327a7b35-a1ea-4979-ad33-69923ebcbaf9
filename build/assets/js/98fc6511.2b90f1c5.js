"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7984],{8453:(e,t,i)=>{i.d(t,{R:()=>a,x:()=>s});var n=i(6540);const r={},o=n.createContext(r);function a(e){const t=n.useContext(o);return n.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function s(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:a(e.components),n.createElement(o.Provider,{value:t},e.children)}},9877:(e,t,i)=>{i.r(t),i.d(t,{assets:()=>c,contentTitle:()=>s,default:()=>p,frontMatter:()=>a,metadata:()=>n,toc:()=>l});const n=JSON.parse('{"id":"ai-architecture/support/index","title":"Support","description":"Contact Information","source":"@site/docs/ai-architecture/support/index.md","sourceDirName":"ai-architecture/support","slug":"/ai-architecture/support/","permalink":"/docs/ai-architecture/support/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/support/index.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"Introduction","permalink":"/docs/ai-architecture/intro"},"next":{"title":"Architecture Overview","permalink":"/docs/ai-architecture/architecture-overview/"}}');var r=i(4848),o=i(8453);const a={sidebar_position:1},s="Support",c={},l=[{value:"Contact Information",id:"contact-information",level:2},{value:"Documentation Support",id:"documentation-support",level:2},{value:"Enterprise Support",id:"enterprise-support",level:2}];function u(e){const t={a:"a",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",ul:"ul",...(0,o.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(t.header,{children:(0,r.jsx)(t.h1,{id:"support",children:"Support"})}),"\n",(0,r.jsx)(t.h2,{id:"contact-information",children:"Contact Information"}),"\n",(0,r.jsx)(t.p,{children:"For technical support and inquiries, please contact us at:"}),"\n",(0,r.jsxs)(t.ul,{children:["\n",(0,r.jsxs)(t.li,{children:["Email: ",(0,r.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"}),", ",(0,r.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),"\n",(0,r.jsxs)(t.li,{children:["Website: ",(0,r.jsx)(t.a,{href:"https://91.life",children:"91.life"})]}),"\n"]}),"\n",(0,r.jsx)(t.h2,{id:"documentation-support",children:"Documentation Support"}),"\n",(0,r.jsx)(t.p,{children:"If you find any issues with the documentation or have suggestions for improvements, please:"}),"\n",(0,r.jsxs)(t.ol,{children:["\n",(0,r.jsxs)(t.li,{children:["Open an issue on our ",(0,r.jsx)(t.a,{href:"https://github.com/91-life/ai-platform-docs",children:"GitHub repository"})]}),"\n",(0,r.jsxs)(t.li,{children:["Contact our documentation team at ",(0,r.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"}),", ",(0,r.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})]}),"\n"]}),"\n",(0,r.jsx)(t.h2,{id:"enterprise-support",children:"Enterprise Support"}),"\n",(0,r.jsxs)(t.p,{children:["Contact our team at ",(0,r.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"}),", ",(0,r.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})," to learn more about our enterprise support options."]})]})}function p(e={}){const{wrapper:t}={...(0,o.R)(),...e.components};return t?(0,r.jsx)(t,{...e,children:(0,r.jsx)(u,{...e})}):u(e)}}}]);