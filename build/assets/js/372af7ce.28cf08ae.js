"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3115],{683:(n,e,t)=>{t.r(e),t.d(e,{assets:()=>d,contentTitle:()=>l,default:()=>h,frontMatter:()=>a,metadata:()=>i,toc:()=>o});const i=JSON.parse('{"id":"ai-architecture/api/data/monitoring","title":"Data Monitoring","description":"Monitor and track changes in your datasets over time.","source":"@site/docs/ai-architecture/api/data/monitoring.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/monitoring","permalink":"/docs/ai-architecture/api/data/monitoring","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/monitoring.md","tags":[],"version":"current","sidebarPosition":9,"frontMatter":{"sidebar_position":9},"sidebar":"tutorialSidebar","previous":{"title":"Data Statistics","permalink":"/docs/ai-architecture/api/data/statistics"},"next":{"title":"Data Governance","permalink":"/docs/ai-architecture/api/data/governance"}}');var r=t(4848),s=t(8453);const a={sidebar_position:9},l="Data Monitoring",d={},o=[{value:"Create Monitoring Job",id:"create-monitoring-job",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Monitoring Results",id:"get-monitoring-results",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"List Monitoring Jobs",id:"list-monitoring-jobs",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Query Parameters",id:"query-parameters-1",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Monitoring Status",id:"monitoring-status",level:2},{value:"Available Metrics",id:"available-metrics",level:2},{value:"Quality Metrics",id:"quality-metrics",level:3},{value:"Drift Metrics",id:"drift-metrics",level:3},{value:"Anomaly Metrics",id:"anomaly-metrics",level:3},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Monitoring Best Practices",id:"monitoring-best-practices",level:2}];function c(n){const e={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,s.R)(),...n.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(e.header,{children:(0,r.jsx)(e.h1,{id:"data-monitoring",children:"Data Monitoring"})}),"\n",(0,r.jsx)(e.p,{children:"Monitor and track changes in your datasets over time."}),"\n",(0,r.jsx)(e.h2,{id:"create-monitoring-job",children:"Create Monitoring Job"}),"\n",(0,r.jsx)(e.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{children:"POST /v1/data/datasets/{dataset_id}/monitoring\n"})}),"\n",(0,r.jsx)(e.h3,{id:"request-body",children:"Request Body"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{className:"language-json",children:'{\n  "name": "data_quality_monitoring",\n  "description": "Monitor data quality metrics",\n  "metrics": [\n    {\n      "name": "completeness",\n      "type": "quality",\n      "threshold": 0.95,\n      "alert_on": "below"\n    },\n    {\n      "name": "distribution_shift",\n      "type": "drift",\n      "threshold": 0.1,\n      "alert_on": "above"\n    },\n    {\n      "name": "anomaly_detection",\n      "type": "anomaly",\n      "threshold": 0.05,\n      "alert_on": "above"\n    }\n  ],\n  "schedule": {\n    "frequency": "daily",\n    "time": "00:00",\n    "timezone": "UTC"\n  },\n  "notifications": {\n    "email": ["<EMAIL>"],\n    "webhook": "https://api.example.com/webhook",\n    "slack": "https://hooks.slack.com/services/xxx"\n  }\n}\n'})}),"\n",(0,r.jsx)(e.h3,{id:"example-response",children:"Example Response"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{className:"language-json",children:'{\n  "data": {\n    "monitoring": {\n      "id": "monitoring_123",\n      "dataset_id": "dataset_123",\n      "name": "data_quality_monitoring",\n      "status": "active",\n      "created_at": "2024-03-14T12:00:00Z",\n      "metrics": [\n        {\n          "name": "completeness",\n          "type": "quality",\n          "threshold": 0.95\n        }\n      ]\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(e.h2,{id:"get-monitoring-results",children:"Get Monitoring Results"}),"\n",(0,r.jsx)(e.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{children:"GET /v1/data/datasets/{dataset_id}/monitoring/{monitoring_id}/results\n"})}),"\n",(0,r.jsx)(e.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,r.jsxs)(e.table,{children:[(0,r.jsx)(e.thead,{children:(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.th,{children:"Parameter"}),(0,r.jsx)(e.th,{children:"Type"}),(0,r.jsx)(e.th,{children:"Description"})]})}),(0,r.jsxs)(e.tbody,{children:[(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"start_date"}),(0,r.jsx)(e.td,{children:"string"}),(0,r.jsx)(e.td,{children:"Start date (ISO format)"})]}),(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"end_date"}),(0,r.jsx)(e.td,{children:"string"}),(0,r.jsx)(e.td,{children:"End date (ISO format)"})]}),(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"metrics"}),(0,r.jsx)(e.td,{children:"array"}),(0,r.jsx)(e.td,{children:"Filter by metrics"})]})]})]}),"\n",(0,r.jsx)(e.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{className:"language-json",children:'{\n  "data": {\n    "results": {\n      "monitoring_id": "monitoring_123",\n      "dataset_id": "dataset_123",\n      "period": {\n        "start": "2024-03-01T00:00:00Z",\n        "end": "2024-03-14T00:00:00Z"\n      },\n      "metrics": {\n        "completeness": {\n          "current": 0.98,\n          "history": [\n            {\n              "timestamp": "2024-03-01T00:00:00Z",\n              "value": 0.99\n            },\n            {\n              "timestamp": "2024-03-02T00:00:00Z",\n              "value": 0.98\n            }\n          ],\n          "alerts": [\n            {\n              "timestamp": "2024-03-03T00:00:00Z",\n              "value": 0.94,\n              "threshold": 0.95,\n              "type": "below_threshold"\n            }\n          ]\n        },\n        "distribution_shift": {\n          "current": 0.05,\n          "history": [\n            {\n              "timestamp": "2024-03-01T00:00:00Z",\n              "value": 0.02\n            },\n            {\n              "timestamp": "2024-03-02T00:00:00Z",\n              "value": 0.03\n            }\n          ],\n          "alerts": []\n        }\n      },\n      "summary": {\n        "total_checks": 14,\n        "alerts": 1,\n        "status": "warning"\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T00:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(e.h2,{id:"list-monitoring-jobs",children:"List Monitoring Jobs"}),"\n",(0,r.jsx)(e.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{children:"GET /v1/data/datasets/{dataset_id}/monitoring\n"})}),"\n",(0,r.jsx)(e.h3,{id:"query-parameters-1",children:"Query Parameters"}),"\n",(0,r.jsxs)(e.table,{children:[(0,r.jsx)(e.thead,{children:(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.th,{children:"Parameter"}),(0,r.jsx)(e.th,{children:"Type"}),(0,r.jsx)(e.th,{children:"Description"})]})}),(0,r.jsxs)(e.tbody,{children:[(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"page"}),(0,r.jsx)(e.td,{children:"integer"}),(0,r.jsx)(e.td,{children:"Page number (default: 1)"})]}),(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"limit"}),(0,r.jsx)(e.td,{children:"integer"}),(0,r.jsx)(e.td,{children:"Items per page (default: 10)"})]}),(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"status"}),(0,r.jsx)(e.td,{children:"string"}),(0,r.jsx)(e.td,{children:"Filter by status"})]})]})]}),"\n",(0,r.jsx)(e.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{className:"language-json",children:'{\n  "data": {\n    "monitoring": [\n      {\n        "id": "monitoring_123",\n        "dataset_id": "dataset_123",\n        "name": "data_quality_monitoring",\n        "status": "active",\n        "created_at": "2024-03-14T12:00:00Z",\n        "last_run": "2024-03-14T00:00:00Z",\n        "metrics": [\n          "completeness",\n          "distribution_shift",\n          "anomaly_detection"\n        ]\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T00:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(e.h2,{id:"monitoring-status",children:"Monitoring Status"}),"\n",(0,r.jsxs)(e.table,{children:[(0,r.jsx)(e.thead,{children:(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.th,{children:"Status"}),(0,r.jsx)(e.th,{children:"Description"})]})}),(0,r.jsxs)(e.tbody,{children:[(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"active"}),(0,r.jsx)(e.td,{children:"Monitoring is active"})]}),(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"paused"}),(0,r.jsx)(e.td,{children:"Monitoring is paused"})]}),(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"inactive"}),(0,r.jsx)(e.td,{children:"Monitoring is inactive"})]}),(0,r.jsxs)(e.tr,{children:[(0,r.jsx)(e.td,{children:"error"}),(0,r.jsx)(e.td,{children:"Monitoring has errors"})]})]})]}),"\n",(0,r.jsx)(e.h2,{id:"available-metrics",children:"Available Metrics"}),"\n",(0,r.jsx)(e.h3,{id:"quality-metrics",children:"Quality Metrics"}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Completeness"}),"\n",(0,r.jsx)(e.li,{children:"Consistency"}),"\n",(0,r.jsx)(e.li,{children:"Accuracy"}),"\n",(0,r.jsx)(e.li,{children:"Validity"}),"\n",(0,r.jsx)(e.li,{children:"Uniqueness"}),"\n"]}),"\n",(0,r.jsx)(e.h3,{id:"drift-metrics",children:"Drift Metrics"}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Distribution shift"}),"\n",(0,r.jsx)(e.li,{children:"Feature drift"}),"\n",(0,r.jsx)(e.li,{children:"Concept drift"}),"\n",(0,r.jsx)(e.li,{children:"Data drift"}),"\n",(0,r.jsx)(e.li,{children:"Model drift"}),"\n"]}),"\n",(0,r.jsx)(e.h3,{id:"anomaly-metrics",children:"Anomaly Metrics"}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Statistical anomalies"}),"\n",(0,r.jsx)(e.li,{children:"Pattern anomalies"}),"\n",(0,r.jsx)(e.li,{children:"Outlier detection"}),"\n",(0,r.jsx)(e.li,{children:"Change detection"}),"\n",(0,r.jsx)(e.li,{children:"Trend analysis"}),"\n"]}),"\n",(0,r.jsx)(e.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,r.jsx)(e.h3,{id:"python",children:"Python"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create monitoring job\nmonitoring = client.data.create_monitoring(\n    "dataset_123",\n    name="data_quality_monitoring",\n    metrics=[\n        {\n            "name": "completeness",\n            "type": "quality",\n            "threshold": 0.95\n        }\n    ]\n)\n\n# Get monitoring results\nresults = client.data.get_monitoring_results(\n    "dataset_123",\n    "monitoring_123",\n    start_date="2024-03-01",\n    end_date="2024-03-14"\n)\n\n# List monitoring jobs\njobs = client.data.list_monitoring(\n    "dataset_123",\n    page=1,\n    limit=10\n)\n'})}),"\n",(0,r.jsx)(e.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,r.jsx)(e.pre,{children:(0,r.jsx)(e.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create monitoring job\nconst monitoring = await client.data.createMonitoring('dataset_123', {\n  name: 'data_quality_monitoring',\n  metrics: [\n    {\n      name: 'completeness',\n      type: 'quality',\n      threshold: 0.95\n    }\n  ]\n});\n\n// Get monitoring results\nconst results = await client.data.getMonitoringResults(\n  'dataset_123',\n  'monitoring_123',\n  {\n    startDate: '2024-03-01',\n    endDate: '2024-03-14'\n  }\n);\n\n// List monitoring jobs\nconst jobs = await client.data.listMonitoring('dataset_123', {\n  page: 1,\n  limit: 10\n});\n"})}),"\n",(0,r.jsx)(e.h2,{id:"monitoring-best-practices",children:"Monitoring Best Practices"}),"\n",(0,r.jsxs)(e.ol,{children:["\n",(0,r.jsx)(e.li,{children:"Define clear metrics"}),"\n",(0,r.jsx)(e.li,{children:"Set appropriate thresholds"}),"\n",(0,r.jsx)(e.li,{children:"Configure notifications"}),"\n",(0,r.jsx)(e.li,{children:"Monitor regularly"}),"\n",(0,r.jsx)(e.li,{children:"Track historical data"}),"\n",(0,r.jsx)(e.li,{children:"Analyze trends"}),"\n",(0,r.jsx)(e.li,{children:"Respond to alerts"}),"\n",(0,r.jsx)(e.li,{children:"Document findings"}),"\n"]})]})}function h(n={}){const{wrapper:e}={...(0,s.R)(),...n.components};return e?(0,r.jsx)(e,{...n,children:(0,r.jsx)(c,{...n})}):c(n)}},8453:(n,e,t)=>{t.d(e,{R:()=>a,x:()=>l});var i=t(6540);const r={},s=i.createContext(r);function a(n){const e=i.useContext(s);return i.useMemo((function(){return"function"==typeof n?n(e):{...e,...n}}),[e,n])}function l(n){let e;return e=n.disableParentContext?"function"==typeof n.components?n.components(r):n.components||r:a(n.components),i.createElement(s.Provider,{value:e},n.children)}}}]);