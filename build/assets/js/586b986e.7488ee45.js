"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7872],{7483:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>d,contentTitle:()=>r,default:()=>u,frontMatter:()=>s,metadata:()=>t,toc:()=>c});const t=JSON.parse('{"id":"ai-architecture/tools/old/api/data-management/data-quality-api","title":"Data Quality API Documentation","description":"The Data Quality API provides endpoints for validating, monitoring, and ensuring data quality across the platform. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/data-management/data-quality-api.md","sourceDirName":"ai-architecture/tools/old/api/data-management","slug":"/ai-architecture/tools/old/api/data-management/data-quality-api","permalink":"/docs/ai-architecture/tools/old/api/data-management/data-quality-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/data-management/data-quality-api.md","tags":[],"version":"current","frontMatter":{"id":"data-quality-api","title":"Data Quality API Documentation","sidebar_label":"Data Quality API"},"sidebar":"tutorialSidebar","previous":{"title":"Data Management","permalink":"/docs/ai-architecture/tools/old/api/data-management/"},"next":{"title":"Feature Store API","permalink":"/docs/ai-architecture/tools/old/api/data-management/feature-store-api"}}');var a=i(4848),l=i(8453);const s={id:"data-quality-api",title:"Data Quality API Documentation",sidebar_label:"Data Quality API"},r="Data Quality API Documentation",d={},c=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Data Validation",id:"data-validation",level:3},{value:"Create Validation Rule",id:"create-validation-rule",level:4},{value:"Run Validation",id:"run-validation",level:4},{value:"Get Validation Results",id:"get-validation-results",level:4},{value:"Data Profiling",id:"data-profiling",level:3},{value:"Run Data Profile",id:"run-data-profile",level:4},{value:"Get Profile Results",id:"get-profile-results",level:4},{value:"Data Quality Metrics",id:"data-quality-metrics",level:3},{value:"Get Quality Metrics",id:"get-quality-metrics",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,l.R)(),...e.components};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.header,{children:(0,a.jsx)(n.h1,{id:"data-quality-api-documentation",children:"Data Quality API Documentation"})}),"\n",(0,a.jsx)(n.p,{children:"The Data Quality API provides endpoints for validating, monitoring, and ensuring data quality across the platform. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,a.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"https://data-quality.91.life/api/v1\n"})}),"\n",(0,a.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,a.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,a.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,a.jsx)(n.h3,{id:"data-validation",children:"Data Validation"}),"\n",(0,a.jsx)(n.h4,{id:"create-validation-rule",children:"Create Validation Rule"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"POST /rules\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "name": "customer_data_validation",\n    "description": "Validate customer data quality",\n    "dataset": "customers",\n    "rules": [\n        {\n            "name": "email_format",\n            "type": "regex",\n            "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$",\n            "column": "email",\n            "severity": "error"\n        },\n        {\n            "name": "age_range",\n            "type": "range",\n            "min": 18,\n            "max": 120,\n            "column": "age",\n            "severity": "warning"\n        },\n        {\n            "name": "required_fields",\n            "type": "not_null",\n            "columns": ["id", "name", "email"],\n            "severity": "error"\n        }\n    ],\n    "schedule": {\n        "frequency": "daily",\n        "time": "00:00:00Z"\n    },\n    "notifications": {\n        "channels": ["slack", "email"],\n        "recipients": ["<EMAIL>"]\n    }\n}\n'})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "rule_id": "rule_001",\n    "name": "customer_data_validation",\n    "status": "active",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,a.jsx)(n.h4,{id:"run-validation",children:"Run Validation"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"POST /validations\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "rule_id": "rule_001",\n    "dataset": "customers",\n    "timestamp": "2024-03-20T10:00:00Z",\n    "options": {\n        "sample_size": 1000,\n        "parallel_processing": true\n    }\n}\n'})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "validation_id": "validation_001",\n    "rule_id": "rule_001",\n    "status": "running",\n    "started_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,a.jsx)(n.h4,{id:"get-validation-results",children:"Get Validation Results"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"GET /validations/{validation_id}\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "validation_id": "validation_001",\n    "rule_id": "rule_001",\n    "status": "completed",\n    "started_at": "2024-03-20T10:00:00Z",\n    "completed_at": "2024-03-20T10:05:00Z",\n    "results": {\n        "total_records": 1000,\n        "valid_records": 980,\n        "invalid_records": 20,\n        "violations": [\n            {\n                "rule": "email_format",\n                "severity": "error",\n                "count": 15,\n                "examples": [\n                    {\n                        "record_id": "123",\n                        "value": "invalid-email",\n                        "expected": "valid email format"\n                    }\n                ]\n            },\n            {\n                "rule": "age_range",\n                "severity": "warning",\n                "count": 5,\n                "examples": [\n                    {\n                        "record_id": "456",\n                        "value": 150,\n                        "expected": "age between 18 and 120"\n                    }\n                ]\n            }\n        ]\n    }\n}\n'})}),"\n",(0,a.jsx)(n.h3,{id:"data-profiling",children:"Data Profiling"}),"\n",(0,a.jsx)(n.h4,{id:"run-data-profile",children:"Run Data Profile"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"POST /profiles\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "dataset": "customers",\n    "columns": ["id", "name", "email", "age", "address"],\n    "metrics": [\n        "completeness",\n        "uniqueness",\n        "distribution",\n        "statistics"\n    ],\n    "options": {\n        "sample_size": 1000,\n        "include_correlations": true\n    }\n}\n'})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "profile_id": "profile_001",\n    "status": "running",\n    "started_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,a.jsx)(n.h4,{id:"get-profile-results",children:"Get Profile Results"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"GET /profiles/{profile_id}\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "profile_id": "profile_001",\n    "status": "completed",\n    "started_at": "2024-03-20T10:00:00Z",\n    "completed_at": "2024-03-20T10:10:00Z",\n    "results": {\n        "columns": {\n            "id": {\n                "completeness": 1.0,\n                "uniqueness": 1.0,\n                "null_count": 0,\n                "distinct_count": 1000\n            },\n            "email": {\n                "completeness": 0.98,\n                "uniqueness": 0.99,\n                "null_count": 20,\n                "distinct_count": 990,\n                "format_validity": 0.95\n            },\n            "age": {\n                "completeness": 0.99,\n                "null_count": 10,\n                "min": 18,\n                "max": 85,\n                "mean": 35.5,\n                "std_dev": 12.3,\n                "distribution": {\n                    "bins": [18, 30, 40, 50, 60, 85],\n                    "counts": [200, 300, 250, 150, 100]\n                }\n            }\n        },\n        "correlations": {\n            "age_income": 0.75,\n            "education_income": 0.65\n        }\n    }\n}\n'})}),"\n",(0,a.jsx)(n.h3,{id:"data-quality-metrics",children:"Data Quality Metrics"}),"\n",(0,a.jsx)(n.h4,{id:"get-quality-metrics",children:"Get Quality Metrics"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"GET /metrics/{dataset}\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"start_time"})," (optional): Start time for metrics"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"end_time"})," (optional): End time for metrics"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"metrics"})," (optional): Comma-separated list of metrics"]}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "dataset": "customers",\n    "metrics": [\n        {\n            "timestamp": "2024-03-20T10:00:00Z",\n            "completeness": 0.98,\n            "accuracy": 0.95,\n            "consistency": 0.97,\n            "timeliness": 0.99,\n            "uniqueness": 0.96\n        }\n    ],\n    "trends": {\n        "completeness": {\n            "trend": "stable",\n            "change": 0.01\n        }\n    }\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,a.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-python",children:'from data_quality import Client\n\n# Initialize client\nclient = Client(\n    host="https://data-quality.91.life",\n    auth_token="your-token"\n)\n\n# Create validation rule\nrule = {\n    "name": "customer_data_validation",\n    "description": "Validate customer data quality",\n    "dataset": "customers",\n    "rules": [\n        {\n            "name": "email_format",\n            "type": "regex",\n            "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$",\n            "column": "email",\n            "severity": "error"\n        }\n    ]\n}\nrule_id = client.create_rule(rule)\n\n# Run validation\nvalidation = client.run_validation(\n    rule_id=rule_id,\n    dataset="customers"\n)\nresults = client.get_validation_results(validation["validation_id"])\nprint(results)\n'})}),"\n",(0,a.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-bash",children:'# Create validation rule\ncurl -X POST https://data-quality.91.life/api/v1/rules \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "customer_data_validation",\n    "description": "Validate customer data quality",\n    "dataset": "customers",\n    "rules": [\n      {\n        "name": "email_format",\n        "type": "regex",\n        "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$",\n        "column": "email",\n        "severity": "error"\n      }\n    ]\n  }\'\n\n# Run validation\ncurl -X POST https://data-quality.91.life/api/v1/validations \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "rule_id": "rule_001",\n    "dataset": "customers",\n    "timestamp": "2024-03-20T10:00:00Z"\n  }\'\n'})}),"\n",(0,a.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,a.jsxs)(n.table,{children:[(0,a.jsx)(n.thead,{children:(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.th,{children:"Code"}),(0,a.jsx)(n.th,{children:"Description"})]})}),(0,a.jsxs)(n.tbody,{children:[(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"400"}),(0,a.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"401"}),(0,a.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"403"}),(0,a.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"404"}),(0,a.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"409"}),(0,a.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"429"}),(0,a.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"500"}),(0,a.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,a.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,a.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,a.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,a.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,a.jsxs)(n.ol,{children:["\n",(0,a.jsxs)(n.li,{children:["\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Validation Rules"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Define clear validation criteria"}),"\n",(0,a.jsx)(n.li,{children:"Use appropriate severity levels"}),"\n",(0,a.jsx)(n.li,{children:"Include helpful error messages"}),"\n"]}),"\n"]}),"\n",(0,a.jsxs)(n.li,{children:["\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Data Profiling"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Profile data regularly"}),"\n",(0,a.jsx)(n.li,{children:"Monitor data distributions"}),"\n",(0,a.jsx)(n.li,{children:"Track data quality trends"}),"\n"]}),"\n"]}),"\n",(0,a.jsxs)(n.li,{children:["\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Quality Metrics"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Set quality thresholds"}),"\n",(0,a.jsx)(n.li,{children:"Monitor metric trends"}),"\n",(0,a.jsx)(n.li,{children:"Set up alerts for issues"}),"\n"]}),"\n"]}),"\n",(0,a.jsxs)(n.li,{children:["\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Performance"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Use sampling for large datasets"}),"\n",(0,a.jsx)(n.li,{children:"Enable parallel processing"}),"\n",(0,a.jsx)(n.li,{children:"Cache profile results"}),"\n"]}),"\n"]}),"\n"]})]})}function u(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,a.jsx)(n,{...e,children:(0,a.jsx)(o,{...e})}):o(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>s,x:()=>r});var t=i(6540);const a={},l=t.createContext(a);function s(e){const n=t.useContext(l);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function r(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(a):e.components||a:s(e.components),t.createElement(l.Provider,{value:n},e.children)}}}]);