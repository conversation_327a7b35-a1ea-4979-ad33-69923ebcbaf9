"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7874],{8453:(e,t,a)=>{a.d(t,{R:()=>i,x:()=>r});var n=a(6540);const s={},d=n.createContext(s);function i(e){const t=n.useContext(d);return n.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function r(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:i(e.components),n.createElement(d.Provider,{value:t},e.children)}},8738:(e,t,a)=>{a.r(t),a.d(t,{assets:()=>l,contentTitle:()=>r,default:()=>h,frontMatter:()=>i,metadata:()=>n,toc:()=>c});const n=JSON.parse('{"id":"ai-architecture/api/data/management","title":"Data Management","description":"Manage and organize your datasets effectively.","source":"@site/docs/ai-architecture/api/data/management.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/management","permalink":"/docs/ai-architecture/api/data/management","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/management.md","tags":[],"version":"current","sidebarPosition":2,"frontMatter":{"sidebar_position":2},"sidebar":"tutorialSidebar","previous":{"title":"Data Upload","permalink":"/docs/ai-architecture/api/data/upload"},"next":{"title":"Data Processing","permalink":"/docs/ai-architecture/api/data/processing"}}');var s=a(4848),d=a(8453);const i={sidebar_position:2},r="Data Management",l={},c=[{value:"List Datasets",id:"list-datasets",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Dataset Details",id:"get-dataset-details",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Update Dataset",id:"update-dataset",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Delete Dataset",id:"delete-dataset",level:2},{value:"Endpoint",id:"endpoint-3",level:3},{value:"Dataset Status",id:"dataset-status",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Best Practices",id:"best-practices",level:2}];function o(e){const t={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,d.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.header,{children:(0,s.jsx)(t.h1,{id:"data-management",children:"Data Management"})}),"\n",(0,s.jsx)(t.p,{children:"Manage and organize your datasets effectively."}),"\n",(0,s.jsx)(t.h2,{id:"list-datasets",children:"List Datasets"}),"\n",(0,s.jsx)(t.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{children:"GET /v1/data/datasets\n"})}),"\n",(0,s.jsx)(t.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,s.jsxs)(t.table,{children:[(0,s.jsx)(t.thead,{children:(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.th,{children:"Parameter"}),(0,s.jsx)(t.th,{children:"Type"}),(0,s.jsx)(t.th,{children:"Description"})]})}),(0,s.jsxs)(t.tbody,{children:[(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{children:"page"}),(0,s.jsx)(t.td,{children:"integer"}),(0,s.jsx)(t.td,{children:"Page number (default: 1)"})]}),(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{children:"limit"}),(0,s.jsx)(t.td,{children:"integer"}),(0,s.jsx)(t.td,{children:"Items per page (default: 10)"})]}),(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{children:"type"}),(0,s.jsx)(t.td,{children:"string"}),(0,s.jsx)(t.td,{children:"Filter by data type"})]}),(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{children:"status"}),(0,s.jsx)(t.td,{children:"string"}),(0,s.jsx)(t.td,{children:"Filter by status"})]})]})]}),"\n",(0,s.jsx)(t.h3,{id:"example-response",children:"Example Response"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "datasets": [\n      {\n        "id": "dataset_123",\n        "name": "training_dataset",\n        "type": "text",\n        "format": "json",\n        "size": "1.2GB",\n        "status": "ready",\n        "created_at": "2024-03-14T12:00:00Z",\n        "updated_at": "2024-03-14T12:00:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 100,\n      "page": 1,\n      "limit": 10,\n      "pages": 10\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,s.jsx)(t.h2,{id:"get-dataset-details",children:"Get Dataset Details"}),"\n",(0,s.jsx)(t.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{children:"GET /v1/data/datasets/{dataset_id}\n"})}),"\n",(0,s.jsx)(t.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "dataset": {\n      "id": "dataset_123",\n      "name": "training_dataset",\n      "type": "text",\n      "format": "json",\n      "size": "1.2GB",\n      "status": "ready",\n      "created_at": "2024-03-14T12:00:00Z",\n      "updated_at": "2024-03-14T12:00:00Z",\n      "metadata": {\n        "source": "customer_feedback",\n        "language": "en"\n      },\n      "statistics": {\n        "total_samples": 10000,\n        "unique_labels": 5,\n        "average_length": 150\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,s.jsx)(t.h2,{id:"update-dataset",children:"Update Dataset"}),"\n",(0,s.jsx)(t.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{children:"PATCH /v1/data/datasets/{dataset_id}\n"})}),"\n",(0,s.jsx)(t.h3,{id:"request-body",children:"Request Body"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{className:"language-json",children:'{\n  "name": "updated_dataset_name",\n  "description": "Updated description",\n  "metadata": {\n    "source": "new_source",\n    "language": "fr"\n  }\n}\n'})}),"\n",(0,s.jsx)(t.h2,{id:"delete-dataset",children:"Delete Dataset"}),"\n",(0,s.jsx)(t.h3,{id:"endpoint-3",children:"Endpoint"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{children:"DELETE /v1/data/datasets/{dataset_id}\n"})}),"\n",(0,s.jsx)(t.h2,{id:"dataset-status",children:"Dataset Status"}),"\n",(0,s.jsxs)(t.table,{children:[(0,s.jsx)(t.thead,{children:(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.th,{children:"Status"}),(0,s.jsx)(t.th,{children:"Description"})]})}),(0,s.jsxs)(t.tbody,{children:[(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{children:"processing"}),(0,s.jsx)(t.td,{children:"Dataset is being processed"})]}),(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{children:"ready"}),(0,s.jsx)(t.td,{children:"Dataset is ready for use"})]}),(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{children:"failed"}),(0,s.jsx)(t.td,{children:"Processing failed"})]}),(0,s.jsxs)(t.tr,{children:[(0,s.jsx)(t.td,{children:"archived"}),(0,s.jsx)(t.td,{children:"Dataset is archived"})]})]})]}),"\n",(0,s.jsx)(t.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,s.jsx)(t.h3,{id:"python",children:"Python"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# List datasets\ndatasets = client.data.list_datasets(\n    page=1,\n    limit=10,\n    type="text"\n)\n\n# Get dataset details\ndataset = client.data.get_dataset("dataset_123")\n\n# Update dataset\nupdated = client.data.update_dataset(\n    "dataset_123",\n    name="updated_name",\n    description="New description"\n)\n\n# Delete dataset\nclient.data.delete_dataset("dataset_123")\n'})}),"\n",(0,s.jsx)(t.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// List datasets\nconst datasets = await client.data.listDatasets({\n  page: 1,\n  limit: 10,\n  type: 'text'\n});\n\n// Get dataset details\nconst dataset = await client.data.getDataset('dataset_123');\n\n// Update dataset\nconst updated = await client.data.updateDataset('dataset_123', {\n  name: 'updated_name',\n  description: 'New description'\n});\n\n// Delete dataset\nawait client.data.deleteDataset('dataset_123');\n"})}),"\n",(0,s.jsx)(t.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,s.jsxs)(t.ol,{children:["\n",(0,s.jsx)(t.li,{children:"Use meaningful dataset names"}),"\n",(0,s.jsx)(t.li,{children:"Keep metadata up to date"}),"\n",(0,s.jsx)(t.li,{children:"Monitor dataset status"}),"\n",(0,s.jsx)(t.li,{children:"Archive unused datasets"}),"\n",(0,s.jsx)(t.li,{children:"Regular data validation"}),"\n",(0,s.jsx)(t.li,{children:"Implement version control"}),"\n",(0,s.jsx)(t.li,{children:"Document data lineage"}),"\n",(0,s.jsx)(t.li,{children:"Set up access controls"}),"\n"]})]})}function h(e={}){const{wrapper:t}={...(0,d.R)(),...e.components};return t?(0,s.jsx)(t,{...e,children:(0,s.jsx)(o,{...e})}):o(e)}}}]);