"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6747],{6322:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>a,default:()=>h,frontMatter:()=>l,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"architecture/architecture_principles","title":"Architecture Principles","description":"This document outlines the core architectural principles that must guide all development and design of our SaaS platform. These principles are fundamental to maintaining a robust, maintainable, and scalable system.","source":"@site/docs/architecture/architecture_principles.md","sourceDirName":"architecture","slug":"/architecture/architecture_principles","permalink":"/docs/architecture/architecture_principles","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/architecture/architecture_principles.md","tags":[],"version":"current","frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"Initial Architecture","permalink":"/docs/architecture/Initial Architecture"},"next":{"title":"Decision Records","permalink":"/docs/category/decision-records"}}');var s=i(4848),t=i(8453);const l={},a="Architecture Principles",o={},c=[{value:"1. Development Process and Tooling",id:"1-development-process-and-tooling",level:2},{value:"1.1 JIRA-Driven Development",id:"11-jira-driven-development",level:3},{value:"1.2 You Professional Software Engineers ... not Hackers",id:"12-you-professional-software-engineers--not-hackers",level:3},{value:"1.3 Open Source and License Compliance",id:"13-open-source-and-license-compliance",level:3},{value:"1.4 Framework Selection and Customization",id:"14-framework-selection-and-customization",level:3},{value:"2. Design Principles",id:"2-design-principles",level:2},{value:"2.1 KISS (Keep It Simple, Stupid)",id:"21-kiss-keep-it-simple-stupid",level:3},{value:"2.2 Separation of Concerns",id:"22-separation-of-concerns",level:3},{value:"2.3 The Best is the Enemy of the Good",id:"23-the-best-is-the-enemy-of-the-good",level:3},{value:"2.4 Early Optimization is the Root of All Evil",id:"24-early-optimization-is-the-root-of-all-evil",level:3},{value:"2.5 Minimize External Dependencies",id:"25-minimize-external-dependencies",level:3},{value:"2.6 Problem-First Approach",id:"26-problem-first-approach",level:3},{value:"2.7 Pay Now or Pay More",id:"27-pay-now-or-pay-more",level:3},{value:"2.8 Automation First",id:"28-automation-first",level:3},{value:"2.9 Clear Naming",id:"29-clear-naming",level:3},{value:"3. Service Architecture",id:"3-service-architecture",level:2},{value:"3.1 Treat Everything as Data",id:"31-treat-everything-as-data",level:3},{value:"3.2 Service Design Principles",id:"32-service-design-principles",level:3},{value:"3.3 Component Replaceability",id:"33-component-replaceability",level:3},{value:"4. Development Standards",id:"4-development-standards",level:2},{value:"4.1 Only use Supported Languages",id:"41-only-use-supported-languages",level:3},{value:"4.2 Documentation and Testing for Everything",id:"42-documentation-and-testing-for-everything",level:3},{value:"4.3 Everything as Code",id:"43-everything-as-code",level:3},{value:"4.4 Data Lineage Everywhere",id:"44-data-lineage-everywhere",level:3},{value:"4.5 MCP Everywhere",id:"45-mcp-everywhere",level:3},{value:"4.6 use Staged Event Driven Architecture",id:"46-use-staged-event-driven-architecture",level:3},{value:"4.7 Command Replay and State Reconstruction",id:"47-command-replay-and-state-reconstruction",level:3},{value:"4.9 Infrastructure Agnostic Execution",id:"49-infrastructure-agnostic-execution",level:3},{value:"4.10 Guarantee Version Coexistence",id:"410-guarantee-version-coexistence",level:3},{value:"5. API Design",id:"5-api-design",level:2},{value:"5.1 Standard API Fa\xe7ades",id:"51-standard-api-fa\xe7ades",level:3},{value:"5.2 SDK Design Principles",id:"52-sdk-design-principles",level:3}];function d(e){const n={h1:"h1",h2:"h2",h3:"h3",header:"header",img:"img",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"architecture-principles",children:"Architecture Principles"})}),"\n",(0,s.jsx)(n.p,{children:"This document outlines the core architectural principles that must guide all development and design of our SaaS platform. These principles are fundamental to maintaining a robust, maintainable, and scalable system."}),"\n",(0,s.jsx)(n.h2,{id:"1-development-process-and-tooling",children:"1. Development Process and Tooling"}),"\n",(0,s.jsx)(n.h3,{id:"11-jira-driven-development",children:"1.1 JIRA-Driven Development"}),"\n",(0,s.jsx)(n.p,{children:"Every development task must be tracked through JIRA tickets. This ensures:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Clear traceability of changes"}),"\n",(0,s.jsx)(n.li,{children:"Proper documentation of requirements and decisions"}),"\n",(0,s.jsx)(n.li,{children:"Consistent project management"}),"\n",(0,s.jsx)(n.li,{children:"Ability to track progress and dependencies"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"12-you-professional-software-engineers--not-hackers",children:"1.2 You Professional Software Engineers ... not Hackers"}),"\n",(0,s.jsx)(n.p,{children:"We are software engineers, not hackers:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Write production-grade, maintainable code"}),"\n",(0,s.jsx)(n.li,{children:"Follow established software engineering practices"}),"\n",(0,s.jsx)(n.li,{children:"Document design decisions and code"}),"\n",(0,s.jsx)(n.li,{children:"Write comprehensive tests"}),"\n",(0,s.jsx)(n.li,{children:"Consider security, performance, and scalability"}),"\n",(0,s.jsx)(n.li,{children:"Use proper version control and branching strategies"}),"\n",(0,s.jsx)(n.li,{children:"Follow code review processes"}),"\n",(0,s.jsx)(n.li,{children:"Consider long-term maintenance and support"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"13-open-source-and-license-compliance",children:"1.3 Open Source and License Compliance"}),"\n",(0,s.jsx)(n.p,{children:"All frameworks and tools used within our software must be Open Source with liberal licenses (EPL, APL, MIT, BSD, AGPL, GPL). This ensures:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Freedom to modify and customize"}),"\n",(0,s.jsx)(n.li,{children:"No vendor lock-in"}),"\n",(0,s.jsx)(n.li,{children:"Community support and transparency"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"14-framework-selection-and-customization",children:"1.4 Framework Selection and Customization"}),"\n",(0,s.jsx)(n.p,{children:"We must maintain a balance between using existing solutions and custom development:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Critical frameworks (Huly, Dapr, Watermill, Marquez) must be debuggable, maintainable, and customizable"}),"\n",(0,s.jsx)(n.li,{children:"We can reuse parts of software but not entire platforms"}),"\n",(0,s.jsx)(n.li,{children:"Every framework/tool requires an Architecture Decision Record (ADR) and approval from the architecture group"}),"\n"]}),"\n",(0,s.jsx)(n.img,{src:"https://www.plantuml.com/plantuml/png/TP6nRiCm34HtVGMdeXto2qMC62WIe8kJBV0WAwmZH29LcNAGlozT9q1Tj1llxa57IUj1R9OIWnakdfBDDeBZc9YGP2-tW93HYQK-e3776aSCdAA6qE1dmK5QMArUuRZRDXKSlBkWj6QXvhog6zXajEAP55Z8Ev7TsOkMbOnw-EZnEe5_k7S-fSoZ3HFq6ETa6rF0QST7sozZfcfsvIUqr3Dpq0jwwbjZY4MVmlTd0VxuPrc0zp88T0ZsUo4yGNTJTm6_PIv9pUSgT9RW2NPewSKWdM3gC6kTz3--0000"}),"\n",(0,s.jsx)(n.h2,{id:"2-design-principles",children:"2. Design Principles"}),"\n",(0,s.jsx)(n.h3,{id:"21-kiss-keep-it-simple-stupid",children:"2.1 KISS (Keep It Simple, Stupid)"}),"\n",(0,s.jsx)(n.p,{children:"Complexity should be avoided whenever possible:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Prefer simple, straightforward solutions over complex ones"}),"\n",(0,s.jsx)(n.li,{children:"Focus on maintainability and readability"}),"\n",(0,s.jsx)(n.li,{children:"Document the reasoning behind any necessary complexity"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"22-separation-of-concerns",children:"2.2 Separation of Concerns"}),"\n",(0,s.jsx)(n.p,{children:"Apply separation of concerns everywhere:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Use clear boundaries between components"}),"\n",(0,s.jsx)(n.li,{children:"Keep domain logic isolated from technical implementation details for cleaner architecture"}),"\n",(0,s.jsx)(n.li,{children:"Isolate different responsibilities into distinct modules"}),"\n",(0,s.jsx)(n.li,{children:"Keep configuration separate from code"}),"\n",(0,s.jsx)(n.li,{children:"Separate deployment concerns from application logic"}),"\n",(0,s.jsx)(n.li,{children:"Maintain clear distinction between public and internal APIs"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"23-the-best-is-the-enemy-of-the-good",children:"2.3 The Best is the Enemy of the Good"}),"\n",(0,s.jsx)(n.p,{children:"Striving for perfection can prevent progress:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Focus on delivering working solutions that meet requirements"}),"\n",(0,s.jsx)(n.li,{children:"Avoid over-engineering and excessive refinement"}),"\n",(0,s.jsx)(n.li,{children:"Prioritize practical solutions over theoretical perfection"}),"\n",(0,s.jsx)(n.li,{children:"Embrace iterative improvement over perfect first attempts"}),"\n",(0,s.jsx)(n.li,{children:'Remember that "good enough" is often better than "perfect but late"'}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"24-early-optimization-is-the-root-of-all-evil",children:"2.4 Early Optimization is the Root of All Evil"}),"\n",(0,s.jsx)(n.p,{children:"Premature optimization can lead to problems:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Focus on correctness and clarity first"}),"\n",(0,s.jsx)(n.li,{children:"Optimize only after identifying actual bottlenecks"}),"\n",(0,s.jsx)(n.li,{children:"Measure before optimizing"}),"\n",(0,s.jsx)(n.li,{children:"Avoid complex optimizations that make code harder to maintain"}),"\n",(0,s.jsx)(n.li,{children:"Let performance requirements drive optimization decisions"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"25-minimize-external-dependencies",children:"2.5 Minimize External Dependencies"}),"\n",(0,s.jsx)(n.p,{children:"Minimize dependency on external services and tools:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Maintain control over critical functionality"}),"\n",(0,s.jsx)(n.li,{children:"External libraries often include more functionality than needed, introducing unnecessary bloat"}),"\n",(0,s.jsx)(n.li,{children:"Reduce the risk of being forced into disruptive upgrades"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"26-problem-first-approach",children:"2.6 Problem-First Approach"}),"\n",(0,s.jsx)(n.p,{children:"Before implementing any solution:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Analyze the problem thoroughly"}),"\n",(0,s.jsx)(n.li,{children:"Identify gaps in current stack"}),"\n",(0,s.jsx)(n.li,{children:"Evaluate build vs. buy options"}),"\n",(0,s.jsx)(n.li,{children:"Consider hybrid approaches"}),"\n",(0,s.jsx)(n.li,{children:"Document the decision-making process (ADR)"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"27-pay-now-or-pay-more",children:"2.7 Pay Now or Pay More"}),"\n",(0,s.jsx)(n.p,{children:"Not investing early in good design will increase technical debt, reduce agility, and cost significantly more to undo later. This principle emphasizes the importance of proper upfront investment in architecture and design decisions."}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Conduct thorough design reviews before implementation"}),"\n",(0,s.jsx)(n.li,{children:"Document architectural decisions and their rationale"}),"\n",(0,s.jsx)(n.li,{children:"Consider long-term implications of design choices"}),"\n",(0,s.jsx)(n.li,{children:"Balance immediate needs with future scalability"}),"\n",(0,s.jsx)(n.li,{children:"Invest in automated testing and quality assurance"}),"\n",(0,s.jsx)(n.li,{children:"Regular architecture reviews to identify potential"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"28-automation-first",children:"2.8 Automation First"}),"\n",(0,s.jsx)(n.p,{children:"What is not automated is a huge cost to the company. Strive for 100% automation of all tasks, especially those pertaining to platform runtime maintenance, deployment, upgrade, and onboarding."}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Automate all deployment and release processes"}),"\n",(0,s.jsx)(n.li,{children:"Implement self-healing and auto-recovery mechanisms"}),"\n",(0,s.jsx)(n.li,{children:"Automate platform monitoring and alerting"}),"\n",(0,s.jsx)(n.li,{children:"Create automated onboarding and provisioning workflows"}),"\n",(0,s.jsx)(n.li,{children:"Implement automated testing at all levels"}),"\n",(0,s.jsx)(n.li,{children:"Automate documentation generation and updates"}),"\n",(0,s.jsx)(n.li,{children:"Establish automated compliance and security checks"}),"\n",(0,s.jsx)(n.li,{children:"Create automated backup and disaster recovery procedures"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"29-clear-naming",children:"2.9 Clear Naming"}),"\n",(0,s.jsx)(n.p,{children:"Badly naming things only adds misery to the world. Clear, consistent, and meaningful names are essential for maintainable and understandable code."}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Use descriptive and self-explanatory names for all components"}),"\n",(0,s.jsx)(n.li,{children:"Follow consistent naming conventions across the platform"}),"\n",(0,s.jsx)(n.li,{children:"Avoid abbreviations unless universally understood"}),"\n",(0,s.jsx)(n.li,{children:"Use domain-specific terminology appropriately"}),"\n",(0,s.jsx)(n.li,{children:"Maintain naming consistency across related components"}),"\n",(0,s.jsx)(n.li,{children:"Document naming conventions and patterns"}),"\n",(0,s.jsx)(n.li,{children:"Review and refactor unclear names during code reviews"}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"3-service-architecture",children:"3. Service Architecture"}),"\n",(0,s.jsx)(n.h3,{id:"31-treat-everything-as-data",children:"3.1 Treat Everything as Data"}),"\n",(0,s.jsx)(n.p,{children:"All platform elements must be treated as data:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Code as data"}),"\n",(0,s.jsx)(n.li,{children:"Models as data"}),"\n",(0,s.jsx)(n.li,{children:"Documents as data"}),"\n",(0,s.jsx)(n.li,{children:"Configuration as data"}),"\n",(0,s.jsx)(n.li,{children:"Metadata for everything"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"32-service-design-principles",children:"3.2 Service Design Principles"}),"\n",(0,s.jsx)(n.p,{children:"All services must follow these principles:"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Mandatory Dapr Integration"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:'All services must be "Daprized"'}),"\n",(0,s.jsx)(n.li,{children:"Leverage Dapr building blocks"}),"\n",(0,s.jsx)(n.li,{children:"Ensure consistent service-to-service communication"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Systematic Interface-Driven Development"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Design services around clear interfaces"}),"\n",(0,s.jsx)(n.li,{children:"Hide implementation details"}),"\n",(0,s.jsx)(n.li,{children:"Focus on API contracts"}),"\n",(0,s.jsx)(n.li,{children:"Make API's acceptance a milestone before progressing on implementation"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Systematic Command-Based Service Design"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Design services around commands they execute"}),"\n",(0,s.jsx)(n.li,{children:"Commands will uniquely define the API that the service offers be it REST, gRPC, MCP, GraphQl etc..."}),"\n",(0,s.jsx)(n.li,{children:"Clear separation of concerns"}),"\n",(0,s.jsx)(n.li,{children:"Event-driven architecture justify when not appropriate"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"Keep Transverse Concerns ... Transverse"}),"\nFocus on your business logic and implement those concerns through injection by the platform mechanisms through its SDKs:"]}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"I/O operations"}),"\n",(0,s.jsx)(n.li,{children:"Configuration management"}),"\n",(0,s.jsx)(n.li,{children:"Transaction management"}),"\n",(0,s.jsx)(n.li,{children:"Security"}),"\n",(0,s.jsx)(n.li,{children:"Entitlement"}),"\n",(0,s.jsx)(n.li,{children:"DataLineage"}),"\n",(0,s.jsx)(n.li,{children:"Monitoring"}),"\n",(0,s.jsx)(n.li,{children:"Session Context"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"33-component-replaceability",children:"3.3 Component Replaceability"}),"\n",(0,s.jsx)(n.p,{children:"Switching components, frameworks, or tools should be a trivial task:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Design services with clear boundaries and interfaces"}),"\n",(0,s.jsx)(n.li,{children:"Use dependency injection and inversion of control"}),"\n",(0,s.jsx)(n.li,{children:"Implement adapter patterns for external dependencies"}),"\n",(0,s.jsx)(n.li,{children:"Keep framework-specific code isolated"}),"\n",(0,s.jsx)(n.li,{children:"Document integration points and dependencies"}),"\n",(0,s.jsx)(n.li,{children:"Use abstraction layers to decouple from specific implementations"}),"\n"]}),"\n",(0,s.jsx)(n.img,{src:"https://www.plantuml.com/plantuml/png/RLBBQiCm4BphAvRSG_-Wf3Y5659oi6-b1w5k715fPQHrY2dzzwuUAJtqOcPdv9rPhNQ9TUJ5EvMkbcOTjGSJ_1m8YGTU7K94mvecXqhDsbprXB0PCDwiGJZg5UC6lXJ0-uFhYJ6UjS4Fq0c6FcjTy5xJ9npaup0MfJjaPOoQqWrZGkW26OoKYZfswlkFuJ_7cct_T2e_zATgH6SxBL6p3LHj2lDhfKrMciUzvZPBjBpMk6WozqfisQ6NIoYqr9dUQzP7IoYH2hq_PlOjaEKGBKs5bBAsJKcSiZ_ZlROPJ1vgw66xVSxpTuU2PDQnAqY6QAZ5RfKaRIW7R327Qb22D9JT7qUDKXHODcYd2qCuPoE05-SWrLqby4jYi7wkiuhA5vGxDksECqWsiMXkLIWRp0tLJf2ycHy0"}),"\n",(0,s.jsx)(n.h2,{id:"4-development-standards",children:"4. Development Standards"}),"\n",(0,s.jsx)(n.h3,{id:"41-only-use-supported-languages",children:"4.1 Only use Supported Languages"}),"\n",(0,s.jsx)(n.p,{children:"Development is restricted to:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Go"}),"\n",(0,s.jsx)(n.li,{children:"Python"}),"\n",(0,s.jsx)(n.li,{children:"TypeScript/JavaScript"}),"\n",(0,s.jsx)(n.li,{children:"Java"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"42-documentation-and-testing-for-everything",children:"4.2 Documentation and Testing for Everything"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"All activities (from requirements down to deployment) must be documented"}),"\n",(0,s.jsx)(n.li,{children:"Comprehensive test coverage"}),"\n",(0,s.jsx)(n.li,{children:"Documentation must also be versioned in Git"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"43-everything-as-code",children:"4.3 Everything as Code"}),"\n",(0,s.jsx)(n.p,{children:"Following Rex's EaaC principle:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Version control for all artifacts"}),"\n",(0,s.jsx)(n.li,{children:"Use Markdown for documentation"}),"\n",(0,s.jsx)(n.li,{children:"Use PlantUML for diagrams"}),"\n",(0,s.jsx)(n.li,{children:"Use Terraform for infrastructure"}),"\n",(0,s.jsx)(n.li,{children:"LLM Rules as Code"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"44-data-lineage-everywhere",children:"4.4 Data Lineage Everywhere"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Track data flow through the system using the SDk - not doing it must be justified and approved by architecture"}),"\n",(0,s.jsx)(n.li,{children:"Document data transformations"}),"\n",(0,s.jsx)(n.li,{children:"Maintain audit trails"}),"\n",(0,s.jsx)(n.li,{children:"Enable data quality monitoring"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"45-mcp-everywhere",children:"4.5 MCP Everywhere"}),"\n",(0,s.jsx)(n.p,{children:"All services must be compatible with the Model Context Protocol (MCP):"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Enable service activation and interaction through LLMs"}),"\n",(0,s.jsx)(n.li,{children:"Provide standardized context and metadata for LLM understanding"}),"\n",(0,s.jsx)(n.li,{children:"Maintain proper context preservation across LLM interactions"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"46-use-staged-event-driven-architecture",children:"4.6 use Staged Event Driven Architecture"}),"\n",(0,s.jsx)(n.p,{children:"The platform must implement Staged Event-Driven Architecture (SEDA):"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Break complex processes into stages"}),"\n",(0,s.jsx)(n.li,{children:"Enable better resource utilization"}),"\n",(0,s.jsx)(n.li,{children:"Improve system scalability"}),"\n",(0,s.jsx)(n.li,{children:"Support asynchronous processing"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"47-command-replay-and-state-reconstruction",children:"4.7 Command Replay and State Reconstruction"}),"\n",(0,s.jsx)(n.p,{children:"The system must support complete state reconstruction through command replay:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"All commands entering the system must be persisted"}),"\n",(0,s.jsx)(n.li,{children:"Commands must be immutable and versioned"}),"\n",(0,s.jsx)(n.li,{children:"State must be deterministically reconstructible"}),"\n",(0,s.jsx)(n.li,{children:"Support point-in-time state reconstruction"}),"\n",(0,s.jsx)(n.li,{children:"Enable system recovery through command replay"}),"\n",(0,s.jsx)(n.li,{children:"Maintain command ordering and causality"}),"\n",(0,s.jsx)(n.li,{children:"Enable system testing through command replay"}),"\n",(0,s.jsx)(n.li,{children:"Allow for system migration and upgrades through replay"}),"\n",(0,s.jsx)(n.li,{children:"Support disaster recovery scenarios"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"49-infrastructure-agnostic-execution",children:"4.9 Infrastructure Agnostic Execution"}),"\n",(0,s.jsx)(n.p,{children:"The platform must be developed in an infrastructure-agnostic manner:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:["Design all mechanisms to work across different deployment scenarios:","\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"On-premise data centers"}),"\n",(0,s.jsx)(n.li,{children:"Public cloud providers (Google Cloud, AWS, Azure)"}),"\n",(0,s.jsx)(n.li,{children:"Local development environments (laptops)"}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.li,{children:"Ensure consistent behavior across all deployment environments"}),"\n",(0,s.jsx)(n.li,{children:"Design for portability from the start"}),"\n",(0,s.jsx)(n.li,{children:"Use infrastructure as code for consistent deployment"}),"\n",(0,s.jsx)(n.li,{children:"Maintain development parity between all environments"}),"\n",(0,s.jsx)(n.li,{children:"Test across all deployment scenarios"}),"\n",(0,s.jsx)(n.li,{children:"Use containerization for consistent runtime environments"}),"\n",(0,s.jsx)(n.li,{children:"Design for offline capabilities where required"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"410-guarantee-version-coexistence",children:"4.10 Guarantee Version Coexistence"}),"\n",(0,s.jsx)(n.p,{children:"The platform must support multiple versions of components running simultaneously:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Allow different versions of backend and frontend components to coexist"}),"\n",(0,s.jsx)(n.li,{children:"Support version-aware routing and service discovery"}),"\n",(0,s.jsx)(n.li,{children:"Maintain backward compatibility between versions"}),"\n",(0,s.jsx)(n.li,{children:"Support gradual migration and canary deployments"}),"\n",(0,s.jsx)(n.li,{children:"Enable client-side version selection"}),"\n",(0,s.jsx)(n.li,{children:"Maintain version-specific documentation and SDKs"}),"\n",(0,s.jsx)(n.li,{children:"Support version deprecation policies"}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"5-api-design",children:"5. API Design"}),"\n",(0,s.jsx)(n.h3,{id:"51-standard-api-fa\xe7ades",children:"5.1 Standard API Fa\xe7ades"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Start any design top down from API to Implementation"}),"\n",(0,s.jsx)(n.li,{children:"Use standard APIs as fa\xe7ades where possible"}),"\n",(0,s.jsx)(n.li,{children:"Hide implementation details"}),"\n",(0,s.jsx)(n.li,{children:"Provide consistent interfaces through commands (not doing it requires justification and approval)"}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"52-sdk-design-principles",children:"5.2 SDK Design Principles"}),"\n",(0,s.jsx)(n.p,{children:"When designing SDKs:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Focus on user experience"}),"\n",(0,s.jsx)(n.li,{children:"Hide implementation complexity"}),"\n",(0,s.jsx)(n.li,{children:"Maintain backward compatibility"}),"\n",(0,s.jsx)(n.li,{children:"Follow language-specific best practices"}),"\n"]}),"\n",(0,s.jsx)(n.img,{src:"https://www.plantuml.com/plantuml/png/NL3BQWCn3BpxAqJk-OMIaFQmjAKXIqwX1zMhx9huXUs5Bg7_NaaDDEr1Q3o3HYDlQiFIvk1DlRYOiM00cq9EaMBhs-892jc6SVHaJ4PxmP5WrJy-mmikL5PmDG0dUM_ttJjWbIvK5xAdm2xON8ggTAzxKNP9f4_N5cLtRqn_tPaVAnNOvkoTrTcx7IBfqB5_EiYGT5Yl7tGhbsBPmwLOtKrCZGznuzGWdMy9RvcXJcds0tmGSzOJHi0uG7KIqX136VvleRO0Vu_Kd_kvZBN93GD5E9SKOERSlruRHlpbtm00"})]})}function h(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(d,{...e})}):d(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>a});var r=i(6540);const s={},t=r.createContext(s);function l(e){const n=r.useContext(t);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:l(e.components),r.createElement(t.Provider,{value:n},e.children)}}}]);