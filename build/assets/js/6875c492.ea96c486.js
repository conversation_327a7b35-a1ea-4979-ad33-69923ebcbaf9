"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[4813],{665:(e,t,a)=>{a.d(t,{A:()=>c});a(6540);var n=a(4164),s=a(3452),i=a(5279),r=a(7821),l=a(4848);function o({className:e}){return(0,l.jsx)(r.A,{type:"caution",title:(0,l.jsx)(s.Rc,{}),className:(0,n.A)(e,i.G.common.unlistedBanner),children:(0,l.jsx)(s.Uh,{})})}function c(e){return(0,l.jsxs)(l.<PERSON>,{children:[(0,l.jsx)(s.AE,{}),(0,l.jsx)(o,{...e})]})}},1865:(e,t,a)=>{a.d(t,{A:()=>r});a(6540);var n=a(4164),s=a(6289),i=a(4848);function r(e){const{permalink:t,title:a,subLabel:r,isNext:l}=e;return(0,i.jsxs)(s.A,{className:(0,n.A)("pagination-nav__link",l?"pagination-nav__link--next":"pagination-nav__link--prev"),to:t,children:[r&&(0,i.jsx)("div",{className:"pagination-nav__sublabel",children:r}),(0,i.jsx)("div",{className:"pagination-nav__label",children:a})]})}},3452:(e,t,a)=>{a.d(t,{AE:()=>o,Rc:()=>r,TT:()=>d,Uh:()=>l,Yh:()=>c});a(6540);var n=a(539),s=a(7143),i=a(4848);function r(){return(0,i.jsx)(n.A,{id:"theme.contentVisibility.unlistedBanner.title",description:"The unlisted content banner title",children:"Unlisted page"})}function l(){return(0,i.jsx)(n.A,{id:"theme.contentVisibility.unlistedBanner.message",description:"The unlisted content banner message",children:"This page is unlisted. Search engines will not index it, and only users having a direct link can access it."})}function o(){return(0,i.jsx)(s.A,{children:(0,i.jsx)("meta",{name:"robots",content:"noindex, nofollow"})})}function c(){return(0,i.jsx)(n.A,{id:"theme.contentVisibility.draftBanner.title",description:"The draft content banner title",children:"Draft page"})}function d(){return(0,i.jsx)(n.A,{id:"theme.contentVisibility.draftBanner.message",description:"The draft content banner message",children:"This page is a draft. It will only be visible in dev and be excluded from the production build."})}},3953:(e,t,a)=>{a.d(t,{A:()=>l});a(6540);var n=a(4164),s=a(6289);const i={tag:"tag_zVej",tagRegular:"tagRegular_sFm0",tagWithCount:"tagWithCount_h2kH"};var r=a(4848);function l({permalink:e,label:t,count:a,description:l}){return(0,r.jsxs)(s.A,{href:e,title:l,className:(0,n.A)(i.tag,a?i.tagWithCount:i.tagRegular),children:[t,a&&(0,r.jsx)("span",{children:a})]})}},4005:(e,t,a)=>{a.d(t,{A:()=>r});a(6540);var n=a(9117),s=a(8189),i=a(4848);function r({items:e,component:t=s.A}){return(0,i.jsx)(i.Fragment,{children:e.map((({content:e})=>(0,i.jsx)(n.in,{content:e,children:(0,i.jsx)(t,{children:(0,i.jsx)(e,{})})},e.metadata.permalink)))})}},5989:(e,t,a)=>{a.d(t,{ZD:()=>r,uz:()=>l});a(6540);var n=a(539),s=a(9057);a(4848);function i(){const{selectMessage:e}=(0,s.W)();return t=>e(t,(0,n.T)({id:"theme.blog.post.plurals",description:'Pluralized label for "{count} posts". Use as much plural forms (separated by "|") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)',message:"One post|{count} posts"},{count:t}))}function r(e){const t=i();return(0,n.T)({id:"theme.blog.tagTitle",description:"The title of the page for a blog tag",message:'{nPosts} tagged with "{tagName}"'},{nPosts:t(e.count),tagName:e.label})}const l=()=>(0,n.T)({id:"theme.blog.authorsList.pageTitle",message:"Authors",description:"The title of the authors page"})},6239:(e,t,a)=>{a.d(t,{A:()=>o});a(6540);var n=a(4164),s=a(539),i=a(3953);const r={tags:"tags_jXut",tag:"tag_QGVx"};var l=a(4848);function o({tags:e}){return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("b",{children:(0,l.jsx)(s.A,{id:"theme.tags.tagsListLabel",description:"The label alongside a tag list",children:"Tags:"})}),(0,l.jsx)("ul",{className:(0,n.A)(r.tags,"padding--none","margin-left--sm"),children:e.map((e=>(0,l.jsx)("li",{className:r.tag,children:(0,l.jsx)(i.A,{...e})},e.permalink)))})]})}},6956:(e,t,a)=>{a.r(t),a.d(t,{default:()=>A});a(6540);var n=a(4164),s=a(539),i=a(4737),r=a(5279),l=a(5989),o=a(6289),c=a(569),d=a(7448),g=a(7220),u=a(4005),m=a(665),h=a(9303),p=a(4848);function x({tag:e}){const t=(0,l.ZD)(e);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(i.be,{title:t,description:e.description}),(0,p.jsx)(g.A,{tag:"blog_tags_posts"})]})}function j({tag:e,items:t,sidebar:a,listMetadata:n}){const i=(0,l.ZD)(e);return(0,p.jsxs)(c.A,{sidebar:a,children:[e.unlisted&&(0,p.jsx)(m.A,{}),(0,p.jsxs)("header",{className:"margin-bottom--xl",children:[(0,p.jsx)(h.A,{as:"h1",children:i}),e.description&&(0,p.jsx)("p",{children:e.description}),(0,p.jsx)(o.A,{href:e.allTagsPath,children:(0,p.jsx)(s.A,{id:"theme.tags.tagsPageLink",description:"The label of the link targeting the tag list page",children:"View All Tags"})})]}),(0,p.jsx)(u.A,{items:t}),(0,p.jsx)(d.A,{metadata:n})]})}function A(e){return(0,p.jsxs)(i.e3,{className:(0,n.A)(r.G.wrapper.blogPages,r.G.page.blogTagPostListPage),children:[(0,p.jsx)(x,{...e}),(0,p.jsx)(j,{...e})]})}},7448:(e,t,a)=>{a.d(t,{A:()=>r});a(6540);var n=a(539),s=a(1865),i=a(4848);function r(e){const{metadata:t}=e,{previousPage:a,nextPage:r}=t;return(0,i.jsxs)("nav",{className:"pagination-nav","aria-label":(0,n.T)({id:"theme.blog.paginator.navAriaLabel",message:"Blog list page navigation",description:"The ARIA label for the blog pagination"}),children:[a&&(0,i.jsx)(s.A,{permalink:a,title:(0,i.jsx)(n.A,{id:"theme.blog.paginator.newerEntries",description:"The label used to navigate to the newer blog posts page (previous page)",children:"Newer entries"})}),r&&(0,i.jsx)(s.A,{permalink:r,title:(0,i.jsx)(n.A,{id:"theme.blog.paginator.olderEntries",description:"The label used to navigate to the older blog posts page (next page)",children:"Older entries"}),isNext:!0})]})}},8189:(e,t,a)=>{a.d(t,{A:()=>B});a(6540);var n=a(4164),s=a(9117),i=a(4848);function r({children:e,className:t}){return(0,i.jsx)("article",{className:t,children:e})}var l=a(6289);const o={title:"title_f1Hy"};function c({className:e}){const{metadata:t,isBlogPostPage:a}=(0,s.e7)(),{permalink:r,title:c}=t,d=a?"h1":"h2";return(0,i.jsx)(d,{className:(0,n.A)(o.title,e),children:a?c:(0,i.jsx)(l.A,{to:r,children:c})})}var d=a(539),g=a(9057),u=a(4978);const m={container:"container_mt6G"};function h({readingTime:e}){const t=function(){const{selectMessage:e}=(0,g.W)();return t=>{const a=Math.ceil(t);return e(a,(0,d.T)({id:"theme.blog.post.readingTime.plurals",description:'Pluralized label for "{readingTime} min read". Use as much plural forms (separated by "|") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)',message:"One min read|{readingTime} min read"},{readingTime:a}))}}();return(0,i.jsx)(i.Fragment,{children:t(e)})}function p({date:e,formattedDate:t}){return(0,i.jsx)("time",{dateTime:e,children:t})}function x(){return(0,i.jsx)(i.Fragment,{children:" \xb7 "})}function j({className:e}){const{metadata:t}=(0,s.e7)(),{date:a,readingTime:r}=t,l=(0,u.i)({day:"numeric",month:"long",year:"numeric",timeZone:"UTC"});return(0,i.jsxs)("div",{className:(0,n.A)(m.container,"margin-vert--md",e),children:[(0,i.jsx)(p,{date:a,formattedDate:(o=a,l.format(new Date(o)))}),void 0!==r&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(x,{}),(0,i.jsx)(h,{readingTime:r})]})]});var o}var A=a(5921);const b={authorCol:"authorCol_Hf19",imageOnlyAuthorRow:"imageOnlyAuthorRow_pa_O",imageOnlyAuthorCol:"imageOnlyAuthorCol_G86a"};function f({className:e}){const{metadata:{authors:t},assets:a}=(0,s.e7)();if(0===t.length)return null;const r=t.every((({name:e})=>!e)),l=1===t.length;return(0,i.jsx)("div",{className:(0,n.A)("margin-top--md margin-bottom--sm",r?b.imageOnlyAuthorRow:"row",e),children:t.map(((e,t)=>(0,i.jsx)("div",{className:(0,n.A)(!r&&(l?"col col--12":"col col--6"),r?b.imageOnlyAuthorCol:b.authorCol),children:(0,i.jsx)(A.A,{author:{...e,imageURL:a.authorsImageUrls[t]??e.imageURL}})},t)))})}function v(){return(0,i.jsxs)("header",{children:[(0,i.jsx)(c,{}),(0,i.jsx)(j,{}),(0,i.jsx)(f,{})]})}var T=a(6647),N=a(4809);function _({children:e,className:t}){const{isBlogPostPage:a}=(0,s.e7)();return(0,i.jsx)("div",{id:a?T.LU:void 0,className:(0,n.A)("markdown",t),children:(0,i.jsx)(N.A,{children:e})})}var w=a(5279),k=a(5783),y=a(6239);function P(){return(0,i.jsx)("b",{children:(0,i.jsx)(d.A,{id:"theme.blog.post.readMore",description:"The label used in blog post item excerpts to link to full blog posts",children:"Read more"})})}function R(e){const{blogPostTitle:t,...a}=e;return(0,i.jsx)(l.A,{"aria-label":(0,d.T)({message:"Read more about {title}",id:"theme.blog.post.readMoreLabel",description:"The ARIA label for the link to full blog posts from excerpts"},{title:t}),...a,children:(0,i.jsx)(P,{})})}function U(){const{metadata:e,isBlogPostPage:t}=(0,s.e7)(),{tags:a,title:r,editUrl:l,hasTruncateMarker:o,lastUpdatedBy:c,lastUpdatedAt:d}=e,g=!t&&o,u=a.length>0;if(!(u||g||l))return null;if(t){const e=!!(l||d||c);return(0,i.jsxs)("footer",{className:"docusaurus-mt-lg",children:[u&&(0,i.jsx)("div",{className:(0,n.A)("row","margin-top--sm",w.G.blog.blogFooterEditMetaRow),children:(0,i.jsx)("div",{className:"col",children:(0,i.jsx)(y.A,{tags:a})})}),e&&(0,i.jsx)(k.A,{className:(0,n.A)("margin-top--sm",w.G.blog.blogFooterEditMetaRow),editUrl:l,lastUpdatedAt:d,lastUpdatedBy:c})]})}return(0,i.jsxs)("footer",{className:"row docusaurus-mt-lg",children:[u&&(0,i.jsx)("div",{className:(0,n.A)("col",{"col--9":g}),children:(0,i.jsx)(y.A,{tags:a})}),g&&(0,i.jsx)("div",{className:(0,n.A)("col text--right",{"col--3":u}),children:(0,i.jsx)(R,{blogPostTitle:r,to:e.permalink})})]})}function B({children:e,className:t}){const a=function(){const{isBlogPostPage:e}=(0,s.e7)();return e?void 0:"margin-bottom--xl"}();return(0,i.jsxs)(r,{className:(0,n.A)(a,t),children:[(0,i.jsx)(v,{}),(0,i.jsx)(_,{children:e}),(0,i.jsx)(U,{})]})}}}]);