"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2214],{1213:(e,s,n)=>{n.r(s),n.d(s,{assets:()=>o,contentTitle:()=>c,default:()=>u,frontMatter:()=>a,metadata:()=>t,toc:()=>l});const t=JSON.parse('{"id":"adrs/platform/dapr-secret-store","title":"3. Dapr Secret Store: Kubernetes Secrets","description":"Date: 2025-02-05","source":"@site/docs/adrs/platform/0003-dapr-secret-store.md","sourceDirName":"adrs/platform","slug":"/adrs/platform/dapr-secret-store","permalink":"/docs/adrs/platform/dapr-secret-store","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/platform/0003-dapr-secret-store.md","tags":[],"version":"current","sidebarPosition":3,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"2. Dapr Pub/Sub: Apache Kafka","permalink":"/docs/adrs/platform/dapr-pub-sub"},"next":{"title":"4. Dapr State Store: PostgreSQL v2","permalink":"/docs/adrs/platform/dapr-state-management"}}');var r=n(4848),i=n(8453);const a={},c="3. Dapr Secret Store: Kubernetes Secrets",o={},l=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2},{value:"Positive Aspects",id:"positive-aspects",level:3},{value:"Negative Aspects",id:"negative-aspects",level:3},{value:"Conclusion",id:"conclusion",level:2}];function d(e){const s={h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,i.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.header,{children:(0,r.jsx)(s.h1,{id:"3-dapr-secret-store-kubernetes-secrets",children:"3. Dapr Secret Store: Kubernetes Secrets"})}),"\n",(0,r.jsx)(s.p,{children:"Date: 2025-02-05"}),"\n",(0,r.jsx)(s.h2,{id:"status",children:"Status"}),"\n",(0,r.jsx)(s.p,{children:"Proposed"}),"\n",(0,r.jsx)(s.h2,{id:"context",children:"Context"}),"\n",(0,r.jsx)(s.p,{children:"Dapr provides a secrets management building block to securely store and retrieve sensitive configuration values. Various secret stores are supported, including cloud-based options such as AWS Secrets Manager, Azure Key Vault, and Google Secret Manager, as well as self-managed solutions like HashiCorp Vault and Kubernetes Secrets. For our use case, we aim to select a secret store that is cloud-agnostic, natively integrates with Kubernetes, and provides secure storage while minimizing operational complexity."}),"\n",(0,r.jsxs)(s.p,{children:[(0,r.jsx)(s.strong,{children:"Kubernetes Secrets"})," offer a built-in solution for managing sensitive information in a Kubernetes cluster. They allow storing secrets such as API keys, passwords, and certificates in an encrypted format and can be accessed by applications through environment variables, volume mounts, or direct API calls. When used with Dapr, Kubernetes Secrets provide a seamless way to manage application secrets while leveraging Kubernetes\u2019 native RBAC and access controls for security."]}),"\n",(0,r.jsx)(s.h2,{id:"decision",children:"Decision"}),"\n",(0,r.jsxs)(s.p,{children:["We will use ",(0,r.jsx)(s.strong,{children:"Kubernetes Secrets"})," as the secret store for Dapr. This decision is driven by the following factors:"]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Native Kubernetes Integration"}),": Kubernetes Secrets are a first-class citizen in Kubernetes and integrate seamlessly with existing cluster configurations and workloads."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Cloud-Agnostic"}),": Unlike cloud-specific secret stores, Kubernetes Secrets work across any Kubernetes deployment, avoiding vendor lock-in."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"RBAC and Security Policies"}),": Kubernetes Secrets can be secured using Kubernetes Role-Based Access Control (RBAC) and network policies to restrict unauthorized access."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Encryption at Rest"}),": Secrets are stored encrypted by default in etcd, and additional encryption options are available for enhanced security."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Dapr Compatibility"}),": The Kubernetes Secrets store is fully supported by Dapr, allowing applications to retrieve secrets dynamically at runtime with minimal configuration."]}),"\n"]}),"\n",(0,r.jsx)(s.h2,{id:"consequences",children:"Consequences"}),"\n",(0,r.jsx)(s.h3,{id:"positive-aspects",children:"Positive Aspects"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Seamless Kubernetes Integration"}),": Applications running in Kubernetes can easily access secrets with minimal changes."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"No Additional Infrastructure"}),": No need to set up or manage an external secret store, reducing operational complexity."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"RBAC and Fine-Grained Access Control"}),": Kubernetes RBAC ensures secure access to secrets based on user and service identities."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Encryption and Security"}),": Secrets are encrypted in etcd, and additional encryption policies can be applied."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Dapr Support"}),": Kubernetes Secrets are fully compatible with Dapr, enabling seamless integration with Dapr-managed applications."]}),"\n"]}),"\n",(0,r.jsx)(s.h3,{id:"negative-aspects",children:"Negative Aspects"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Limited Secret Rotation Mechanisms"}),": Kubernetes Secrets do not natively provide automatic secret rotation mechanisms like some cloud-based solutions."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"etcd Security Concerns"}),": If not properly secured, etcd can be a potential attack vector for secrets leakage."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Potential Namespace Scoping Issues"}),": Secrets are namespace-scoped, requiring careful planning for cross-namespace access."]}),"\n"]}),"\n",(0,r.jsx)(s.h2,{id:"conclusion",children:"Conclusion"}),"\n",(0,r.jsx)(s.p,{children:"By adopting Kubernetes Secrets as the secret store in Dapr, we ensure a secure, cloud-agnostic, and well-integrated solution for managing sensitive application data. This choice simplifies secret management within Kubernetes environments while leveraging Kubernetes' native security and access control features."})]})}function u(e={}){const{wrapper:s}={...(0,i.R)(),...e.components};return s?(0,r.jsx)(s,{...e,children:(0,r.jsx)(d,{...e})}):d(e)}},8453:(e,s,n)=>{n.d(s,{R:()=>a,x:()=>c});var t=n(6540);const r={},i=t.createContext(r);function a(e){const s=t.useContext(i);return t.useMemo((function(){return"function"==typeof e?e(s):{...s,...e}}),[s,e])}function c(e){let s;return s=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:a(e.components),t.createElement(i.Provider,{value:s},e.children)}}}]);