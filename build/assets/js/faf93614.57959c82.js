"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7143],{8223:(e,t,i)=>{i.r(t),i.d(t,{assets:()=>s,contentTitle:()=>l,default:()=>h,frontMatter:()=>r,metadata:()=>a,toc:()=>d});const a=JSON.parse('{"id":"ai-architecture/tools/old/api/api","title":"API Documentation","description":"Welcome to the MLOps platform API documentation. This documentation provides detailed information about all available APIs, their endpoints, authentication methods, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/index.md","sourceDirName":"ai-architecture/tools/old/api","slug":"/ai-architecture/tools/old/api/","permalink":"/docs/ai-architecture/tools/old/api/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/index.md","tags":[],"version":"current","frontMatter":{"id":"api","title":"API Documentation","sidebar_label":"API Overview"},"sidebar":"tutorialSidebar","previous":{"title":"tools","permalink":"/docs/ai-architecture/tools/"},"next":{"title":"MLOps Platform API Endpoints","permalink":"/docs/ai-architecture/tools/old/api/api-endpoints"}}');var n=i(4848),o=i(8453);const r={id:"api",title:"API Documentation",sidebar_label:"API Overview"},l="API Documentation",s={},d=[{value:"API Categories",id:"api-categories",level:2},{value:"Model Management",id:"model-management",level:3},{value:"Data Management",id:"data-management",level:3},{value:"Monitoring",id:"monitoring",level:3},{value:"Orchestration",id:"orchestration",level:3},{value:"Visualization",id:"visualization",level:3},{value:"Getting Started",id:"getting-started",level:2}];function c(e){const t={a:"a",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",...(0,o.R)(),...e.components};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(t.header,{children:(0,n.jsx)(t.h1,{id:"api-documentation",children:"API Documentation"})}),"\n",(0,n.jsx)(t.p,{children:"Welcome to the MLOps platform API documentation. This documentation provides detailed information about all available APIs, their endpoints, authentication methods, and usage examples."}),"\n",(0,n.jsx)(t.h2,{id:"api-categories",children:"API Categories"}),"\n",(0,n.jsx)(t.h3,{id:"model-management",children:(0,n.jsx)(t.a,{href:"/docs/ai-architecture/tools/old/api/model-management/",children:"Model Management"})}),"\n",(0,n.jsx)(t.p,{children:"APIs for managing machine learning models, including serving, registry, and experiment tracking."}),"\n",(0,n.jsx)(t.h3,{id:"data-management",children:(0,n.jsx)(t.a,{href:"/docs/ai-architecture/tools/old/api/data-management/",children:"Data Management"})}),"\n",(0,n.jsx)(t.p,{children:"APIs for managing data, including feature stores, data quality, and storage systems."}),"\n",(0,n.jsx)(t.h3,{id:"monitoring",children:(0,n.jsx)(t.a,{href:"/docs/ai-architecture/tools/old/api/monitoring/",children:"Monitoring"})}),"\n",(0,n.jsx)(t.p,{children:"APIs for monitoring model performance and system health, including alerting."}),"\n",(0,n.jsx)(t.h3,{id:"orchestration",children:(0,n.jsx)(t.a,{href:"/docs/ai-architecture/tools/old/api/orchestration/",children:"Orchestration"})}),"\n",(0,n.jsx)(t.p,{children:"APIs for workflow orchestration and pipeline management."}),"\n",(0,n.jsx)(t.h3,{id:"visualization",children:(0,n.jsx)(t.a,{href:"/docs/ai-architecture/tools/old/api/visualization/",children:"Visualization"})}),"\n",(0,n.jsx)(t.p,{children:"Diagrams and visual documentation for the platform."}),"\n",(0,n.jsx)(t.h2,{id:"getting-started",children:"Getting Started"}),"\n",(0,n.jsxs)(t.ol,{children:["\n",(0,n.jsxs)(t.li,{children:["Review the ",(0,n.jsx)(t.a,{href:"/docs/ai-architecture/tools/old/api/api-endpoints",children:"API Endpoints"})," for a complete list of available endpoints"]}),"\n",(0,n.jsx)(t.li,{children:"Check the authentication requirements for each API"}),"\n",(0,n.jsx)(t.li,{children:"Follow the usage examples to integrate with the platform"}),"\n",(0,n.jsx)(t.li,{children:"Refer to the troubleshooting guide for common issues"}),"\n"]})]})}function h(e={}){const{wrapper:t}={...(0,o.R)(),...e.components};return t?(0,n.jsx)(t,{...e,children:(0,n.jsx)(c,{...e})}):c(e)}},8453:(e,t,i)=>{i.d(t,{R:()=>r,x:()=>l});var a=i(6540);const n={},o=a.createContext(n);function r(e){const t=a.useContext(o);return a.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function l(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(n):e.components||n:r(e.components),a.createElement(o.Provider,{value:t},e.children)}}}]);