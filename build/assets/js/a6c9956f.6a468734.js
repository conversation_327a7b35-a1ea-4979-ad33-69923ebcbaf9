"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8704],{1856:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>c,contentTitle:()=>a,default:()=>h,frontMatter:()=>l,metadata:()=>t,toc:()=>o});const t=JSON.parse('{"id":"ai-architecture/tools/old/api/monitoring/monitoring-api","title":"Monitoring API Documentation","description":"The Monitoring API provides endpoints for tracking model performance, data quality, and system health metrics. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/monitoring/monitoring-api.md","sourceDirName":"ai-architecture/tools/old/api/monitoring","slug":"/ai-architecture/tools/old/api/monitoring/monitoring-api","permalink":"/docs/ai-architecture/tools/old/api/monitoring/monitoring-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/monitoring/monitoring-api.md","tags":[],"version":"current","frontMatter":{"id":"monitoring-api","title":"Monitoring API Documentation","sidebar_label":"Monitoring API"},"sidebar":"tutorialSidebar","previous":{"title":"Alerting API","permalink":"/docs/ai-architecture/tools/old/api/monitoring/alerting-api"},"next":{"title":"Orchestration","permalink":"/docs/ai-architecture/tools/old/api/orchestration/"}}');var s=i(4848),r=i(8453);const l={id:"monitoring-api",title:"Monitoring API Documentation",sidebar_label:"Monitoring API"},a="Monitoring API Documentation",c={},o=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Model Monitoring",id:"model-monitoring",level:3},{value:"Log Model Metrics",id:"log-model-metrics",level:4},{value:"Get Model Metrics",id:"get-model-metrics",level:4},{value:"Data Quality Monitoring",id:"data-quality-monitoring",level:3},{value:"Log Data Quality Metrics",id:"log-data-quality-metrics",level:4},{value:"Get Data Quality Metrics",id:"get-data-quality-metrics",level:4},{value:"System Health Monitoring",id:"system-health-monitoring",level:3},{value:"Log System Metrics",id:"log-system-metrics",level:4},{value:"Get System Health",id:"get-system-health",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function d(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,r.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"monitoring-api-documentation",children:"Monitoring API Documentation"})}),"\n",(0,s.jsx)(n.p,{children:"The Monitoring API provides endpoints for tracking model performance, data quality, and system health metrics. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,s.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"https://monitoring.91.life/api/v1\n"})}),"\n",(0,s.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,s.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,s.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,s.jsx)(n.h3,{id:"model-monitoring",children:"Model Monitoring"}),"\n",(0,s.jsx)(n.h4,{id:"log-model-metrics",children:"Log Model Metrics"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /models/{model_id}/metrics\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "timestamp": "2024-03-20T10:00:00Z",\n    "metrics": {\n        "accuracy": 0.95,\n        "precision": 0.92,\n        "recall": 0.94,\n        "f1_score": 0.93,\n        "latency_ms": 150,\n        "throughput": 100\n    },\n    "metadata": {\n        "environment": "production",\n        "version": "v1.2.3",\n        "batch_size": 32\n    }\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "metric_id": "metric_001",\n    "model_id": "model_001",\n    "timestamp": "2024-03-20T10:00:00Z",\n    "status": "recorded"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"get-model-metrics",children:"Get Model Metrics"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /models/{model_id}/metrics\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"start_time"})," (optional): Start time for metrics range"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"end_time"})," (optional): End time for metrics range"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"metric_names"})," (optional): Comma-separated list of metric names"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"aggregation"})," (optional): Aggregation function (avg, min, max, sum)"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "metrics": [\n        {\n            "timestamp": "2024-03-20T10:00:00Z",\n            "accuracy": 0.95,\n            "precision": 0.92,\n            "recall": 0.94,\n            "f1_score": 0.93,\n            "latency_ms": 150,\n            "throughput": 100\n        }\n    ],\n    "aggregations": {\n        "accuracy": {\n            "avg": 0.95,\n            "min": 0.94,\n            "max": 0.96\n        }\n    }\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"data-quality-monitoring",children:"Data Quality Monitoring"}),"\n",(0,s.jsx)(n.h4,{id:"log-data-quality-metrics",children:"Log Data Quality Metrics"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /datasets/{dataset_id}/quality\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "timestamp": "2024-03-20T10:00:00Z",\n    "metrics": {\n        "completeness": 0.98,\n        "accuracy": 0.95,\n        "consistency": 0.97,\n        "timeliness": 0.99,\n        "uniqueness": 0.96\n    },\n    "schema_validation": {\n        "valid": true,\n        "errors": []\n    },\n    "data_validation": {\n        "valid": true,\n        "errors": []\n    }\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "quality_id": "quality_001",\n    "dataset_id": "dataset_001",\n    "timestamp": "2024-03-20T10:00:00Z",\n    "status": "recorded"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"get-data-quality-metrics",children:"Get Data Quality Metrics"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /datasets/{dataset_id}/quality\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"start_time"})," (optional): Start time for metrics range"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"end_time"})," (optional): End time for metrics range"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"metric_names"})," (optional): Comma-separated list of metric names"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "metrics": [\n        {\n            "timestamp": "2024-03-20T10:00:00Z",\n            "completeness": 0.98,\n            "accuracy": 0.95,\n            "consistency": 0.97,\n            "timeliness": 0.99,\n            "uniqueness": 0.96\n        }\n    ],\n    "trends": {\n        "completeness": {\n            "trend": "stable",\n            "change": 0.01\n        }\n    }\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"system-health-monitoring",children:"System Health Monitoring"}),"\n",(0,s.jsx)(n.h4,{id:"log-system-metrics",children:"Log System Metrics"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /system/metrics\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "timestamp": "2024-03-20T10:00:00Z",\n    "component": "feature_store",\n    "metrics": {\n        "cpu_usage": 45.5,\n        "memory_usage": 60.2,\n        "disk_usage": 75.8,\n        "request_rate": 100,\n        "error_rate": 0.1,\n        "latency_ms": 150\n    },\n    "status": "healthy"\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "metric_id": "system_001",\n    "component": "feature_store",\n    "timestamp": "2024-03-20T10:00:00Z",\n    "status": "recorded"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"get-system-health",children:"Get System Health"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /system/health\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"component"})," (optional): Filter by component"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"start_time"})," (optional): Start time for metrics range"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"end_time"})," (optional): End time for metrics range"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "components": [\n        {\n            "name": "feature_store",\n            "status": "healthy",\n            "metrics": {\n                "cpu_usage": 45.5,\n                "memory_usage": 60.2,\n                "disk_usage": 75.8,\n                "request_rate": 100,\n                "error_rate": 0.1,\n                "latency_ms": 150\n            },\n            "last_updated": "2024-03-20T10:00:00Z"\n        }\n    ],\n    "overall_status": "healthy"\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,s.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'from monitoring import Client\n\n# Initialize client\nclient = Client(\n    host="https://monitoring.91.life",\n    auth_token="your-token"\n)\n\n# Log model metrics\nmetrics = {\n    "timestamp": "2024-03-20T10:00:00Z",\n    "metrics": {\n        "accuracy": 0.95,\n        "precision": 0.92,\n        "recall": 0.94,\n        "f1_score": 0.93,\n        "latency_ms": 150,\n        "throughput": 100\n    }\n}\nclient.log_model_metrics("model_001", metrics)\n\n# Get data quality metrics\nquality_metrics = client.get_data_quality(\n    dataset_id="dataset_001",\n    start_time="2024-03-19T00:00:00Z",\n    end_time="2024-03-20T00:00:00Z"\n)\nprint(quality_metrics)\n'})}),"\n",(0,s.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:'# Log model metrics\ncurl -X POST https://monitoring.91.life/api/v1/models/model_001/metrics \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "timestamp": "2024-03-20T10:00:00Z",\n    "metrics": {\n      "accuracy": 0.95,\n      "precision": 0.92,\n      "recall": 0.94,\n      "f1_score": 0.93,\n      "latency_ms": 150,\n      "throughput": 100\n    }\n  }\'\n\n# Get system health\ncurl -X GET "https://monitoring.91.life/api/v1/system/health?component=feature_store" \\\n  -H "Authorization: Bearer ${TOKEN}"\n'})}),"\n",(0,s.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Code"}),(0,s.jsx)(n.th,{children:"Description"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"400"}),(0,s.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"401"}),(0,s.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"403"}),(0,s.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"404"}),(0,s.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"429"}),(0,s.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"500"}),(0,s.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,s.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,s.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,s.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Model Monitoring"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Monitor key performance metrics"}),"\n",(0,s.jsx)(n.li,{children:"Set up alerts for anomalies"}),"\n",(0,s.jsx)(n.li,{children:"Track model drift"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Data Quality"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Monitor data completeness"}),"\n",(0,s.jsx)(n.li,{children:"Track data accuracy"}),"\n",(0,s.jsx)(n.li,{children:"Validate data consistency"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"System Health"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Monitor resource usage"}),"\n",(0,s.jsx)(n.li,{children:"Track error rates"}),"\n",(0,s.jsx)(n.li,{children:"Set up health checks"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Alerting"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Configure appropriate thresholds"}),"\n",(0,s.jsx)(n.li,{children:"Set up notification channels"}),"\n",(0,s.jsx)(n.li,{children:"Implement escalation policies"}),"\n"]}),"\n"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(d,{...e})}):d(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>a});var t=i(6540);const s={},r=t.createContext(s);function l(e){const n=t.useContext(r);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:l(e.components),t.createElement(r.Provider,{value:n},e.children)}}}]);