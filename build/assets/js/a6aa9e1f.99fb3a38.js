"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7643],{1865:(e,t,a)=>{a.d(t,{A:()=>i});a(6540);var n=a(4164),s=a(6289),r=a(4848);function i(e){const{permalink:t,title:a,subLabel:i,isNext:l}=e;return(0,r.jsxs)(s.A,{className:(0,n.A)("pagination-nav__link",l?"pagination-nav__link--next":"pagination-nav__link--prev"),to:t,children:[i&&(0,r.jsx)("div",{className:"pagination-nav__sublabel",children:i}),(0,r.jsx)("div",{className:"pagination-nav__label",children:a})]})}},3953:(e,t,a)=>{a.d(t,{A:()=>l});a(6540);var n=a(4164),s=a(6289);const r={tag:"tag_zVej",tagRegular:"tagRegular_sFm0",tagWithCount:"tagWithCount_h2kH"};var i=a(4848);function l({permalink:e,label:t,count:a,description:l}){return(0,i.jsxs)(s.A,{href:e,title:l,className:(0,n.A)(r.tag,a?r.tagWithCount:r.tagRegular),children:[t,a&&(0,i.jsx)("span",{children:a})]})}},4005:(e,t,a)=>{a.d(t,{A:()=>i});a(6540);var n=a(9117),s=a(8189),r=a(4848);function i({items:e,component:t=s.A}){return(0,r.jsx)(r.Fragment,{children:e.map((({content:e})=>(0,r.jsx)(n.in,{content:e,children:(0,r.jsx)(t,{children:(0,r.jsx)(e,{})})},e.metadata.permalink)))})}},5631:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});a(6540);var n=a(4164),s=a(797),r=a(4737),i=a(5279),l=a(569),o=a(7448),c=a(7220),d=a(4005),g=a(7143),m=a(9117),u=a(4848);function h(e){const t=(0,m.kJ)(e);return(0,u.jsx)(g.A,{children:(0,u.jsx)("script",{type:"application/ld+json",children:JSON.stringify(t)})})}function p(e){const{metadata:t}=e,{siteConfig:{title:a}}=(0,s.A)(),{blogDescription:n,blogTitle:i,permalink:l}=t,o="/"===l?a:i;return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(r.be,{title:o,description:n}),(0,u.jsx)(c.A,{tag:"blog_posts_list"})]})}function x(e){const{metadata:t,items:a,sidebar:n}=e;return(0,u.jsxs)(l.A,{sidebar:n,children:[(0,u.jsx)(d.A,{items:a}),(0,u.jsx)(o.A,{metadata:t})]})}function j(e){return(0,u.jsxs)(r.e3,{className:(0,n.A)(i.G.wrapper.blogPages,i.G.page.blogListPage),children:[(0,u.jsx)(p,{...e}),(0,u.jsx)(h,{...e}),(0,u.jsx)(x,{...e})]})}},6239:(e,t,a)=>{a.d(t,{A:()=>o});a(6540);var n=a(4164),s=a(539),r=a(3953);const i={tags:"tags_jXut",tag:"tag_QGVx"};var l=a(4848);function o({tags:e}){return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("b",{children:(0,l.jsx)(s.A,{id:"theme.tags.tagsListLabel",description:"The label alongside a tag list",children:"Tags:"})}),(0,l.jsx)("ul",{className:(0,n.A)(i.tags,"padding--none","margin-left--sm"),children:e.map((e=>(0,l.jsx)("li",{className:i.tag,children:(0,l.jsx)(r.A,{...e})},e.permalink)))})]})}},7448:(e,t,a)=>{a.d(t,{A:()=>i});a(6540);var n=a(539),s=a(1865),r=a(4848);function i(e){const{metadata:t}=e,{previousPage:a,nextPage:i}=t;return(0,r.jsxs)("nav",{className:"pagination-nav","aria-label":(0,n.T)({id:"theme.blog.paginator.navAriaLabel",message:"Blog list page navigation",description:"The ARIA label for the blog pagination"}),children:[a&&(0,r.jsx)(s.A,{permalink:a,title:(0,r.jsx)(n.A,{id:"theme.blog.paginator.newerEntries",description:"The label used to navigate to the newer blog posts page (previous page)",children:"Newer entries"})}),i&&(0,r.jsx)(s.A,{permalink:i,title:(0,r.jsx)(n.A,{id:"theme.blog.paginator.olderEntries",description:"The label used to navigate to the older blog posts page (next page)",children:"Older entries"}),isNext:!0})]})}},8189:(e,t,a)=>{a.d(t,{A:()=>U});a(6540);var n=a(4164),s=a(9117),r=a(4848);function i({children:e,className:t}){return(0,r.jsx)("article",{className:t,children:e})}var l=a(6289);const o={title:"title_f1Hy"};function c({className:e}){const{metadata:t,isBlogPostPage:a}=(0,s.e7)(),{permalink:i,title:c}=t,d=a?"h1":"h2";return(0,r.jsx)(d,{className:(0,n.A)(o.title,e),children:a?c:(0,r.jsx)(l.A,{to:i,children:c})})}var d=a(539),g=a(9057),m=a(4978);const u={container:"container_mt6G"};function h({readingTime:e}){const t=function(){const{selectMessage:e}=(0,g.W)();return t=>{const a=Math.ceil(t);return e(a,(0,d.T)({id:"theme.blog.post.readingTime.plurals",description:'Pluralized label for "{readingTime} min read". Use as much plural forms (separated by "|") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)',message:"One min read|{readingTime} min read"},{readingTime:a}))}}();return(0,r.jsx)(r.Fragment,{children:t(e)})}function p({date:e,formattedDate:t}){return(0,r.jsx)("time",{dateTime:e,children:t})}function x(){return(0,r.jsx)(r.Fragment,{children:" \xb7 "})}function j({className:e}){const{metadata:t}=(0,s.e7)(),{date:a,readingTime:i}=t,l=(0,m.i)({day:"numeric",month:"long",year:"numeric",timeZone:"UTC"});return(0,r.jsxs)("div",{className:(0,n.A)(u.container,"margin-vert--md",e),children:[(0,r.jsx)(p,{date:a,formattedDate:(o=a,l.format(new Date(o)))}),void 0!==i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x,{}),(0,r.jsx)(h,{readingTime:i})]})]});var o}var A=a(5921);const b={authorCol:"authorCol_Hf19",imageOnlyAuthorRow:"imageOnlyAuthorRow_pa_O",imageOnlyAuthorCol:"imageOnlyAuthorCol_G86a"};function f({className:e}){const{metadata:{authors:t},assets:a}=(0,s.e7)();if(0===t.length)return null;const i=t.every((({name:e})=>!e)),l=1===t.length;return(0,r.jsx)("div",{className:(0,n.A)("margin-top--md margin-bottom--sm",i?b.imageOnlyAuthorRow:"row",e),children:t.map(((e,t)=>(0,r.jsx)("div",{className:(0,n.A)(!i&&(l?"col col--12":"col col--6"),i?b.imageOnlyAuthorCol:b.authorCol),children:(0,r.jsx)(A.A,{author:{...e,imageURL:a.authorsImageUrls[t]??e.imageURL}})},t)))})}function v(){return(0,r.jsxs)("header",{children:[(0,r.jsx)(c,{}),(0,r.jsx)(j,{}),(0,r.jsx)(f,{})]})}var N=a(6647),_=a(4809);function T({children:e,className:t}){const{isBlogPostPage:a}=(0,s.e7)();return(0,r.jsx)("div",{id:a?N.LU:void 0,className:(0,n.A)("markdown",t),children:(0,r.jsx)(_.A,{children:e})})}var k=a(5279),w=a(5783),y=a(6239);function P(){return(0,r.jsx)("b",{children:(0,r.jsx)(d.A,{id:"theme.blog.post.readMore",description:"The label used in blog post item excerpts to link to full blog posts",children:"Read more"})})}function R(e){const{blogPostTitle:t,...a}=e;return(0,r.jsx)(l.A,{"aria-label":(0,d.T)({message:"Read more about {title}",id:"theme.blog.post.readMoreLabel",description:"The ARIA label for the link to full blog posts from excerpts"},{title:t}),...a,children:(0,r.jsx)(P,{})})}function C(){const{metadata:e,isBlogPostPage:t}=(0,s.e7)(),{tags:a,title:i,editUrl:l,hasTruncateMarker:o,lastUpdatedBy:c,lastUpdatedAt:d}=e,g=!t&&o,m=a.length>0;if(!(m||g||l))return null;if(t){const e=!!(l||d||c);return(0,r.jsxs)("footer",{className:"docusaurus-mt-lg",children:[m&&(0,r.jsx)("div",{className:(0,n.A)("row","margin-top--sm",k.G.blog.blogFooterEditMetaRow),children:(0,r.jsx)("div",{className:"col",children:(0,r.jsx)(y.A,{tags:a})})}),e&&(0,r.jsx)(w.A,{className:(0,n.A)("margin-top--sm",k.G.blog.blogFooterEditMetaRow),editUrl:l,lastUpdatedAt:d,lastUpdatedBy:c})]})}return(0,r.jsxs)("footer",{className:"row docusaurus-mt-lg",children:[m&&(0,r.jsx)("div",{className:(0,n.A)("col",{"col--9":g}),children:(0,r.jsx)(y.A,{tags:a})}),g&&(0,r.jsx)("div",{className:(0,n.A)("col text--right",{"col--3":m}),children:(0,r.jsx)(R,{blogPostTitle:i,to:e.permalink})})]})}function U({children:e,className:t}){const a=function(){const{isBlogPostPage:e}=(0,s.e7)();return e?void 0:"margin-bottom--xl"}();return(0,r.jsxs)(i,{className:(0,n.A)(a,t),children:[(0,r.jsx)(v,{}),(0,r.jsx)(T,{children:e}),(0,r.jsx)(C,{})]})}}}]);