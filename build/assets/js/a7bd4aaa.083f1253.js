"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7098],{6404:(n,e,r)=>{r.r(e),r.d(e,{default:()=>d});r(6540);var s=r(4737);function t(n,e){return`docs-${n}-${e}`}var o=r(1097),c=r(2831),i=r(7220),u=r(4848);function a(n){const{version:e}=n;return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(i.A,{version:e.version,tag:t(e.pluginId,e.version)}),(0,u.jsx)(s.be,{children:e.noIndex&&(0,u.jsx)("meta",{name:"robots",content:"noindex, nofollow"})})]})}function l(n){const{version:e,route:r}=n;return(0,u.jsx)(s.e3,{className:e.className,children:(0,u.jsx)(o.n,{version:e,children:(0,c.v)(r.routes)})})}function d(n){return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(a,{...n}),(0,u.jsx)(l,{...n})]})}}}]);