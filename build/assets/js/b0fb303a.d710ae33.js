"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2048],{9717:e=>{e.exports=JSON.parse('{"categoryGeneratedIndex":{"title":"Platform","description":"Platform Architectural Decision Records.","slug":"/category/platform","permalink":"/docs/category/platform","sidebar":"tutorialSidebar","navigation":{"previous":{"title":"6. Keycloak as Authentication and User Management Solution","permalink":"/docs/adrs/global/keycloak"},"next":{"title":"1. Dapr","permalink":"/docs/adrs/platform/dapr"}}}}')}}]);