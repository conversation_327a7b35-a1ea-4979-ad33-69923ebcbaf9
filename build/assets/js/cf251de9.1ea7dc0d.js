"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2118],{7190:(e,i,n)=>{n.r(i),n.d(i,{assets:()=>l,contentTitle:()=>o,default:()=>h,frontMatter:()=>s,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/api/api-overview","title":"API Overview","description":"Welcome to the AI Platform API documentation. This section provides comprehensive information about our API endpoints, authentication, and usage.","source":"@site/docs/ai-architecture/api/api-overview.md","sourceDirName":"ai-architecture/api","slug":"/ai-architecture/api/api-overview","permalink":"/docs/ai-architecture/api/api-overview","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/api-overview.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"API Reference","permalink":"/docs/ai-architecture/api/"},"next":{"title":"Authentication","permalink":"/docs/ai-architecture/api/auth/authentication"}}');var t=n(4848),a=n(8453);const s={sidebar_position:1},o="API Overview",l={},c=[{value:"API Base URL",id:"api-base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"Rate Limits",id:"rate-limits",level:2},{value:"Response Format",id:"response-format",level:2},{value:"Error Handling",id:"error-handling",level:2},{value:"Available Endpoints",id:"available-endpoints",level:2},{value:"SDK Support",id:"sdk-support",level:2},{value:"Getting Started",id:"getting-started",level:2}];function d(e){const i={a:"a",code:"code",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",ul:"ul",...(0,a.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.header,{children:(0,t.jsx)(i.h1,{id:"api-overview",children:"API Overview"})}),"\n",(0,t.jsx)(i.p,{children:"Welcome to the AI Platform API documentation. This section provides comprehensive information about our API endpoints, authentication, and usage."}),"\n",(0,t.jsx)(i.h2,{id:"api-base-url",children:"API Base URL"}),"\n",(0,t.jsx)(i.pre,{children:(0,t.jsx)(i.code,{children:"https://api.ai-platform.example.com/v1\n"})}),"\n",(0,t.jsx)(i.h2,{id:"authentication",children:"Authentication"}),"\n",(0,t.jsx)(i.p,{children:"All API requests require authentication using API keys. You can obtain your API key from the dashboard."}),"\n",(0,t.jsx)(i.pre,{children:(0,t.jsx)(i.code,{className:"language-bash",children:'curl -H "Authorization: Bearer YOUR_API_KEY" https://api.ai-platform.example.com/v1/models\n'})}),"\n",(0,t.jsx)(i.h2,{id:"rate-limits",children:"Rate Limits"}),"\n",(0,t.jsxs)(i.ul,{children:["\n",(0,t.jsx)(i.li,{children:"100 requests per minute for standard plans"}),"\n",(0,t.jsx)(i.li,{children:"1000 requests per minute for enterprise plans"}),"\n"]}),"\n",(0,t.jsx)(i.h2,{id:"response-format",children:"Response Format"}),"\n",(0,t.jsx)(i.p,{children:"All responses are in JSON format and include the following structure:"}),"\n",(0,t.jsx)(i.pre,{children:(0,t.jsx)(i.code,{className:"language-json",children:'{\n  "data": {\n    // Response data here\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,t.jsx)(i.h2,{id:"error-handling",children:"Error Handling"}),"\n",(0,t.jsx)(i.p,{children:"Errors follow a standard format:"}),"\n",(0,t.jsx)(i.pre,{children:(0,t.jsx)(i.code,{className:"language-json",children:'{\n  "error": {\n    "code": "invalid_request",\n    "message": "The request was invalid",\n    "details": {\n      // Additional error details\n    }\n  }\n}\n'})}),"\n",(0,t.jsx)(i.h2,{id:"available-endpoints",children:"Available Endpoints"}),"\n",(0,t.jsxs)(i.ul,{children:["\n",(0,t.jsx)(i.li,{children:(0,t.jsx)(i.a,{href:"/docs/ai-architecture/api/auth/authentication",children:"Authentication"})}),"\n",(0,t.jsx)(i.li,{children:(0,t.jsx)(i.a,{href:"/docs/ai-architecture/api/models/list-models",children:"Models"})}),"\n",(0,t.jsx)(i.li,{children:(0,t.jsx)(i.a,{href:"/docs/ai-architecture/api/data/upload",children:"Data Management"})}),"\n",(0,t.jsx)(i.li,{children:(0,t.jsx)(i.a,{href:"/docs/ai-architecture/api/monitoring/metrics",children:"Monitoring"})}),"\n"]}),"\n",(0,t.jsx)(i.h2,{id:"sdk-support",children:"SDK Support"}),"\n",(0,t.jsx)(i.p,{children:"We provide official SDKs for:"}),"\n",(0,t.jsxs)(i.ul,{children:["\n",(0,t.jsx)(i.li,{children:"Python"}),"\n",(0,t.jsx)(i.li,{children:"JavaScript/TypeScript"}),"\n",(0,t.jsx)(i.li,{children:"Java"}),"\n",(0,t.jsx)(i.li,{children:"Go"}),"\n"]}),"\n",(0,t.jsx)(i.h2,{id:"getting-started",children:"Getting Started"}),"\n",(0,t.jsxs)(i.ol,{children:["\n",(0,t.jsx)(i.li,{children:(0,t.jsx)(i.a,{href:"/docs/ai-architecture/api/auth/authentication#creating-an-api-key",children:"Create an API Key"})}),"\n",(0,t.jsx)(i.li,{children:(0,t.jsx)(i.a,{href:"/docs/ai-architecture/api/models/list-models#example-request",children:"Make your first request"})}),"\n",(0,t.jsx)(i.li,{children:(0,t.jsx)(i.a,{href:"/docs/ai-architecture/apis/ai-architecture/api",children:"Explore the API Reference"})}),"\n"]})]})}function h(e={}){const{wrapper:i}={...(0,a.R)(),...e.components};return i?(0,t.jsx)(i,{...e,children:(0,t.jsx)(d,{...e})}):d(e)}},8453:(e,i,n)=>{n.d(i,{R:()=>s,x:()=>o});var r=n(6540);const t={},a=r.createContext(t);function s(e){const i=r.useContext(a);return r.useMemo((function(){return"function"==typeof e?e(i):{...i,...e}}),[i,e])}function o(e){let i;return i=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:s(e.components),r.createElement(a.Provider,{value:i},e.children)}}}]);