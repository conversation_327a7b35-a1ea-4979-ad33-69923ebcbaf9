"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[5825],{5366:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>d,contentTitle:()=>a,default:()=>o,frontMatter:()=>l,metadata:()=>s,toc:()=>c});const s=JSON.parse('{"id":"ai-architecture/api/data/processing","title":"Data Processing","description":"Process and transform your datasets using configurable pipelines.","source":"@site/docs/ai-architecture/api/data/processing.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/processing","permalink":"/docs/ai-architecture/api/data/processing","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/processing.md","tags":[],"version":"current","sidebarPosition":2,"frontMatter":{"sidebar_position":2},"sidebar":"tutorialSidebar","previous":{"title":"Data Management","permalink":"/docs/ai-architecture/api/data/management"},"next":{"title":"Data Versioning","permalink":"/docs/ai-architecture/api/data/versioning"}}');var t=i(4848),r=i(8453);const l={sidebar_position:2},a="Data Processing",d={},c=[{value:"Create Processing Pipeline",id:"create-processing-pipeline",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Run Pipeline",id:"run-pipeline",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Request Body",id:"request-body-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Get Pipeline Results",id:"get-pipeline-results",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Processing Steps",id:"processing-steps",level:2},{value:"Processing Status",id:"processing-status",level:2},{value:"Job Status",id:"job-status",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Processing Best Practices",id:"processing-best-practices",level:2}];function p(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,r.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"data-processing",children:"Data Processing"})}),"\n",(0,t.jsx)(n.p,{children:"Process and transform your datasets using configurable pipelines."}),"\n",(0,t.jsx)(n.h2,{id:"create-processing-pipeline",children:"Create Processing Pipeline"}),"\n",(0,t.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"POST /v1/data/pipelines\n"})}),"\n",(0,t.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "name": "text_preprocessing",\n  "description": "Text preprocessing pipeline",\n  "steps": [\n    {\n      "name": "clean_text",\n      "type": "text",\n      "config": {\n        "remove_special_chars": true,\n        "lowercase": true,\n        "remove_numbers": false\n      }\n    },\n    {\n      "name": "tokenize",\n      "type": "text",\n      "config": {\n        "method": "word",\n        "max_tokens": 100\n      }\n    },\n    {\n      "name": "remove_stopwords",\n      "type": "text",\n      "config": {\n        "language": "en"\n      }\n    }\n  ],\n  "input_schema": {\n    "type": "object",\n    "properties": {\n      "text": {\n        "type": "string"\n      }\n    }\n  },\n  "output_schema": {\n    "type": "object",\n    "properties": {\n      "tokens": {\n        "type": "array",\n        "items": {\n          "type": "string"\n        }\n      }\n    }\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "pipeline": {\n      "id": "pipeline_123",\n      "name": "text_preprocessing",\n      "description": "Text preprocessing pipeline",\n      "steps": [\n        {\n          "id": "step_123",\n          "name": "clean_text",\n          "type": "text",\n          "config": {\n            "remove_special_chars": true,\n            "lowercase": true,\n            "remove_numbers": false\n          }\n        }\n      ],\n      "status": "active",\n      "created_at": "2024-03-14T12:00:00Z",\n      "updated_at": "2024-03-14T12:00:00Z"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"run-pipeline",children:"Run Pipeline"}),"\n",(0,t.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"POST /v1/data/pipelines/{pipeline_id}/run\n"})}),"\n",(0,t.jsx)(n.h3,{id:"request-body-1",children:"Request Body"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "input": {\n    "text": "Hello, World! This is a test."\n  },\n  "options": {\n    "batch_size": 100,\n    "timeout": 300\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "job": {\n      "id": "job_123",\n      "pipeline_id": "pipeline_123",\n      "status": "processing",\n      "created_at": "2024-03-14T12:00:00Z",\n      "input": {\n        "text": "Hello, World! This is a test."\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"get-pipeline-results",children:"Get Pipeline Results"}),"\n",(0,t.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"GET /v1/data/pipelines/{pipeline_id}/jobs/{job_id}\n"})}),"\n",(0,t.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "job": {\n      "id": "job_123",\n      "pipeline_id": "pipeline_123",\n      "status": "completed",\n      "created_at": "2024-03-14T12:00:00Z",\n      "completed_at": "2024-03-14T12:00:05Z",\n      "input": {\n        "text": "Hello, World! This is a test."\n      },\n      "output": {\n        "tokens": ["hello", "world", "test"]\n      },\n      "metrics": {\n        "processing_time": 5.0,\n        "memory_usage": 100.5\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:05Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"processing-steps",children:"Processing Steps"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Type"}),(0,t.jsx)(n.th,{children:"Description"}),(0,t.jsx)(n.th,{children:"Use Case"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"text"}),(0,t.jsx)(n.td,{children:"Text processing"}),(0,t.jsx)(n.td,{children:"NLP, text cleaning"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"image"}),(0,t.jsx)(n.td,{children:"Image processing"}),(0,t.jsx)(n.td,{children:"Computer vision"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"audio"}),(0,t.jsx)(n.td,{children:"Audio processing"}),(0,t.jsx)(n.td,{children:"Speech recognition"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"video"}),(0,t.jsx)(n.td,{children:"Video processing"}),(0,t.jsx)(n.td,{children:"Video analysis"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"tabular"}),(0,t.jsx)(n.td,{children:"Tabular data processing"}),(0,t.jsx)(n.td,{children:"Data transformation"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"custom"}),(0,t.jsx)(n.td,{children:"Custom processing"}),(0,t.jsx)(n.td,{children:"Specialized tasks"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"processing-status",children:"Processing Status"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Status"}),(0,t.jsx)(n.th,{children:"Description"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"created"}),(0,t.jsx)(n.td,{children:"Pipeline created"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"active"}),(0,t.jsx)(n.td,{children:"Pipeline is active"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"inactive"}),(0,t.jsx)(n.td,{children:"Pipeline is inactive"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"error"}),(0,t.jsx)(n.td,{children:"Pipeline has errors"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"job-status",children:"Job Status"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Status"}),(0,t.jsx)(n.th,{children:"Description"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"queued"}),(0,t.jsx)(n.td,{children:"Job is queued"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"processing"}),(0,t.jsx)(n.td,{children:"Job is processing"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"completed"}),(0,t.jsx)(n.td,{children:"Job is completed"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"failed"}),(0,t.jsx)(n.td,{children:"Job has failed"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"cancelled"}),(0,t.jsx)(n.td,{children:"Job was cancelled"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,t.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create pipeline\npipeline = client.data.create_pipeline(\n    name="text_preprocessing",\n    description="Text preprocessing pipeline",\n    steps=[\n        {\n            "name": "clean_text",\n            "type": "text",\n            "config": {\n                "remove_special_chars": True,\n                "lowercase": True\n            }\n        }\n    ]\n)\n\n# Run pipeline\njob = client.data.run_pipeline(\n    "pipeline_123",\n    input={\n        "text": "Hello, World!"\n    }\n)\n\n# Get results\nresults = client.data.get_pipeline_job(\n    "pipeline_123",\n    "job_123"\n)\n'})}),"\n",(0,t.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create pipeline\nconst pipeline = await client.data.createPipeline({\n  name: 'text_preprocessing',\n  description: 'Text preprocessing pipeline',\n  steps: [\n    {\n      name: 'clean_text',\n      type: 'text',\n      config: {\n        removeSpecialChars: true,\n        lowercase: true\n      }\n    }\n  ]\n});\n\n// Run pipeline\nconst job = await client.data.runPipeline('pipeline_123', {\n  input: {\n    text: 'Hello, World!'\n  }\n});\n\n// Get results\nconst results = await client.data.getPipelineJob('pipeline_123', 'job_123');\n"})}),"\n",(0,t.jsx)(n.h2,{id:"processing-best-practices",children:"Processing Best Practices"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Define clear schemas"}),"\n",(0,t.jsx)(n.li,{children:"Use appropriate steps"}),"\n",(0,t.jsx)(n.li,{children:"Handle errors gracefully"}),"\n",(0,t.jsx)(n.li,{children:"Monitor performance"}),"\n",(0,t.jsx)(n.li,{children:"Validate inputs/outputs"}),"\n",(0,t.jsx)(n.li,{children:"Document transformations"}),"\n",(0,t.jsx)(n.li,{children:"Test thoroughly"}),"\n",(0,t.jsx)(n.li,{children:"Version pipelines"}),"\n"]})]})}function o(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(p,{...e})}):p(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>a});var s=i(6540);const t={},r=s.createContext(t);function l(e){const n=s.useContext(r);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:l(e.components),s.createElement(r.Provider,{value:n},e.children)}}}]);