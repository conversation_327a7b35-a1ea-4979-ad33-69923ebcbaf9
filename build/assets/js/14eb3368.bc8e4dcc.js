"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6969],{1865:(e,n,t)=>{t.d(n,{A:()=>a});t(6540);var s=t(4164),r=t(6289),i=t(4848);function a(e){const{permalink:n,title:t,subLabel:a,isNext:o}=e;return(0,i.jsxs)(r.A,{className:(0,s.A)("pagination-nav__link",o?"pagination-nav__link--next":"pagination-nav__link--prev"),to:n,children:[a&&(0,i.jsx)("div",{className:"pagination-nav__sublabel",children:a}),(0,i.jsx)("div",{className:"pagination-nav__label",children:t})]})}},3600:(e,n,t)=>{t.d(n,{A:()=>a});t(6540);var s=t(539),r=t(1865),i=t(4848);function a(e){const{previous:n,next:t}=e;return(0,i.jsxs)("nav",{className:"pagination-nav docusaurus-mt-lg","aria-label":(0,s.T)({id:"theme.docs.paginator.navAriaLabel",message:"Docs pages",description:"The ARIA label for the docs pagination"}),children:[n&&(0,i.jsx)(r.A,{...n,subLabel:(0,i.jsx)(s.A,{id:"theme.docs.paginator.previous",description:"The label used to navigate to the previous doc",children:"Previous"})}),t&&(0,i.jsx)(r.A,{...t,subLabel:(0,i.jsx)(s.A,{id:"theme.docs.paginator.next",description:"The label used to navigate to the next doc",children:"Next"}),isNext:!0})]})}},5730:(e,n,t)=>{t.d(n,{A:()=>l});t(6540);var s=t(4164),r=t(539),i=t(5279),a=t(1097),o=t(4848);function l({className:e}){const n=(0,a.r)();return n.badge?(0,o.jsx)("span",{className:(0,s.A)(e,i.G.docs.docVersionBadge,"badge badge--secondary"),children:(0,o.jsx)(r.A,{id:"theme.docs.versionBadge.label",values:{versionLabel:n.label},children:"Version: {versionLabel}"})}):null}},5833:(e,n,t)=>{t.d(n,{A:()=>p});t(6540);var s=t(4164),r=t(797),i=t(6289),a=t(539),o=t(4183),l=t(5279),c=t(7958),d=t(1097),u=t(4848);const m={unreleased:function({siteTitle:e,versionMetadata:n}){return(0,u.jsx)(a.A,{id:"theme.docs.versions.unreleasedVersionLabel",description:"The label used to tell the user that he's browsing an unreleased doc version",values:{siteTitle:e,versionLabel:(0,u.jsx)("b",{children:n.label})},children:"This is unreleased documentation for {siteTitle} {versionLabel} version."})},unmaintained:function({siteTitle:e,versionMetadata:n}){return(0,u.jsx)(a.A,{id:"theme.docs.versions.unmaintainedVersionLabel",description:"The label used to tell the user that he's browsing an unmaintained doc version",values:{siteTitle:e,versionLabel:(0,u.jsx)("b",{children:n.label})},children:"This is documentation for {siteTitle} {versionLabel}, which is no longer actively maintained."})}};function h(e){const n=m[e.versionMetadata.banner];return(0,u.jsx)(n,{...e})}function b({versionLabel:e,to:n,onClick:t}){return(0,u.jsx)(a.A,{id:"theme.docs.versions.latestVersionSuggestionLabel",description:"The label used to tell the user to check the latest version",values:{versionLabel:e,latestVersionLink:(0,u.jsx)("b",{children:(0,u.jsx)(i.A,{to:n,onClick:t,children:(0,u.jsx)(a.A,{id:"theme.docs.versions.latestVersionLinkLabel",description:"The label used for the latest version suggestion link label",children:"latest version"})})})},children:"For up-to-date documentation, see the {latestVersionLink} ({versionLabel})."})}function x({className:e,versionMetadata:n}){const{siteConfig:{title:t}}=(0,r.A)(),{pluginId:i}=(0,o.vT)({failfast:!0}),{savePreferredVersionName:a}=(0,c.g1)(i),{latestDocSuggestion:d,latestVersionSuggestion:m}=(0,o.HW)(i),x=d??(p=m).docs.find((e=>e.id===p.mainDocId));var p;return(0,u.jsxs)("div",{className:(0,s.A)(e,l.G.docs.docVersionBanner,"alert alert--warning margin-bottom--md"),role:"alert",children:[(0,u.jsx)("div",{children:(0,u.jsx)(h,{siteTitle:t,versionMetadata:n})}),(0,u.jsx)("div",{className:"margin-top--md",children:(0,u.jsx)(b,{versionLabel:m.label,to:x.path,onClick:()=>a(m.name)})})]})}function p({className:e}){const n=(0,d.r)();return n.banner?(0,u.jsx)(x,{className:e,versionMetadata:n}):null}},7519:(e,n,t)=>{t.d(n,{A:()=>g});t(6540);var s=t(4164),r=t(5279),i=t(9439),a=t(3465),o=t(6289),l=t(539),c=t(9030),d=t(4848);function u(e){return(0,d.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,d.jsx)("path",{d:"M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z",fill:"currentColor"})})}const m={breadcrumbHomeIcon:"breadcrumbHomeIcon_YNFT"};function h(){const e=(0,c.Ay)("/");return(0,d.jsx)("li",{className:"breadcrumbs__item",children:(0,d.jsx)(o.A,{"aria-label":(0,l.T)({id:"theme.docs.breadcrumbs.home",message:"Home page",description:"The ARIA label for the home page in the breadcrumbs"}),className:"breadcrumbs__link",href:e,children:(0,d.jsx)(u,{className:m.breadcrumbHomeIcon})})})}const b={breadcrumbsContainer:"breadcrumbsContainer_Z_bl"};function x({children:e,href:n,isLast:t}){const s="breadcrumbs__link";return t?(0,d.jsx)("span",{className:s,itemProp:"name",children:e}):n?(0,d.jsx)(o.A,{className:s,href:n,itemProp:"item",children:(0,d.jsx)("span",{itemProp:"name",children:e})}):(0,d.jsx)("span",{className:s,children:e})}function p({children:e,active:n,index:t,addMicrodata:r}){return(0,d.jsxs)("li",{...r&&{itemScope:!0,itemProp:"itemListElement",itemType:"https://schema.org/ListItem"},className:(0,s.A)("breadcrumbs__item",{"breadcrumbs__item--active":n}),children:[e,(0,d.jsx)("meta",{itemProp:"position",content:String(t+1)})]})}function g(){const e=(0,i.OF)(),n=(0,a.Dt)();return e?(0,d.jsx)("nav",{className:(0,s.A)(r.G.docs.docBreadcrumbs,b.breadcrumbsContainer),"aria-label":(0,l.T)({id:"theme.docs.breadcrumbs.navAriaLabel",message:"Breadcrumbs",description:"The ARIA label for the breadcrumbs"}),children:(0,d.jsxs)("ul",{className:"breadcrumbs",itemScope:!0,itemType:"https://schema.org/BreadcrumbList",children:[n&&(0,d.jsx)(h,{}),e.map(((n,t)=>{const s=t===e.length-1,r="category"===n.type&&n.linkUnlisted?void 0:n.href;return(0,d.jsx)(p,{active:s,index:t,addMicrodata:!!r,children:(0,d.jsx)(x,{href:r,isLast:s,children:n.label})},t)}))]})}):null}},7981:(e,n,t)=>{t.r(n),t.d(n,{default:()=>I});t(6540);var s=t(4737),r=t(9439),i=t(9030),a=t(4164),o=t(6289),l=t(9057),c=t(2887),d=t(539),u=t(9303);const m={cardContainer:"cardContainer_fWXF",cardTitle:"cardTitle_rnsV",cardDescription:"cardDescription_PWke"};var h=t(4848);function b({href:e,children:n}){return(0,h.jsx)(o.A,{href:e,className:(0,a.A)("card padding--lg",m.cardContainer),children:n})}function x({href:e,icon:n,title:t,description:s}){return(0,h.jsxs)(b,{href:e,children:[(0,h.jsxs)(u.A,{as:"h2",className:(0,a.A)("text--truncate",m.cardTitle),title:t,children:[n," ",t]}),s&&(0,h.jsx)("p",{className:(0,a.A)("text--truncate",m.cardDescription),title:s,children:s})]})}function p({item:e}){const n=(0,r.Nr)(e),t=function(){const{selectMessage:e}=(0,l.W)();return n=>e(n,(0,d.T)({message:"1 item|{count} items",id:"theme.docs.DocCard.categoryDescription.plurals",description:"The default description for a category card in the generated index about how many items this category includes"},{count:n}))}();return n?(0,h.jsx)(x,{href:n,icon:"\ud83d\uddc3\ufe0f",title:e.label,description:e.description??t(e.items.length)}):null}function g({item:e}){const n=(0,c.A)(e.href)?"\ud83d\udcc4\ufe0f":"\ud83d\udd17",t=(0,r.cC)(e.docId??void 0);return(0,h.jsx)(x,{href:e.href,icon:n,title:e.label,description:e.description??t?.description})}function v({item:e}){switch(e.type){case"link":return(0,h.jsx)(g,{item:e});case"category":return(0,h.jsx)(p,{item:e});default:throw new Error(`unknown item type ${JSON.stringify(e)}`)}}function f({className:e}){const n=(0,r.$S)();return(0,h.jsx)(j,{items:n.items,className:e})}function j(e){const{items:n,className:t}=e;if(!n)return(0,h.jsx)(f,{...e});const s=(0,r.d1)(n);return(0,h.jsx)("section",{className:(0,a.A)("row",t),children:s.map(((e,n)=>(0,h.jsx)("article",{className:"col col--6 margin-bottom--lg",children:(0,h.jsx)(v,{item:e})},n)))})}var A=t(3600),N=t(5833),T=t(5730),_=t(7519);const L={generatedIndexPage:"generatedIndexPage_vN6x",list:"list_eTzJ",title:"title_kItE"};function k({categoryGeneratedIndex:e}){return(0,h.jsx)(s.be,{title:e.title,description:e.description,keywords:e.keywords,image:(0,i.Ay)(e.image)})}function y({categoryGeneratedIndex:e}){const n=(0,r.$S)();return(0,h.jsxs)("div",{className:L.generatedIndexPage,children:[(0,h.jsx)(N.A,{}),(0,h.jsx)(_.A,{}),(0,h.jsx)(T.A,{}),(0,h.jsxs)("header",{children:[(0,h.jsx)(u.A,{as:"h1",className:L.title,children:e.title}),e.description&&(0,h.jsx)("p",{children:e.description})]}),(0,h.jsx)("article",{className:"margin-top--lg",children:(0,h.jsx)(j,{items:n.items,className:L.list})}),(0,h.jsx)("footer",{className:"margin-top--lg",children:(0,h.jsx)(A.A,{previous:e.navigation.previous,next:e.navigation.next})})]})}function I(e){return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(k,{...e}),(0,h.jsx)(y,{...e})]})}},9057:(e,n,t)=>{t.d(n,{W:()=>c});var s=t(6540),r=t(797);const i=["zero","one","two","few","many","other"];function a(e){return i.filter((n=>e.includes(n)))}const o={locale:"en",pluralForms:a(["one","other"]),select:e=>1===e?"one":"other"};function l(){const{i18n:{currentLocale:e}}=(0,r.A)();return(0,s.useMemo)((()=>{try{return function(e){const n=new Intl.PluralRules(e);return{locale:e,pluralForms:a(n.resolvedOptions().pluralCategories),select:e=>n.select(e)}}(e)}catch(n){return console.error(`Failed to use Intl.PluralRules for locale "${e}".\nDocusaurus will fallback to the default (English) implementation.\nError: ${n.message}\n`),o}}),[e])}function c(){const e=l();return{selectMessage:(n,t)=>function(e,n,t){const s=e.split("|");if(1===s.length)return s[0];s.length>t.pluralForms.length&&console.error(`For locale=${t.locale}, a maximum of ${t.pluralForms.length} plural forms are expected (${t.pluralForms.join(",")}), but the message contains ${s.length}: ${e}`);const r=t.select(n),i=t.pluralForms.indexOf(r);return s[Math.min(i,s.length-1)]}(t,n,e)}}}}]);