"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7656],{6234:(e,t,s)=>{s.r(t),s.d(t,{assets:()=>c,contentTitle:()=>o,default:()=>p,frontMatter:()=>r,metadata:()=>n,toc:()=>l});const n=JSON.parse('{"id":"adrs/platform/dapr-state-management","title":"4. Dapr State Store: PostgreSQL v2","description":"Date: 2025-02-05","source":"@site/docs/adrs/platform/0004-dapr-state-management.md","sourceDirName":"adrs/platform","slug":"/adrs/platform/dapr-state-management","permalink":"/docs/adrs/platform/dapr-state-management","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/platform/0004-dapr-state-management.md","tags":[],"version":"current","sidebarPosition":4,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"3. Dapr Secret Store: Kubernetes Secrets","permalink":"/docs/adrs/platform/dapr-secret-store"},"next":{"title":"5. Web Applications with React","permalink":"/docs/adrs/platform/react"}}');var i=s(4848),a=s(8453);const r={},o="4. Dapr State Store: PostgreSQL v2",c={},l=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2},{value:"Positive Aspects",id:"positive-aspects",level:3},{value:"Negative Aspects",id:"negative-aspects",level:3},{value:"Conclusion",id:"conclusion",level:2}];function d(e){const t={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,a.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(t.header,{children:(0,i.jsx)(t.h1,{id:"4-dapr-state-store-postgresql-v2",children:"4. Dapr State Store: PostgreSQL v2"})}),"\n",(0,i.jsx)(t.p,{children:"Date: 2025-02-05"}),"\n",(0,i.jsx)(t.h2,{id:"status",children:"Status"}),"\n",(0,i.jsx)(t.p,{children:"Proposed"}),"\n",(0,i.jsx)(t.h2,{id:"context",children:"Context"}),"\n",(0,i.jsx)(t.p,{children:"Dapr provides a state management building block that enables microservices to persist and retrieve state efficiently. Various state store options exist, including Redis, etcd, Consul, Apache Cassandra, and PostgreSQL. Our goal is to choose a solution that offers strong consistency, high availability, and ease of integration with existing enterprise databases. Given our requirements for transactional guarantees, SQL-based querying, and scalability, PostgreSQL v2 emerges as a compelling choice."}),"\n",(0,i.jsxs)(t.p,{children:["The ",(0,i.jsx)(t.strong,{children:"PostgreSQL v2"})," state store component in Dapr introduces significant performance and reliability improvements over v1. Notably, it stores state values using the ",(0,i.jsx)(t.code,{children:"BYTEA"})," data type, which enhances query speed and, in some cases, improves space efficiency compared to the previously used ",(0,i.jsx)(t.code,{children:"JSONB"})," column. However, due to this change, the v2 component does not support the Dapr state store query APIs. Additionally, ETags are now random UUIDs, ensuring better compatibility with other PostgreSQL-compatible databases, such as CockroachDB. Importantly, v1 and v2 components are not compatible, and data cannot be migrated between the two versions."]}),"\n",(0,i.jsx)(t.h2,{id:"decision",children:"Decision"}),"\n",(0,i.jsxs)(t.p,{children:["We will use ",(0,i.jsx)(t.strong,{children:"PostgreSQL v2"})," as the state management backend for Dapr. PostgreSQL offers ACID-compliant transactions, ensuring data integrity across state operations. Compared to other options:"]}),"\n",(0,i.jsxs)(t.ul,{children:["\n",(0,i.jsxs)(t.li,{children:[(0,i.jsx)(t.strong,{children:"Redis"}),": Provides fast in-memory storage but lacks strong consistency guarantees and durability unless configured with persistence mechanisms."]}),"\n",(0,i.jsxs)(t.li,{children:[(0,i.jsx)(t.strong,{children:"etcd"}),": A strongly consistent key-value store optimized for distributed systems. However, it lacks the advanced querying capabilities, relational integrity, and built-in support for transactions that PostgreSQL v2 provides. Additionally, etcd is primarily designed for configuration management and service discovery rather than high-volume state persistence, which can impact its efficiency in some state management use cases."]}),"\n",(0,i.jsxs)(t.li,{children:[(0,i.jsx)(t.strong,{children:"Consul"}),": Offers key-value storage with strong consistency guarantees and service discovery capabilities. However, it lacks relational integrity, SQL-based querying, and transactional support, making it less suitable for structured state management compared to PostgreSQL v2."]}),"\n",(0,i.jsxs)(t.li,{children:[(0,i.jsx)(t.strong,{children:"Apache Cassandra"}),": A highly scalable, distributed NoSQL database that supports multi-region replication. However, it lacks ACID-compliant transactions, making it less suitable for workloads requiring strong consistency. Additionally, while Cassandra is highly available and fault-tolerant, it does not provide native SQL-based querying and requires additional tooling for analytics and complex queries compared to PostgreSQL v2."]}),"\n",(0,i.jsxs)(t.li,{children:[(0,i.jsx)(t.strong,{children:"PostgreSQL v1"}),": While viable, the v2 component offers performance enhancements, better indexing, and improved ",(0,i.jsx)(t.code,{children:"BYTEA"})," support for flexible data structures. However, it does not support the Dapr state store query APIs, which should be considered if query capabilities are required."]}),"\n"]}),"\n",(0,i.jsx)(t.p,{children:"PostgreSQL v2\u2019s combination of relational integrity, scalability improvements, and open-source nature makes it a preferred choice for state management in Dapr."}),"\n",(0,i.jsx)(t.h2,{id:"consequences",children:"Consequences"}),"\n",(0,i.jsx)(t.h3,{id:"positive-aspects",children:"Positive Aspects"}),"\n",(0,i.jsxs)(t.ul,{children:["\n",(0,i.jsx)(t.li,{children:"ACID transactions provide strong consistency and data integrity."}),"\n",(0,i.jsx)(t.li,{children:"SQL-based querying enables complex state retrieval and analytics."}),"\n",(0,i.jsx)(t.li,{children:"Open-source and widely adopted, reducing vendor lock-in."}),"\n",(0,i.jsx)(t.li,{children:"Already part of the existing technology stack, minimizing additional adoption efforts."}),"\n",(0,i.jsx)(t.li,{children:"Enhanced performance and reliability in v2 improve state management efficiency."}),"\n"]}),"\n",(0,i.jsx)(t.h3,{id:"negative-aspects",children:"Negative Aspects"}),"\n",(0,i.jsxs)(t.ul,{children:["\n",(0,i.jsx)(t.li,{children:"The v2 component does not support Dapr state store query APIs, which may limit querying capabilities."}),"\n",(0,i.jsx)(t.li,{children:"Requires careful schema design to optimize performance for high-scale applications."}),"\n",(0,i.jsx)(t.li,{children:"May introduce additional operational overhead for backups, scaling, and tuning."}),"\n"]}),"\n",(0,i.jsx)(t.h2,{id:"conclusion",children:"Conclusion"}),"\n",(0,i.jsx)(t.p,{children:"By adopting PostgreSQL v2, we ensure a robust, scalable, and future-proof solution for state management within Dapr. This choice enhances consistency, reliability, and integration within microservices architectures while aligning with modern database best practices."})]})}function p(e={}){const{wrapper:t}={...(0,a.R)(),...e.components};return t?(0,i.jsx)(t,{...e,children:(0,i.jsx)(d,{...e})}):d(e)}},8453:(e,t,s)=>{s.d(t,{R:()=>r,x:()=>o});var n=s(6540);const i={},a=n.createContext(i);function r(e){const t=n.useContext(a);return n.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function o(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:r(e.components),n.createElement(a.Provider,{value:t},e.children)}}}]);