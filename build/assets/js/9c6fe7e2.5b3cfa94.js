"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6682],{8453:(e,n,i)=>{i.d(n,{R:()=>a,x:()=>c});var r=i(6540);const t={},s=r.createContext(t);function a(e){const n=r.useContext(s);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function c(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:a(e.components),r.createElement(s.Provider,{value:n},e.children)}},9481:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>l,contentTitle:()=>c,default:()=>p,frontMatter:()=>a,metadata:()=>r,toc:()=>d});const r=JSON.parse('{"id":"ai-architecture/tools/old/api/model-management/experiment-tracking-api","title":"Experiment Tracking API Documentation","description":"The Experiment Tracking API provides endpoints for managing machine learning experiments, tracking metrics, and comparing model performance. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/model-management/experiment-tracking-api.md","sourceDirName":"ai-architecture/tools/old/api/model-management","slug":"/ai-architecture/tools/old/api/model-management/experiment-tracking-api","permalink":"/docs/ai-architecture/tools/old/api/model-management/experiment-tracking-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/model-management/experiment-tracking-api.md","tags":[],"version":"current","frontMatter":{"id":"experiment-tracking-api","title":"Experiment Tracking API Documentation","sidebar_label":"Experiment Tracking API"},"sidebar":"tutorialSidebar","previous":{"title":"Model Management","permalink":"/docs/ai-architecture/tools/old/api/model-management/"},"next":{"title":"Model Registry API","permalink":"/docs/ai-architecture/tools/old/api/model-management/model-registry-api"}}');var t=i(4848),s=i(8453);const a={id:"experiment-tracking-api",title:"Experiment Tracking API Documentation",sidebar_label:"Experiment Tracking API"},c="Experiment Tracking API Documentation",l={},d=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Experiments",id:"experiments",level:3},{value:"Create Experiment",id:"create-experiment",level:4},{value:"List Experiments",id:"list-experiments",level:4},{value:"Runs",id:"runs",level:3},{value:"Create Run",id:"create-run",level:4},{value:"Get Run Details",id:"get-run-details",level:4},{value:"Metrics",id:"metrics",level:3},{value:"Log Metrics",id:"log-metrics",level:4},{value:"Get Metrics History",id:"get-metrics-history",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,s.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"experiment-tracking-api-documentation",children:"Experiment Tracking API Documentation"})}),"\n",(0,t.jsx)(n.p,{children:"The Experiment Tracking API provides endpoints for managing machine learning experiments, tracking metrics, and comparing model performance. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,t.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"https://experiment-tracking.91.life/api/v1\n"})}),"\n",(0,t.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,t.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,t.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,t.jsx)(n.h3,{id:"experiments",children:"Experiments"}),"\n",(0,t.jsx)(n.h4,{id:"create-experiment",children:"Create Experiment"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /experiments\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "name": "customer_churn_prediction",\n    "description": "Predict customer churn using various features",\n    "tags": ["churn", "classification", "customer"],\n    "metrics": {\n        "primary": "f1_score",\n        "secondary": ["accuracy", "precision", "recall"]\n    },\n    "parameters": {\n        "model_type": "xgboost",\n        "max_depth": 6,\n        "learning_rate": 0.1\n    }\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "experiment_id": "exp_001",\n    "name": "customer_churn_prediction",\n    "status": "active",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"list-experiments",children:"List Experiments"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"GET /experiments\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"tags"})," (optional): Filter by tags"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"status"})," (optional): Filter by status"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"start_time"})," (optional): Start time for experiments"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"end_time"})," (optional): End time for experiments"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "experiments": [\n        {\n            "experiment_id": "exp_001",\n            "name": "customer_churn_prediction",\n            "description": "Predict customer churn using various features",\n            "tags": ["churn", "classification", "customer"],\n            "status": "active",\n            "created_at": "2024-03-20T10:00:00Z",\n            "last_updated": "2024-03-20T10:30:00Z"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"runs",children:"Runs"}),"\n",(0,t.jsx)(n.h4,{id:"create-run",children:"Create Run"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /experiments/{experiment_id}/runs\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "name": "run_001",\n    "parameters": {\n        "model_type": "xgboost",\n        "max_depth": 6,\n        "learning_rate": 0.1,\n        "n_estimators": 100\n    },\n    "metrics": {\n        "accuracy": 0.95,\n        "precision": 0.92,\n        "recall": 0.94,\n        "f1_score": 0.93\n    },\n    "artifacts": [\n        {\n            "name": "model",\n            "type": "model",\n            "uri": "s3://models/churn/xgboost/v1"\n        },\n        {\n            "name": "feature_importance",\n            "type": "plot",\n            "uri": "s3://plots/churn/feature_importance.png"\n        }\n    ]\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "run_id": "run_001",\n    "experiment_id": "exp_001",\n    "status": "completed",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"get-run-details",children:"Get Run Details"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"GET /experiments/{experiment_id}/runs/{run_id}\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "run_id": "run_001",\n    "experiment_id": "exp_001",\n    "name": "run_001",\n    "status": "completed",\n    "parameters": {\n        "model_type": "xgboost",\n        "max_depth": 6,\n        "learning_rate": 0.1,\n        "n_estimators": 100\n    },\n    "metrics": {\n        "accuracy": 0.95,\n        "precision": 0.92,\n        "recall": 0.94,\n        "f1_score": 0.93\n    },\n    "artifacts": [\n        {\n            "name": "model",\n            "type": "model",\n            "uri": "s3://models/churn/xgboost/v1"\n        },\n        {\n            "name": "feature_importance",\n            "type": "plot",\n            "uri": "s3://plots/churn/feature_importance.png"\n        }\n    ],\n    "created_at": "2024-03-20T10:00:00Z",\n    "completed_at": "2024-03-20T10:30:00Z"\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"metrics",children:"Metrics"}),"\n",(0,t.jsx)(n.h4,{id:"log-metrics",children:"Log Metrics"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /experiments/{experiment_id}/runs/{run_id}/metrics\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "metrics": {\n        "accuracy": 0.95,\n        "precision": 0.92,\n        "recall": 0.94,\n        "f1_score": 0.93\n    },\n    "step": 100,\n    "timestamp": "2024-03-20T10:30:00Z"\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "status": "recorded",\n    "timestamp": "2024-03-20T10:30:00Z"\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"get-metrics-history",children:"Get Metrics History"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"GET /experiments/{experiment_id}/runs/{run_id}/metrics\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"metric_names"})," (optional): Comma-separated list of metric names"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"start_step"})," (optional): Start step for metrics"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"end_step"})," (optional): End step for metrics"]}),"\n"]}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "metrics": {\n        "accuracy": [\n            {\n                "step": 100,\n                "value": 0.95,\n                "timestamp": "2024-03-20T10:30:00Z"\n            }\n        ],\n        "f1_score": [\n            {\n                "step": 100,\n                "value": 0.93,\n                "timestamp": "2024-03-20T10:30:00Z"\n            }\n        ]\n    }\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,t.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'from experiment_tracking import Client\n\n# Initialize client\nclient = Client(\n    host="https://experiment-tracking.91.life",\n    auth_token="your-token"\n)\n\n# Create experiment\nexperiment = {\n    "name": "customer_churn_prediction",\n    "description": "Predict customer churn using various features",\n    "tags": ["churn", "classification", "customer"]\n}\nexperiment_id = client.create_experiment(experiment)\n\n# Create run\nrun = {\n    "name": "run_001",\n    "parameters": {\n        "model_type": "xgboost",\n        "max_depth": 6,\n        "learning_rate": 0.1\n    },\n    "metrics": {\n        "accuracy": 0.95,\n        "f1_score": 0.93\n    }\n}\nrun_id = client.create_run(experiment_id, run)\n\n# Log metrics\nclient.log_metrics(\n    experiment_id=experiment_id,\n    run_id=run_id,\n    metrics={\n        "accuracy": 0.96,\n        "f1_score": 0.94\n    },\n    step=200\n)\n'})}),"\n",(0,t.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:'# Create experiment\ncurl -X POST https://experiment-tracking.91.life/api/v1/experiments \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "customer_churn_prediction",\n    "description": "Predict customer churn using various features",\n    "tags": ["churn", "classification", "customer"]\n  }\'\n\n# Create run\ncurl -X POST https://experiment-tracking.91.life/api/v1/experiments/exp_001/runs \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "run_001",\n    "parameters": {\n      "model_type": "xgboost",\n      "max_depth": 6,\n      "learning_rate": 0.1\n    },\n    "metrics": {\n      "accuracy": 0.95,\n      "f1_score": 0.93\n    }\n  }\'\n'})}),"\n",(0,t.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Code"}),(0,t.jsx)(n.th,{children:"Description"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"400"}),(0,t.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"401"}),(0,t.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"403"}),(0,t.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"404"}),(0,t.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"409"}),(0,t.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"429"}),(0,t.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"500"}),(0,t.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,t.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,t.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Experiment Organization"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Use meaningful experiment names"}),"\n",(0,t.jsx)(n.li,{children:"Add descriptive tags"}),"\n",(0,t.jsx)(n.li,{children:"Document experiment purpose"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Run Management"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Log all relevant parameters"}),"\n",(0,t.jsx)(n.li,{children:"Track key metrics"}),"\n",(0,t.jsx)(n.li,{children:"Save important artifacts"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Metrics Tracking"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Log metrics regularly"}),"\n",(0,t.jsx)(n.li,{children:"Use consistent metric names"}),"\n",(0,t.jsx)(n.li,{children:"Include timestamps"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Performance"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Batch metric logging"}),"\n",(0,t.jsx)(n.li,{children:"Use appropriate step intervals"}),"\n",(0,t.jsx)(n.li,{children:"Clean up old experiments"}),"\n"]}),"\n"]}),"\n"]})]})}function p(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(o,{...e})}):o(e)}}}]);