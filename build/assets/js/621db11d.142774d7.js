"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[4212],{217:(t,e,s)=>{s.r(e),s.d(e,{default:()=>m});s(6540);var a=s(4164),r=s(4737),o=s(5279),u=s(5989),l=s(569),n=s(7220),i=s(9303),c=s(5921);const h={authorListItem:"authorListItem_n3yI"};var g=s(4848);function p({author:t}){return(0,g.jsx)("li",{className:h.authorListItem,children:(0,g.jsx)(c.A,{as:"h2",author:t,count:t.count})})}function d({authors:t}){return(0,g.jsx)("section",{className:(0,a.A)("margin-vert--lg",h.authorsListSection),children:(0,g.jsx)("ul",{children:t.map((t=>(0,g.jsx)(p,{author:t},t.key)))})})}function m({authors:t,sidebar:e}){const s=(0,u.uz)();return(0,g.jsxs)(r.e3,{className:(0,a.A)(o.G.wrapper.blogPages,o.G.page.blogAuthorsListPage),children:[(0,g.jsx)(r.be,{title:s}),(0,g.jsx)(n.A,{tag:"blog_authors_list"}),(0,g.jsxs)(l.A,{sidebar:e,children:[(0,g.jsx)(i.A,{as:"h1",children:s}),(0,g.jsx)(d,{authors:t})]})]})}},5989:(t,e,s)=>{s.d(e,{ZD:()=>u,uz:()=>l});s(6540);var a=s(539),r=s(9057);s(4848);function o(){const{selectMessage:t}=(0,r.W)();return e=>t(e,(0,a.T)({id:"theme.blog.post.plurals",description:'Pluralized label for "{count} posts". Use as much plural forms (separated by "|") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)',message:"One post|{count} posts"},{count:e}))}function u(t){const e=o();return(0,a.T)({id:"theme.blog.tagTitle",description:"The title of the page for a blog tag",message:'{nPosts} tagged with "{tagName}"'},{nPosts:e(t.count),tagName:t.label})}const l=()=>(0,a.T)({id:"theme.blog.authorsList.pageTitle",message:"Authors",description:"The title of the authors page"})}}]);