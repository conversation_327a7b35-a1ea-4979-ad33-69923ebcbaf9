"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[648],{631:(n,e,r)=>{r.r(e),r.d(e,{assets:()=>a,contentTitle:()=>o,default:()=>h,frontMatter:()=>t,metadata:()=>s,toc:()=>d});const s=JSON.parse('{"id":"ai-architecture/tools/tools","title":"tools","description":"Tooling and Libraries","source":"@site/docs/ai-architecture/tools/tools.md","sourceDirName":"ai-architecture/tools","slug":"/ai-architecture/tools/","permalink":"/docs/ai-architecture/tools/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/tools.md","tags":[],"version":"current","frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"System Requirements","permalink":"/docs/ai-architecture/system-requirements/"},"next":{"title":"API Overview","permalink":"/docs/ai-architecture/tools/old/api/"}}');var i=r(4848),l=r(8453);const t={},o=void 0,a={},d=[{value:"Tooling and Libraries",id:"tooling-and-libraries",level:2},{value:"Core Infrastructure",id:"core-infrastructure",level:3},{value:"Data Processing and Storage",id:"data-processing-and-storage",level:3},{value:"Machine Learning and AI",id:"machine-learning-and-ai",level:3},{value:"Feature Engineering and Management",id:"feature-engineering-and-management",level:3},{value:"Monitoring and Observability",id:"monitoring-and-observability",level:3},{value:"Workflow and Orchestration",id:"workflow-and-orchestration",level:3},{value:"Security and Access Control",id:"security-and-access-control",level:3},{value:"Development and Testing",id:"development-and-testing",level:3},{value:"Integration and APIs",id:"integration-and-apis",level:3},{value:"Data Quality and Validation",id:"data-quality-and-validation",level:3},{value:"Model Management and Deployment",id:"model-management-and-deployment",level:3},{value:"Development Tools and SDKs",id:"development-tools-and-sdks",level:3},{value:"Documentation and Knowledge Base",id:"documentation-and-knowledge-base",level:3},{value:"Community and Support",id:"community-and-support",level:3}];function c(n){const e={h2:"h2",h3:"h3",li:"li",strong:"strong",ul:"ul",...(0,l.R)(),...n.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e.h2,{id:"tooling-and-libraries",children:"Tooling and Libraries"}),"\n",(0,i.jsx)(e.h3,{id:"core-infrastructure",children:"Core Infrastructure"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Kubernetes"}),": Container orchestration platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Helm"}),": Package manager for Kubernetes"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Docker"}),": Container runtime"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Istio"}),": Service mesh for traffic management and security"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Cert-Manager"}),": Certificate management for Kubernetes"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"External-DNS"}),": DNS management for Kubernetes resources"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"NGINX Ingress Controller"}),": Kubernetes ingress controller"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"data-processing-and-storage",children:"Data Processing and Storage"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Trino"}),": Distributed SQL query engine"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"LakeFS"}),": Data lake version control"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"MinIO"}),": S3-compatible object storage"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"PostgreSQL"}),": Relational database for metadata"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"MongoDB"}),": Document database for flexible data storage"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Redis"}),": In-memory data store for caching"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Apache Kafka"}),": Distributed event streaming platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Apache Spark"}),": Unified analytics engine"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Apache Flink"}),": Stream processing framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Apache Beam"}),": Unified programming model for batch and streaming"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"machine-learning-and-ai",children:"Machine Learning and AI"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Kubeflow"}),": ML toolkit for Kubernetes"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"MLflow"}),": Experiment tracking and model registry"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"TensorFlow"}),": Open-source ML framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"PyTorch"}),": Deep learning framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Scikit-learn"}),": Machine learning library"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"XGBoost"}),": Gradient boosting framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"LightGBM"}),": Gradient boosting framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Hugging Face Transformers"}),": State-of-the-art NLP"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"ONNX"}),": Open Neural Network Exchange"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"TensorRT"}),": High-performance deep learning inference"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"KServe"}),": Model serving platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Seldon Core"}),": Model serving and monitoring"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"BentoML"}),": Model serving framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Ray"}),": Distributed computing framework for ML"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"feature-engineering-and-management",children:"Feature Engineering and Management"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Feast"}),": Feature store for ML"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Hopsworks"}),": Feature store platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Tecton"}),": Enterprise feature platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Featureform"}),": Feature store framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"DVC"}),": Data version control"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Great Expectations"}),": Data quality and validation"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Pandas"}),": Data manipulation and analysis"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"NumPy"}),": Scientific computing"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Vaex"}),": Out-of-core DataFrames"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"monitoring-and-observability",children:"Monitoring and Observability"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Prometheus"}),": Monitoring and alerting"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Grafana"}),": Visualization and analytics"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Jaeger"}),": Distributed tracing"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"ELK Stack"}),": Log management and analysis"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"OpenTelemetry"}),": Observability framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Evidently"}),": ML monitoring"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Arize"}),": ML observability"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Weights & Biases"}),": Experiment tracking"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Neptune"}),": Experiment tracking"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Comet"}),": Experiment tracking"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"workflow-and-orchestration",children:"Workflow and Orchestration"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Apache Airflow"}),": Workflow orchestration"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Prefect"}),": Workflow management"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Dagster"}),": Data orchestration"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Kedro"}),": Data pipeline framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Metaflow"}),": ML pipeline framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Flyte"}),": Workflow automation platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Argo"}),": Kubernetes-native workflow engine"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Tekton"}),": Cloud-native CI/CD"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"security-and-access-control",children:"Security and Access Control"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Dex"}),": OIDC provider"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Keycloak"}),": Identity and access management"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Vault"}),": Secrets management"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"OPA"}),": Policy enforcement"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Falco"}),": Container security"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Trivy"}),": Container vulnerability scanner"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"SonarQube"}),": Code quality and security"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Snyk"}),": Security scanning"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"development-and-testing",children:"Development and Testing"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Jupyter"}),": Interactive computing"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"VS Code"}),": IDE with ML extensions"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Git"}),": Version control"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"GitHub Actions"}),": CI/CD"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Pytest"}),": Testing framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Black"}),": Code formatting"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Flake8"}),": Linting"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Mypy"}),": Static type checking"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Sphinx"}),": Documentation generation"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Docusaurus"}),": Documentation website"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"integration-and-apis",children:"Integration and APIs"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"FastAPI"}),": Modern API framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"gRPC"}),": High-performance RPC framework"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"GraphQL"}),": Query language for APIs"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Swagger/OpenAPI"}),": API documentation"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Postman"}),": API development and testing"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Kong"}),": API gateway"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Tyk"}),": API management"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"data-quality-and-validation",children:"Data Quality and Validation"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Great Expectations"}),": Data validation"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Deequ"}),": Data quality"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Soda SQL"}),": Data testing"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"DataHub"}),": Metadata platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Amundsen"}),": Data discovery"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Marquez"}),": Data lineage"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"OpenLineage"}),": Data lineage standard"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"model-management-and-deployment",children:"Model Management and Deployment"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"MLflow"}),": Model lifecycle management"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"BentoML"}),": Model serving"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Cortex"}),": Model serving"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Triton"}),": Inference server"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"TorchServe"}),": PyTorch model serving"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"TensorFlow Serving"}),": TF model serving"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"ONNX Runtime"}),": Model inference"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"ModelMesh"}),": Model serving platform"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"development-tools-and-sdks",children:"Development Tools and SDKs"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Python SDK"}),": Core development"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Java SDK"}),": Enterprise integration"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Go SDK"}),": High-performance services"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Node.js SDK"}),": Web services"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Rust SDK"}),": Systems programming"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"CLI Tools"}),": Command-line interface"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"REST API"}),": HTTP interface"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"gRPC API"}),": High-performance interface"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"documentation-and-knowledge-base",children:"Documentation and Knowledge Base"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Docusaurus"}),": Documentation website"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"MkDocs"}),": Documentation generator"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Sphinx"}),": Documentation generator"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Swagger"}),": API documentation"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Postman"}),": API documentation"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Confluence"}),": Knowledge base"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Notion"}),": Documentation"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"GitBook"}),": Documentation"]}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"community-and-support",children:"Community and Support"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Slack"}),": Community communication"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Discord"}),": Community platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"GitHub"}),": Code hosting"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Stack Overflow"}),": Q&A platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Medium"}),": Blog platform"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"YouTube"}),": Video tutorials"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"Meetup"}),": Community events"]}),"\n",(0,i.jsxs)(e.li,{children:[(0,i.jsx)(e.strong,{children:"LinkedIn"}),": Professional network"]}),"\n"]})]})}function h(n={}){const{wrapper:e}={...(0,l.R)(),...n.components};return e?(0,i.jsx)(e,{...n,children:(0,i.jsx)(c,{...n})}):c(n)}},8453:(n,e,r)=>{r.d(e,{R:()=>t,x:()=>o});var s=r(6540);const i={},l=s.createContext(i);function t(n){const e=s.useContext(l);return s.useMemo((function(){return"function"==typeof n?n(e):{...e,...n}}),[e,n])}function o(n){let e;return e=n.disableParentContext?"function"==typeof n.components?n.components(i):n.components||i:t(n.components),s.createElement(l.Provider,{value:e},n.children)}}}]);