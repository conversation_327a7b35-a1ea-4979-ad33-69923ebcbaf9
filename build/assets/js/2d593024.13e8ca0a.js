"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[356],{837:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>a,contentTitle:()=>l,default:()=>h,frontMatter:()=>o,metadata:()=>s,toc:()=>c});const s=JSON.parse('{"id":"adrs/platform/go-database-communication","title":"5. Database Communication in Go","description":"Date: 2024-03-19","source":"@site/docs/adrs/platform/0009-go-database-communication.md","sourceDirName":"adrs/platform","slug":"/adrs/platform/go-database-communication","permalink":"/docs/adrs/platform/go-database-communication","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/platform/0009-go-database-communication.md","tags":[],"version":"current","sidebarPosition":9,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"4. Go Libraries and Components","permalink":"/docs/adrs/platform/go-libraries"},"next":{"title":"6. Feature Flags and A/B Testing Platform","permalink":"/docs/adrs/platform/feature-flags"}}');var r=i(4848),t=i(8453);const o={},l="5. Database Communication in Go",a={},c=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2},{value:"Benefits",id:"benefits",level:3},{value:"Risks &amp; Mitigation",id:"risks--mitigation",level:3},{value:"Implementation Notes",id:"implementation-notes",level:2}];function d(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"5-database-communication-in-go",children:"5. Database Communication in Go"})}),"\n",(0,r.jsx)(n.p,{children:"Date: 2024-03-19"}),"\n",(0,r.jsx)(n.h2,{id:"status",children:"Status"}),"\n",(0,r.jsx)(n.p,{children:"Proposed"}),"\n",(0,r.jsx)(n.h2,{id:"context",children:"Context"}),"\n",(0,r.jsx)(n.p,{children:"Our Go services need efficient database communication with options for both high performance and developer productivity."}),"\n",(0,r.jsx)(n.h2,{id:"decision",children:"Decision"}),"\n",(0,r.jsx)(n.p,{children:"We will use:"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Migrations"}),": ",(0,r.jsx)(n.code,{children:"golang-migrate/migrate"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Version-controlled schema changes"}),"\n",(0,r.jsx)(n.li,{children:"Multi-database support"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"High Performance"}),": ",(0,r.jsx)(n.code,{children:"jackc/pgx"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Native PostgreSQL driver"}),"\n",(0,r.jsx)(n.li,{children:"Connection pooling"}),"\n",(0,r.jsxs)(n.li,{children:["Used for:","\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"High-performance requirements"}),"\n",(0,r.jsx)(n.li,{children:"Complex queries"}),"\n",(0,r.jsx)(n.li,{children:"PostgreSQL-specific features"}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"ORM"}),": ",(0,r.jsx)(n.code,{children:"gorm.io/gorm"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Developer-friendly API"}),"\n",(0,r.jsxs)(n.li,{children:["Used for:","\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Rapid development"}),"\n",(0,r.jsx)(n.li,{children:"Complex relationships"}),"\n",(0,r.jsx)(n.li,{children:"CRUD-heavy applications"}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"consequences",children:"Consequences"}),"\n",(0,r.jsx)(n.h3,{id:"benefits",children:"Benefits"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Flexible approach for different needs"}),"\n",(0,r.jsx)(n.li,{children:"High performance when required"}),"\n",(0,r.jsx)(n.li,{children:"Developer productivity where needed"}),"\n",(0,r.jsx)(n.li,{children:"Consistent migration strategy"}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"risks--mitigation",children:"Risks & Mitigation"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Library selection: Clear usage guidelines"}),"\n",(0,r.jsx)(n.li,{children:"Performance: Define requirements upfront"}),"\n",(0,r.jsx)(n.li,{children:"Migrations: Standardize workflow"}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"implementation-notes",children:"Implementation Notes"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsx)(n.li,{children:"Use pgx for high-performance needs"}),"\n",(0,r.jsx)(n.li,{children:"Use GORM for rapid development"}),"\n",(0,r.jsx)(n.li,{children:"Always use migrations for schema changes"}),"\n",(0,r.jsx)(n.li,{children:"Document performance requirements"}),"\n",(0,r.jsx)(n.li,{children:"Regular performance monitoring"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(d,{...e})}):d(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>o,x:()=>l});var s=i(6540);const r={},t=s.createContext(r);function o(e){const n=s.useContext(t);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:o(e.components),s.createElement(t.Provider,{value:n},e.children)}}}]);