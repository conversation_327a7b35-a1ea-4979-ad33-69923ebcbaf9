"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6188],{3893:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>d,contentTitle:()=>l,default:()=>h,frontMatter:()=>r,metadata:()=>i,toc:()=>c});const i=JSON.parse('{"id":"ai-architecture/tools/old/api/data-management/openmetadata-api","title":"OpenMetadata API Documentation","description":"OpenMetadata provides a RESTful API for managing metadata and data lineage. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/data-management/openmetadata-api.md","sourceDirName":"ai-architecture/tools/old/api/data-management","slug":"/ai-architecture/tools/old/api/data-management/openmetadata-api","permalink":"/docs/ai-architecture/tools/old/api/data-management/openmetadata-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/data-management/openmetadata-api.md","tags":[],"version":"current","frontMatter":{"id":"openmetadata-api","title":"OpenMetadata API Documentation","sidebar_label":"OpenMetadata API"},"sidebar":"tutorialSidebar","previous":{"title":"MinIO API","permalink":"/docs/ai-architecture/tools/old/api/data-management/minio-api"},"next":{"title":"Trino API","permalink":"/docs/ai-architecture/tools/old/api/data-management/trino-api"}}');var a=t(4848),s=t(8453);const r={id:"openmetadata-api",title:"OpenMetadata API Documentation",sidebar_label:"OpenMetadata API"},l="OpenMetadata API Documentation",d={},c=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Entity Management",id:"entity-management",level:3},{value:"Create Entity",id:"create-entity",level:4},{value:"List Entities",id:"list-entities",level:4},{value:"Lineage Management",id:"lineage-management",level:3},{value:"Create Lineage",id:"create-lineage",level:4},{value:"Get Lineage",id:"get-lineage",level:4},{value:"Tag Management",id:"tag-management",level:3},{value:"Create Tag",id:"create-tag",level:4},{value:"List Tags",id:"list-tags",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,s.R)(),...e.components};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.header,{children:(0,a.jsx)(n.h1,{id:"openmetadata-api-documentation",children:"OpenMetadata API Documentation"})}),"\n",(0,a.jsx)(n.p,{children:"OpenMetadata provides a RESTful API for managing metadata and data lineage. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,a.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"https://openmetadata.91.life/api/v1\n"})}),"\n",(0,a.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,a.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,a.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,a.jsx)(n.h3,{id:"entity-management",children:"Entity Management"}),"\n",(0,a.jsx)(n.h4,{id:"create-entity",children:"Create Entity"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"POST /entities\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "name": "customer_table",\n    "description": "Customer information table",\n    "type": "table",\n    "service": "hive",\n    "database": "sales",\n    "schema": "public",\n    "columns": [\n        {\n            "name": "customer_id",\n            "type": "INT64",\n            "description": "Unique customer identifier",\n            "tags": ["PII", "primary_key"]\n        },\n        {\n            "name": "email",\n            "type": "STRING",\n            "description": "Customer email address",\n            "tags": ["PII", "contact"]\n        }\n    ],\n    "tags": {\n        "domain": "customer",\n        "tier": "tier1"\n    }\n}\n'})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "id": "customer_table",\n    "name": "customer_table",\n    "description": "Customer information table",\n    "type": "table",\n    "service": "hive",\n    "database": "sales",\n    "schema": "public",\n    "created_at": "2024-03-20T10:00:00Z",\n    "updated_at": "2024-03-20T10:00:00Z",\n    "version": 1\n}\n'})}),"\n",(0,a.jsx)(n.h4,{id:"list-entities",children:"List Entities"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"GET /entities\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"type"})," (optional): Filter by entity type"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"service"})," (optional): Filter by service"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"tag"})," (optional): Filter by tag"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "entities": [\n        {\n            "id": "customer_table",\n            "name": "customer_table",\n            "description": "Customer information table",\n            "type": "table",\n            "service": "hive",\n            "database": "sales",\n            "schema": "public",\n            "created_at": "2024-03-20T10:00:00Z",\n            "updated_at": "2024-03-20T10:00:00Z",\n            "version": 1\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,a.jsx)(n.h3,{id:"lineage-management",children:"Lineage Management"}),"\n",(0,a.jsx)(n.h4,{id:"create-lineage",children:"Create Lineage"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"POST /lineage\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "source": {\n        "id": "customer_table",\n        "type": "table"\n    },\n    "target": {\n        "id": "customer_features",\n        "type": "feature"\n    },\n    "description": "Customer features derived from customer table",\n    "type": "derived",\n    "tags": {\n        "pipeline": "feature_engineering",\n        "version": "v1"\n    }\n}\n'})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "id": "lineage_001",\n    "source": {\n        "id": "customer_table",\n        "type": "table"\n    },\n    "target": {\n        "id": "customer_features",\n        "type": "feature"\n    },\n    "description": "Customer features derived from customer table",\n    "type": "derived",\n    "created_at": "2024-03-20T10:00:00Z",\n    "version": 1\n}\n'})}),"\n",(0,a.jsx)(n.h4,{id:"get-lineage",children:"Get Lineage"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"GET /lineage/{entity_id}\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"depth"})," (optional): Lineage depth to retrieve"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"direction"}),' (optional): "upstream" or "downstream"']}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "entity": {\n        "id": "customer_features",\n        "type": "feature"\n    },\n    "lineage": {\n        "upstream": [\n            {\n                "id": "customer_table",\n                "type": "table",\n                "relationship": "derived"\n            }\n        ],\n        "downstream": [\n            {\n                "id": "customer_model",\n                "type": "model",\n                "relationship": "used_by"\n            }\n        ]\n    }\n}\n'})}),"\n",(0,a.jsx)(n.h3,{id:"tag-management",children:"Tag Management"}),"\n",(0,a.jsx)(n.h4,{id:"create-tag",children:"Create Tag"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"POST /tags\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "name": "PII",\n    "description": "Personally Identifiable Information",\n    "category": "data_classification",\n    "color": "#FF0000"\n}\n'})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "id": "PII",\n    "name": "PII",\n    "description": "Personally Identifiable Information",\n    "category": "data_classification",\n    "color": "#FF0000",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,a.jsx)(n.h4,{id:"list-tags",children:"List Tags"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-http",children:"GET /tags\n"})}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"category"})," (optional): Filter by category"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Response:"})}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n    "tags": [\n        {\n            "id": "PII",\n            "name": "PII",\n            "description": "Personally Identifiable Information",\n            "category": "data_classification",\n            "color": "#FF0000",\n            "created_at": "2024-03-20T10:00:00Z"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,a.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-python",children:'from openmetadata import Client\n\n# Initialize client\nclient = Client(\n    host="https://openmetadata.91.life",\n    auth_token="your-token"\n)\n\n# Create entity\nentity = {\n    "name": "customer_table",\n    "description": "Customer information table",\n    "type": "table",\n    "service": "hive",\n    "database": "sales",\n    "schema": "public",\n    "columns": [\n        {\n            "name": "customer_id",\n            "type": "INT64",\n            "description": "Unique customer identifier",\n            "tags": ["PII", "primary_key"]\n        }\n    ]\n}\nentity_id = client.create_entity(entity)\n\n# Get lineage\nlineage = client.get_lineage(\n    entity_id="customer_features",\n    depth=2,\n    direction="upstream"\n)\nprint(lineage)\n'})}),"\n",(0,a.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-bash",children:'# Create entity\ncurl -X POST https://openmetadata.91.life/api/v1/entities \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "customer_table",\n    "description": "Customer information table",\n    "type": "table",\n    "service": "hive",\n    "database": "sales",\n    "schema": "public",\n    "columns": [\n      {\n        "name": "customer_id",\n        "type": "INT64",\n        "description": "Unique customer identifier",\n        "tags": ["PII", "primary_key"]\n      }\n    ]\n  }\'\n\n# Get lineage\ncurl -X GET "https://openmetadata.91.life/api/v1/lineage/customer_features?depth=2&direction=upstream" \\\n  -H "Authorization: Bearer ${TOKEN}"\n'})}),"\n",(0,a.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,a.jsxs)(n.table,{children:[(0,a.jsx)(n.thead,{children:(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.th,{children:"Code"}),(0,a.jsx)(n.th,{children:"Description"})]})}),(0,a.jsxs)(n.tbody,{children:[(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"400"}),(0,a.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"401"}),(0,a.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"403"}),(0,a.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"404"}),(0,a.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"409"}),(0,a.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"429"}),(0,a.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"500"}),(0,a.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,a.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,a.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,a.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,a.jsxs)(n.li,{children:[(0,a.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,a.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,a.jsxs)(n.ol,{children:["\n",(0,a.jsxs)(n.li,{children:["\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Entity Management"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Use consistent naming conventions"}),"\n",(0,a.jsx)(n.li,{children:"Provide detailed descriptions"}),"\n",(0,a.jsx)(n.li,{children:"Tag entities appropriately"}),"\n"]}),"\n"]}),"\n",(0,a.jsxs)(n.li,{children:["\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Lineage Tracking"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Document all data dependencies"}),"\n",(0,a.jsx)(n.li,{children:"Keep lineage up to date"}),"\n",(0,a.jsx)(n.li,{children:"Use appropriate relationship types"}),"\n"]}),"\n"]}),"\n",(0,a.jsxs)(n.li,{children:["\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Tag Management"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Use standardized tag categories"}),"\n",(0,a.jsx)(n.li,{children:"Document tag meanings"}),"\n",(0,a.jsx)(n.li,{children:"Apply tags consistently"}),"\n"]}),"\n"]}),"\n",(0,a.jsxs)(n.li,{children:["\n",(0,a.jsx)(n.p,{children:(0,a.jsx)(n.strong,{children:"Security"})}),"\n",(0,a.jsxs)(n.ul,{children:["\n",(0,a.jsx)(n.li,{children:"Implement proper access controls"}),"\n",(0,a.jsx)(n.li,{children:"Monitor metadata changes"}),"\n",(0,a.jsx)(n.li,{children:"Audit tag usage"}),"\n"]}),"\n"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,a.jsx)(n,{...e,children:(0,a.jsx)(o,{...e})}):o(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>r,x:()=>l});var i=t(6540);const a={},s=i.createContext(a);function r(e){const n=i.useContext(s);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(a):e.components||a:r(e.components),i.createElement(s.Provider,{value:n},e.children)}}}]);