"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6524],{4938:(e,a,n)=>{n.r(a),n.d(a,{assets:()=>o,contentTitle:()=>l,default:()=>p,frontMatter:()=>s,metadata:()=>i,toc:()=>c});const i=JSON.parse('{"id":"ai-architecture/tools/old/api/visualization/api-diagrams","title":"API Interaction Diagrams","description":"This page contains diagrams illustrating the interactions between different components of the MLOps platform.","source":"@site/docs/ai-architecture/tools/old/api/visualization/diagrams.md","sourceDirName":"ai-architecture/tools/old/api/visualization","slug":"/ai-architecture/tools/old/api/visualization/api-diagrams","permalink":"/docs/ai-architecture/tools/old/api/visualization/api-diagrams","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/visualization/diagrams.md","tags":[],"version":"current","frontMatter":{"id":"api-diagrams","title":"API Interaction Diagrams","sidebar_label":"API Diagrams"},"sidebar":"tutorialSidebar","previous":{"title":"Visualization","permalink":"/docs/ai-architecture/tools/old/api/visualization/"},"next":{"title":"R&D Detailed Scenarios and Flows","permalink":"/docs/ai-architecture/tools/old/architecture/medical-device-detailed-scenarios"}}');var t=n(4848),r=n(8453);const s={id:"api-diagrams",title:"API Interaction Diagrams",sidebar_label:"API Diagrams"},l="API Interaction Diagrams",o={},c=[{value:"Data Flow Architecture",id:"data-flow-architecture",level:2},{value:"Model Training Pipeline",id:"model-training-pipeline",level:2},{value:"Feature Serving Flow",id:"feature-serving-flow",level:2},{value:"Metadata Management",id:"metadata-management",level:2},{value:"Model Deployment Flow",id:"model-deployment-flow",level:2},{value:"Data Versioning Flow",id:"data-versioning-flow",level:2},{value:"Feature Engineering Pipeline",id:"feature-engineering-pipeline",level:2},{value:"Model Monitoring Flow",id:"model-monitoring-flow",level:2},{value:"Data Quality Validation Flow",id:"data-quality-validation-flow",level:2}];function d(e){const a={code:"code",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",...(0,r.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(a.header,{children:(0,t.jsx)(a.h1,{id:"api-interaction-diagrams",children:"API Interaction Diagrams"})}),"\n",(0,t.jsx)(a.p,{children:"This page contains diagrams illustrating the interactions between different components of the MLOps platform."}),"\n",(0,t.jsx)(a.h2,{id:"data-flow-architecture",children:"Data Flow Architecture"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"graph TD\n    A[Data Sources] --\x3e|Ingest| B[LakeFS]\n    B --\x3e|Version Control| C[MinIO]\n    C --\x3e|Query| D[Trino]\n    D --\x3e|Feature Engineering| E[Feature Store]\n    E --\x3e|Training| F[MLflow]\n    F --\x3e|Deploy| G[KServe]\n    H[Kubeflow] --\x3e|Orchestrate| F\n    I[OpenMetadata] --\x3e|Track| A & B & C & D & E & F & G\n"})}),"\n",(0,t.jsx)(a.h2,{id:"model-training-pipeline",children:"Model Training Pipeline"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"sequenceDiagram\n    participant D as Data Scientist\n    participant K as Kubeflow\n    participant L as LakeFS\n    participant M as MLflow\n    participant F as Feature Store\n    participant S as KServe\n\n    D->>K: Submit Training Pipeline\n    K->>L: Checkout Data Version\n    K->>F: Get Features\n    K->>M: Start Experiment\n    K->>M: Log Parameters\n    K->>M: Log Metrics\n    K->>M: Register Model\n    M->>S: Deploy Model\n    S--\x3e>D: Model Endpoint\n"})}),"\n",(0,t.jsx)(a.h2,{id:"feature-serving-flow",children:"Feature Serving Flow"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"sequenceDiagram\n    participant C as Client\n    participant F as Feature Store\n    participant M as MinIO\n    participant T as Trino\n    participant S as KServe\n\n    C->>F: Request Features\n    F->>T: Query Latest Features\n    T->>M: Read Feature Data\n    M--\x3e>T: Return Data\n    T--\x3e>F: Process Features\n    F--\x3e>C: Return Features\n    C->>S: Model Inference\n    S--\x3e>C: Predictions\n"})}),"\n",(0,t.jsx)(a.h2,{id:"metadata-management",children:"Metadata Management"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"graph TD\n    A[Data Sources] --\x3e|Register| B[OpenMetadata]\n    C[Models] --\x3e|Register| B\n    D[Features] --\x3e|Register| B\n    E[Pipelines] --\x3e|Register| B\n    B --\x3e|Lineage| F[Lineage Graph]\n    B --\x3e|Tags| G[Tag Management]\n    B --\x3e|Search| H[Metadata Search]\n"})}),"\n",(0,t.jsx)(a.h2,{id:"model-deployment-flow",children:"Model Deployment Flow"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"sequenceDiagram\n    participant D as Data Scientist\n    participant M as MLflow\n    participant K as KServe\n    participant O as OpenMetadata\n    participant C as Client\n\n    D->>M: Register Model\n    M->>K: Deploy Model\n    K->>O: Register Deployment\n    O->>O: Track Lineage\n    C->>K: Inference Request\n    K--\x3e>C: Prediction\n    K->>O: Log Usage\n"})}),"\n",(0,t.jsx)(a.h2,{id:"data-versioning-flow",children:"Data Versioning Flow"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"sequenceDiagram\n    participant D as Data Engineer\n    participant L as LakeFS\n    participant M as MinIO\n    participant O as OpenMetadata\n\n    D->>L: Create Branch\n    L->>M: Write Data\n    M--\x3e>L: Confirm Write\n    L->>L: Commit Changes\n    L->>O: Register Version\n    O->>O: Update Lineage\n"})}),"\n",(0,t.jsx)(a.h2,{id:"feature-engineering-pipeline",children:"Feature Engineering Pipeline"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"graph TD\n    A[Raw Data] --\x3e|Extract| B[Feature Engineering]\n    B --\x3e|Transform| C[Feature Store]\n    C --\x3e|Validate| D[Feature Registry]\n    D --\x3e|Serve| E[Model Training]\n    D --\x3e|Serve| F[Model Serving]\n    G[OpenMetadata] --\x3e|Track| A & B & C & D & E & F\n"})}),"\n",(0,t.jsx)(a.h2,{id:"model-monitoring-flow",children:"Model Monitoring Flow"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"sequenceDiagram\n    participant C as Client\n    participant S as KServe\n    participant M as MLflow\n    participant O as OpenMetadata\n\n    C->>S: Inference Request\n    S--\x3e>C: Prediction\n    S->>M: Log Metrics\n    S->>O: Log Usage\n    O->>O: Update Lineage\n    M->>M: Track Performance\n"})}),"\n",(0,t.jsx)(a.h2,{id:"data-quality-validation-flow",children:"Data Quality Validation Flow"}),"\n",(0,t.jsx)(a.pre,{children:(0,t.jsx)(a.code,{className:"language-mermaid",children:"sequenceDiagram\n    participant D as Data Source\n    participant V as Validation Service\n    participant P as Profiling Service\n    participant M as Monitoring Service\n    participant A as Alerting Service\n    participant N as Notification Service\n\n    D->>V: Submit Data\n    V->>V: Apply Validation Rules\n    V->>P: Trigger Profiling\n    P->>P: Calculate Metrics\n    P->>M: Log Quality Metrics\n    M->>A: Check Thresholds\n    alt Quality Issues Detected\n        A->>N: Send Alert\n        N--\x3e>A: Alert Sent\n    end\n    V--\x3e>D: Validation Results\n"})}),"\n",(0,t.jsx)(a.p,{children:"These diagrams provide a visual representation of how different components of the MLOps platform interact with each other. They help in understanding:"}),"\n",(0,t.jsxs)(a.ol,{children:["\n",(0,t.jsx)(a.li,{children:"Data flow between components"}),"\n",(0,t.jsx)(a.li,{children:"Sequence of operations in various processes"}),"\n",(0,t.jsx)(a.li,{children:"Dependencies between different services"}),"\n",(0,t.jsx)(a.li,{children:"Integration points in the platform"}),"\n",(0,t.jsx)(a.li,{children:"Monitoring and tracking flows"}),"\n"]}),"\n",(0,t.jsx)(a.p,{children:"For more detailed information about each component, please refer to their respective API documentation pages."})]})}function p(e={}){const{wrapper:a}={...(0,r.R)(),...e.components};return a?(0,t.jsx)(a,{...e,children:(0,t.jsx)(d,{...e})}):d(e)}},8453:(e,a,n)=>{n.d(a,{R:()=>s,x:()=>l});var i=n(6540);const t={},r=i.createContext(t);function s(e){const a=i.useContext(r);return i.useMemo((function(){return"function"==typeof e?e(a):{...a,...e}}),[a,e])}function l(e){let a;return a=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:s(e.components),i.createElement(r.Provider,{value:a},e.children)}}}]);