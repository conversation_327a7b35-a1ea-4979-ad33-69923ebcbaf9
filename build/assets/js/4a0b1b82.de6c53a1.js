"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[4043],{5057:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>o,contentTitle:()=>a,default:()=>h,frontMatter:()=>d,metadata:()=>i,toc:()=>l});const i=JSON.parse('{"id":"ai-architecture/api/auth/tokens","title":"API Tokens","description":"Manage API tokens for authentication and access control.","source":"@site/docs/ai-architecture/api/auth/tokens.md","sourceDirName":"ai-architecture/api/auth","slug":"/ai-architecture/api/auth/tokens","permalink":"/docs/ai-architecture/api/auth/tokens","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/auth/tokens.md","tags":[],"version":"current","sidebarPosition":3,"frontMatter":{"sidebar_position":3},"sidebar":"tutorialSidebar","previous":{"title":"Authorization","permalink":"/docs/ai-architecture/api/auth/authorization"},"next":{"title":"Data Upload","permalink":"/docs/ai-architecture/api/data/upload"}}');var s=t(4848),r=t(8453);const d={sidebar_position:3},a="API Tokens",o={},l=[{value:"Create Token",id:"create-token",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"List Tokens",id:"list-tokens",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Revoke Token",id:"revoke-token",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Token Status",id:"token-status",level:2},{value:"Token Types",id:"token-types",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Token Best Practices",id:"token-best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,r.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"api-tokens",children:"API Tokens"})}),"\n",(0,s.jsx)(n.p,{children:"Manage API tokens for authentication and access control."}),"\n",(0,s.jsx)(n.h2,{id:"create-token",children:"Create Token"}),"\n",(0,s.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"POST /v1/auth/tokens\n"})}),"\n",(0,s.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n  "name": "production_token",\n  "description": "Token for production environment",\n  "expires_at": "2025-03-14T12:00:00Z",\n  "permissions": [\n    "read:models",\n    "write:models",\n    "read:data"\n  ],\n  "metadata": {\n    "environment": "production",\n    "purpose": "model_deployment"\n  }\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "token": {\n      "id": "token_123",\n      "name": "production_token",\n      "key": "sk_live_123456789",\n      "created_at": "2024-03-14T12:00:00Z",\n      "expires_at": "2025-03-14T12:00:00Z",\n      "permissions": [\n        "read:models",\n        "write:models",\n        "read:data"\n      ]\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"list-tokens",children:"List Tokens"}),"\n",(0,s.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"GET /v1/auth/tokens\n"})}),"\n",(0,s.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Parameter"}),(0,s.jsx)(n.th,{children:"Type"}),(0,s.jsx)(n.th,{children:"Description"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"page"}),(0,s.jsx)(n.td,{children:"integer"}),(0,s.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"limit"}),(0,s.jsx)(n.td,{children:"integer"}),(0,s.jsx)(n.td,{children:"Items per page (default: 10)"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"status"}),(0,s.jsx)(n.td,{children:"string"}),(0,s.jsx)(n.td,{children:"Filter by status"})]})]})]}),"\n",(0,s.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "tokens": [\n      {\n        "id": "token_123",\n        "name": "production_token",\n        "created_at": "2024-03-14T12:00:00Z",\n        "expires_at": "2025-03-14T12:00:00Z",\n        "status": "active",\n        "last_used": "2024-03-14T12:30:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:30:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"revoke-token",children:"Revoke Token"}),"\n",(0,s.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"DELETE /v1/auth/tokens/{token_id}\n"})}),"\n",(0,s.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "token": {\n      "id": "token_123",\n      "status": "revoked",\n      "revoked_at": "2024-03-14T12:30:00Z"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:30:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"token-status",children:"Token Status"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Status"}),(0,s.jsx)(n.th,{children:"Description"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"active"}),(0,s.jsx)(n.td,{children:"Token is active"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"expired"}),(0,s.jsx)(n.td,{children:"Token has expired"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"revoked"}),(0,s.jsx)(n.td,{children:"Token has been revoked"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"suspended"}),(0,s.jsx)(n.td,{children:"Token is temporarily suspended"})]})]})]}),"\n",(0,s.jsx)(n.h2,{id:"token-types",children:"Token Types"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Type"}),(0,s.jsx)(n.th,{children:"Description"}),(0,s.jsx)(n.th,{children:"Use Case"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"API Key"}),(0,s.jsx)(n.td,{children:"Long-lived API key"}),(0,s.jsx)(n.td,{children:"Server-to-server integration"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"JWT"}),(0,s.jsx)(n.td,{children:"Short-lived JWT token"}),(0,s.jsx)(n.td,{children:"User authentication"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"OAuth"}),(0,s.jsx)(n.td,{children:"OAuth 2.0 token"}),(0,s.jsx)(n.td,{children:"Third-party integration"})]})]})]}),"\n",(0,s.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,s.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create token\ntoken = client.auth.create_token(\n    name="production_token",\n    description="Token for production environment",\n    expires_at="2025-03-14T12:00:00Z",\n    permissions=[\n        "read:models",\n        "write:models",\n        "read:data"\n    ]\n)\n\n# List tokens\ntokens = client.auth.list_tokens(\n    page=1,\n    limit=10\n)\n\n# Revoke token\nclient.auth.revoke_token("token_123")\n'})}),"\n",(0,s.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create token\nconst token = await client.auth.createToken({\n  name: 'production_token',\n  description: 'Token for production environment',\n  expiresAt: '2025-03-14T12:00:00Z',\n  permissions: [\n    'read:models',\n    'write:models',\n    'read:data'\n  ]\n});\n\n// List tokens\nconst tokens = await client.auth.listTokens({\n  page: 1,\n  limit: 10\n});\n\n// Revoke token\nawait client.auth.revokeToken('token_123');\n"})}),"\n",(0,s.jsx)(n.h2,{id:"token-best-practices",children:"Token Best Practices"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsx)(n.li,{children:"Use descriptive token names"}),"\n",(0,s.jsx)(n.li,{children:"Set appropriate expiration"}),"\n",(0,s.jsx)(n.li,{children:"Limit token permissions"}),"\n",(0,s.jsx)(n.li,{children:"Rotate tokens regularly"}),"\n",(0,s.jsx)(n.li,{children:"Monitor token usage"}),"\n",(0,s.jsx)(n.li,{children:"Revoke unused tokens"}),"\n",(0,s.jsx)(n.li,{children:"Secure token storage"}),"\n",(0,s.jsx)(n.li,{children:"Implement rate limiting"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(c,{...e})}):c(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>d,x:()=>a});var i=t(6540);const s={},r=i.createContext(s);function d(e){const n=i.useContext(r);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:d(e.components),i.createElement(r.Provider,{value:n},e.children)}}}]);