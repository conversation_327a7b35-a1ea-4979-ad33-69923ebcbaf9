"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2086],{2823:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>d,contentTitle:()=>o,default:()=>m,frontMatter:()=>t,metadata:()=>s,toc:()=>a});const s=JSON.parse('{"id":"ai-architecture/tools/old/api/model-management/model-registry-api","title":"Model Registry API Documentation","description":"The Model Registry API provides endpoints for managing model versions, tracking model lineage, and managing model deployments. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/model-management/model-registry-api.md","sourceDirName":"ai-architecture/tools/old/api/model-management","slug":"/ai-architecture/tools/old/api/model-management/model-registry-api","permalink":"/docs/ai-architecture/tools/old/api/model-management/model-registry-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/model-management/model-registry-api.md","tags":[],"version":"current","frontMatter":{"id":"model-registry-api","title":"Model Registry API Documentation","sidebar_label":"Model Registry API"},"sidebar":"tutorialSidebar","previous":{"title":"Experiment Tracking API","permalink":"/docs/ai-architecture/tools/old/api/model-management/experiment-tracking-api"},"next":{"title":"Model Serving API","permalink":"/docs/ai-architecture/tools/old/api/model-management/model-serving-api"}}');var r=i(4848),l=i(8453);const t={id:"model-registry-api",title:"Model Registry API Documentation",sidebar_label:"Model Registry API"},o="Model Registry API Documentation",d={},a=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Models",id:"models",level:3},{value:"Register Model",id:"register-model",level:4},{value:"List Models",id:"list-models",level:4},{value:"Model Versions",id:"model-versions",level:3},{value:"Create Model Version",id:"create-model-version",level:4},{value:"List Model Versions",id:"list-model-versions",level:4},{value:"Model Deployments",id:"model-deployments",level:3},{value:"Create Deployment",id:"create-deployment",level:4},{value:"Get Deployment Status",id:"get-deployment-status",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,l.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"model-registry-api-documentation",children:"Model Registry API Documentation"})}),"\n",(0,r.jsx)(n.p,{children:"The Model Registry API provides endpoints for managing model versions, tracking model lineage, and managing model deployments. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,r.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"https://model-registry.91.life/api/v1\n"})}),"\n",(0,r.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,r.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,r.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,r.jsx)(n.h3,{id:"models",children:"Models"}),"\n",(0,r.jsx)(n.h4,{id:"register-model",children:"Register Model"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"POST /models\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "name": "customer_churn_predictor",\n    "description": "XGBoost model for customer churn prediction",\n    "type": "classification",\n    "framework": "xgboost",\n    "version": "1.0.0",\n    "artifacts": {\n        "model": "s3://models/churn/xgboost/v1/model.pkl",\n        "config": "s3://models/churn/xgboost/v1/config.json",\n        "requirements": "s3://models/churn/xgboost/v1/requirements.txt"\n    },\n    "metrics": {\n        "accuracy": 0.95,\n        "precision": 0.92,\n        "recall": 0.94,\n        "f1_score": 0.93\n    },\n    "parameters": {\n        "max_depth": 6,\n        "learning_rate": 0.1,\n        "n_estimators": 100\n    },\n    "tags": ["churn", "classification", "customer"],\n    "experiment_id": "exp_001",\n    "run_id": "run_001"\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "model_id": "model_001",\n    "name": "customer_churn_predictor",\n    "version": "1.0.0",\n    "status": "registered",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"list-models",children:"List Models"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /models\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"name"})," (optional): Filter by model name"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"type"})," (optional): Filter by model type"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"framework"})," (optional): Filter by framework"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"tags"})," (optional): Filter by tags"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"status"})," (optional): Filter by status"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "models": [\n        {\n            "model_id": "model_001",\n            "name": "customer_churn_predictor",\n            "description": "XGBoost model for customer churn prediction",\n            "type": "classification",\n            "framework": "xgboost",\n            "version": "1.0.0",\n            "status": "registered",\n            "created_at": "2024-03-20T10:00:00Z",\n            "last_updated": "2024-03-20T10:30:00Z"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"model-versions",children:"Model Versions"}),"\n",(0,r.jsx)(n.h4,{id:"create-model-version",children:"Create Model Version"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"POST /models/{model_id}/versions\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "version": "1.1.0",\n    "description": "Updated model with new features",\n    "artifacts": {\n        "model": "s3://models/churn/xgboost/v1.1.0/model.pkl",\n        "config": "s3://models/churn/xgboost/v1.1.0/config.json",\n        "requirements": "s3://models/churn/xgboost/v1.1.0/requirements.txt"\n    },\n    "metrics": {\n        "accuracy": 0.96,\n        "precision": 0.93,\n        "recall": 0.95,\n        "f1_score": 0.94\n    },\n    "parameters": {\n        "max_depth": 8,\n        "learning_rate": 0.05,\n        "n_estimators": 200\n    },\n    "experiment_id": "exp_002",\n    "run_id": "run_002"\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "version_id": "version_001",\n    "model_id": "model_001",\n    "version": "1.1.0",\n    "status": "registered",\n    "created_at": "2024-03-20T11:00:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"list-model-versions",children:"List Model Versions"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /models/{model_id}/versions\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"status"})," (optional): Filter by status"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "versions": [\n        {\n            "version_id": "version_001",\n            "model_id": "model_001",\n            "version": "1.1.0",\n            "description": "Updated model with new features",\n            "status": "registered",\n            "created_at": "2024-03-20T11:00:00Z",\n            "metrics": {\n                "accuracy": 0.96,\n                "precision": 0.93,\n                "recall": 0.95,\n                "f1_score": 0.94\n            }\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"model-deployments",children:"Model Deployments"}),"\n",(0,r.jsx)(n.h4,{id:"create-deployment",children:"Create Deployment"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"POST /models/{model_id}/versions/{version_id}/deployments\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "name": "production",\n    "environment": "prod",\n    "config": {\n        "replicas": 3,\n        "resources": {\n            "cpu": "2",\n            "memory": "4Gi"\n        },\n        "scaling": {\n            "min_replicas": 2,\n            "max_replicas": 10,\n            "target_cpu_utilization": 80\n        }\n    },\n    "endpoint": {\n        "type": "rest",\n        "path": "/v1/predict"\n    }\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "deployment_id": "deployment_001",\n    "model_id": "model_001",\n    "version_id": "version_001",\n    "name": "production",\n    "status": "deploying",\n    "created_at": "2024-03-20T12:00:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"get-deployment-status",children:"Get Deployment Status"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /models/{model_id}/versions/{version_id}/deployments/{deployment_id}\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "deployment_id": "deployment_001",\n    "model_id": "model_001",\n    "version_id": "version_001",\n    "name": "production",\n    "status": "running",\n    "endpoint": "https://api.91.life/v1/predict",\n    "metrics": {\n        "replicas": 3,\n        "cpu_usage": 45.5,\n        "memory_usage": 60.2,\n        "request_rate": 100,\n        "latency_ms": 150\n    },\n    "created_at": "2024-03-20T12:00:00Z",\n    "last_updated": "2024-03-20T12:05:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,r.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'from model_registry import Client\n\n# Initialize client\nclient = Client(\n    host="https://model-registry.91.life",\n    auth_token="your-token"\n)\n\n# Register model\nmodel = {\n    "name": "customer_churn_predictor",\n    "description": "XGBoost model for customer churn prediction",\n    "type": "classification",\n    "framework": "xgboost",\n    "version": "1.0.0",\n    "artifacts": {\n        "model": "s3://models/churn/xgboost/v1/model.pkl"\n    }\n}\nmodel_id = client.register_model(model)\n\n# Create deployment\ndeployment = {\n    "name": "production",\n    "environment": "prod",\n    "config": {\n        "replicas": 3,\n        "resources": {\n            "cpu": "2",\n            "memory": "4Gi"\n        }\n    }\n}\ndeployment_id = client.create_deployment(\n    model_id=model_id,\n    version_id="version_001",\n    deployment=deployment\n)\n'})}),"\n",(0,r.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:'# Register model\ncurl -X POST https://model-registry.91.life/api/v1/models \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "customer_churn_predictor",\n    "description": "XGBoost model for customer churn prediction",\n    "type": "classification",\n    "framework": "xgboost",\n    "version": "1.0.0",\n    "artifacts": {\n      "model": "s3://models/churn/xgboost/v1/model.pkl"\n    }\n  }\'\n\n# Create deployment\ncurl -X POST https://model-registry.91.life/api/v1/models/model_001/versions/version_001/deployments \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "production",\n    "environment": "prod",\n    "config": {\n      "replicas": 3,\n      "resources": {\n        "cpu": "2",\n        "memory": "4Gi"\n      }\n    }\n  }\'\n'})}),"\n",(0,r.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,r.jsxs)(n.table,{children:[(0,r.jsx)(n.thead,{children:(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.th,{children:"Code"}),(0,r.jsx)(n.th,{children:"Description"})]})}),(0,r.jsxs)(n.tbody,{children:[(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"400"}),(0,r.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"401"}),(0,r.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"403"}),(0,r.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"404"}),(0,r.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"409"}),(0,r.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"429"}),(0,r.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"500"}),(0,r.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,r.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,r.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,r.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Model Registration"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Use semantic versioning"}),"\n",(0,r.jsx)(n.li,{children:"Include comprehensive metadata"}),"\n",(0,r.jsx)(n.li,{children:"Document model changes"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Version Management"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Track model lineage"}),"\n",(0,r.jsx)(n.li,{children:"Maintain version history"}),"\n",(0,r.jsx)(n.li,{children:"Document version differences"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Deployment Management"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Use appropriate resources"}),"\n",(0,r.jsx)(n.li,{children:"Configure scaling policies"}),"\n",(0,r.jsx)(n.li,{children:"Monitor deployment health"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Security"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Secure model artifacts"}),"\n",(0,r.jsx)(n.li,{children:"Control access to models"}),"\n",(0,r.jsx)(n.li,{children:"Audit model usage"}),"\n"]}),"\n"]}),"\n"]})]})}function m(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(c,{...e})}):c(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>t,x:()=>o});var s=i(6540);const r={},l=s.createContext(r);function t(e){const n=s.useContext(l);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function o(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:t(e.components),s.createElement(l.Provider,{value:n},e.children)}}}]);