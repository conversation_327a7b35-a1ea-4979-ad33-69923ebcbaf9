"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3302],{2006:(e,i,n)=>{n.r(i),n.d(i,{assets:()=>l,contentTitle:()=>r,default:()=>h,frontMatter:()=>o,metadata:()=>t,toc:()=>c});const t=JSON.parse('{"id":"adrs/global/opa","title":"5. OPA as Policy Evaluation Engine","description":"Date: 2025-03-17","source":"@site/docs/adrs/global/0005-opa.md","sourceDirName":"adrs/global","slug":"/adrs/global/opa","permalink":"/docs/adrs/global/opa","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/global/0005-opa.md","tags":[],"version":"current","sidebarPosition":5,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"4. ODRL as Policy and Entitlement Definition Language","permalink":"/docs/adrs/global/odrl"},"next":{"title":"6. Keycloak as Authentication and User Management Solution","permalink":"/docs/adrs/global/keycloak"}}');var a=n(4848),s=n(8453);const o={},r="5. OPA as Policy Evaluation Engine",l={},c=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2}];function d(e){const i={h1:"h1",h2:"h2",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,s.R)(),...e.components};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.header,{children:(0,a.jsx)(i.h1,{id:"5-opa-as-policy-evaluation-engine",children:"5. OPA as Policy Evaluation Engine"})}),"\n",(0,a.jsx)(i.p,{children:"Date: 2025-03-17"}),"\n",(0,a.jsx)(i.h2,{id:"status",children:"Status"}),"\n",(0,a.jsx)(i.p,{children:"Proposed"}),"\n",(0,a.jsx)(i.h2,{id:"context",children:"Context"}),"\n",(0,a.jsx)(i.p,{children:"Efficient and low-latency authorization is critical to ensuring secure access control while meeting strict compliance and regulatory requirements. As our platform scales and adapts to evolving access control needs, it is essential to select a policy engine that offers both performance and flexibility."}),"\n",(0,a.jsx)(i.p,{children:"We evaluated several policy engines and decision frameworks to determine the best fit for our environment:"}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Balana + XACML:"})," Although this combination is mature and feature-rich, its reliance on standalone services and complex processing pipelines introduces higher latency, which could negatively impact system responsiveness. Additionally, XACML policies are notoriously complex and not human-readable, which complicates policy management and increases the likelihood of misconfigurations."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Keycloak's Decision Engine:"})," Keycloak provides built-in policies for authentication and authorization. However, using its decision engine would create an unnecessary dependency, potentially limiting our flexibility and hindering our ability to implement granular, tailored policies required by our healthcare clients."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"OPA (Open Policy Agent):"})," OPA offers robust, decoupled policy evaluation capabilities that integrate seamlessly with external data sources. Its support for data loading makes it a natural fit for working with ODRL policies. Moreover, the availability of OPA SDKs allows us to build libraries that can be embedded directly within our applications or integrated into Dapr middleware. This approach minimizes latency by eliminating the need for remote service calls during policy evaluation."]}),"\n"]}),"\n",(0,a.jsx)(i.p,{children:"Given these factors, OPA not only meets our performance and flexibility criteria but also aligns with our long-term strategic goals of maintaining an independent, scalable authorization framework."}),"\n",(0,a.jsx)(i.h2,{id:"decision",children:"Decision"}),"\n",(0,a.jsxs)(i.p,{children:["We propose to adopt ",(0,a.jsx)(i.strong,{children:"OPA"})," as our policy engine, leveraging its SDKs to build libraries that can be embedded directly within our applications or integrated with Dapr middleware. This approach offers several advantages:"]}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Reduced Latency:"})," By embedding OPA via SDKs within the app or middleware, we eliminate the overhead associated with calling out to a separate service."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Seamless Integration with ODRL:"})," OPA\u2019s ability to load and work with external data allows for a smooth integration with our chosen ODRL policy standard."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Flexibility and Modularity:"})," Using OPA SDKs allows us to tailor policy evaluation logic to our specific needs without being tied to a centralized decision engine."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Avoiding Vendor Dependencies:"})," This approach mitigates the risks of dependency on external policy services such as those offered by Keycloak or standalone OPA deployments."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Enhanced Developer Experience:"})," Embedding policy evaluation directly within the application or middleware streamlines development and maintenance, providing a more cohesive system architecture."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Dapr OPA Middleware Updates:"})," We will update the Dapr OPA middleware to support data loading for ODRL policies, ensuring that policy evaluation has access to all necessary external data for comprehensive authorization decisions."]}),"\n"]}),"\n",(0,a.jsx)(i.h2,{id:"consequences",children:"Consequences"}),"\n",(0,a.jsx)(i.p,{children:"Adopting OPA as our integrated policy engine through its SDKs brings several benefits along with some considerations:"}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsxs)(i.li,{children:["\n",(0,a.jsx)(i.p,{children:(0,a.jsx)(i.strong,{children:"Benefits:"})}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Performance Improvement:"})," Direct in-app or middleware policy evaluation reduces network overhead and latency."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Tighter Integration with ODRL:"})," Enhanced support for ODRL policies through data loading capabilities ensures consistency across our authorization framework."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Modularity and Flexibility:"})," Allows for dynamic policy updates and more granular control over the evaluation process."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Simplified Architecture:"})," Reduces the need to manage and scale a separate policy evaluation service."]}),"\n"]}),"\n"]}),"\n",(0,a.jsxs)(i.li,{children:["\n",(0,a.jsx)(i.p,{children:(0,a.jsx)(i.strong,{children:"Trade-offs and Risks:"})}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Development Overhead:"})," Building and maintaining custom libraries using OPA SDKs requires initial development effort and ongoing maintenance."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Complexity in Integration:"})," Embedding policy evaluation within various parts of the system (applications or Dapr middleware) may increase the complexity of integration and testing."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Dependency on OPA SDK Updates:"})," We must monitor and adapt to changes in the OPA SDKs to ensure continuous compatibility and performance."]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.strong,{children:"Middleware Update Requirements:"})," Updating the Dapr OPA middleware to support data loading for ODRL policies introduces additional work and testing to ensure smooth integration and operation."]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,a.jsx)(i.p,{children:"By choosing to integrate OPA directly through its SDKs and updating our Dapr OPA middleware, we aim to achieve a balance between performance, flexibility, and robust policy evaluation, ensuring that our Healthcare platform remains secure and responsive to evolving authorization requirements."})]})}function h(e={}){const{wrapper:i}={...(0,s.R)(),...e.components};return i?(0,a.jsx)(i,{...e,children:(0,a.jsx)(d,{...e})}):d(e)}},8453:(e,i,n)=>{n.d(i,{R:()=>o,x:()=>r});var t=n(6540);const a={},s=t.createContext(a);function o(e){const i=t.useContext(s);return t.useMemo((function(){return"function"==typeof e?e(i):{...i,...e}}),[i,e])}function r(e){let i;return i=e.disableParentContext?"function"==typeof e.components?e.components(a):e.components||a:o(e.components),t.createElement(s.Provider,{value:i},e.children)}}}]);