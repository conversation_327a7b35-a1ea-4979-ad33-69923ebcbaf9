"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8130],{7735:e=>{e.exports=JSON.parse('{"archive":{"blogPosts":[{"id":"welcome","metadata":{"permalink":"/blog/welcome","source":"@site/blog/2024-03-21-welcome.md","title":"Welcome to the 91.life AI Platform Documentation","description":"Welcome to the official documentation for the 91.life AI Platform! This documentation site will serve as your comprehensive guide to understanding, implementing, and maintaining our enterprise AI platform.","date":"2024-03-21T00:00:00.000Z","tags":[{"inline":true,"label":"welcome","permalink":"/blog/tags/welcome"},{"inline":true,"label":"documentation","permalink":"/blog/tags/documentation"}],"readingTime":0.52,"hasTruncateMarker":false,"authors":[{"name":"hortense, jean, albin, alban","key":null,"page":null}],"frontMatter":{"slug":"welcome","title":"Welcome to the 91.life AI Platform Documentation","author":"hortense, jean, albin, alban","tags":["welcome","documentation"]},"unlisted":false},"content":"Welcome to the official documentation for the 91.life AI Platform! This documentation site will serve as your comprehensive guide to understanding, implementing, and maintaining our enterprise AI platform.\\n\\n## What\'s New\\n\\n- Complete documentation structure\\n- API reference\\n- Implementation guides\\n- Best practices\\n- Security guidelines\\n\\n## Getting Started\\n\\nTo get started with the documentation:\\n\\n1. Check out the [Getting Started](/docs/intro) guide\\n2. Review the [Architecture Overview](/docs/architecture-overview)\\n3. Explore the [Implementation](/docs/implementation) section\\n\\n## Stay Updated\\n\\nWe\'ll be regularly updating this documentation with:\\n\\n- New features and capabilities\\n- Best practices and guidelines\\n- Security updates\\n- API changes\\n\\nStay tuned for more updates!"}]}}')}}]);