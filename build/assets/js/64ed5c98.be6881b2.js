"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2101],{3218:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>l,contentTitle:()=>a,default:()=>u,frontMatter:()=>r,metadata:()=>i,toc:()=>d});const i=JSON.parse('{"id":"adrs/platform/vite","title":"7. Vite as the Build Tool for React applications","description":"Date: 2025-03-10","source":"@site/docs/adrs/platform/0007-vite.md","sourceDirName":"adrs/platform","slug":"/adrs/platform/vite","permalink":"/docs/adrs/platform/vite","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/platform/0007-vite.md","tags":[],"version":"current","sidebarPosition":7,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"6. Nx as Monorepo Manager and Build Tool","permalink":"/docs/adrs/platform/nx"},"next":{"title":"4. Go Libraries and Components","permalink":"/docs/adrs/platform/go-libraries"}}');var s=n(4848),o=n(8453);const r={},a="7. Vite as the Build Tool for React applications",l={},d=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2},{value:"Benefits",id:"benefits",level:3},{value:"Risks &amp; Mitigation",id:"risks--mitigation",level:3}];function c(e){const t={h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,o.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.header,{children:(0,s.jsx)(t.h1,{id:"7-vite-as-the-build-tool-for-react-applications",children:"7. Vite as the Build Tool for React applications"})}),"\n",(0,s.jsx)(t.p,{children:"Date: 2025-03-10"}),"\n",(0,s.jsx)(t.h2,{id:"status",children:"Status"}),"\n",(0,s.jsx)(t.p,{children:"Proposed"}),"\n",(0,s.jsx)(t.h2,{id:"context",children:"Context"}),"\n",(0,s.jsx)(t.p,{children:"As we adopt a Microfrontend (MFE) architecture, we need a fast and efficient build tool to support independent\nmodule development and integration. While Next.js provides a powerful framework, it lacks robust support for\nMicro-frontends and Module Federation. To enable faster development cycles, improve build performance, and ensure a\nsmooth integration process for MFEs, we need a tool that is optimized for this architecture."}),"\n",(0,s.jsx)(t.h2,{id:"decision",children:"Decision"}),"\n",(0,s.jsxs)(t.p,{children:["We will ",(0,s.jsx)(t.strong,{children:"use Vite as the build tool for React applications"}),"."]}),"\n",(0,s.jsx)(t.h2,{id:"consequences",children:"Consequences"}),"\n",(0,s.jsx)(t.h3,{id:"benefits",children:"Benefits"}),"\n",(0,s.jsxs)(t.ul,{children:["\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:"Fast Development Cycles"}),": Vite\u2019s optimized build times and HMR result in quicker iteration and testing for MFEs."]}),"\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:"Great MFE Integration"}),": Native support for Module Federation ensures seamless integration of MFEs."]}),"\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:"Improved Developer Experienc"}),"e: Vite\u2019s modern tooling provides an enhanced development experience with\nfaster feedback loops."]}),"\n"]}),"\n",(0,s.jsx)(t.h3,{id:"risks--mitigation",children:"Risks & Mitigation"}),"\n",(0,s.jsxs)(t.ul,{children:["\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:"Plugin Compatibility"}),": Vite\u2019s ecosystem is not as mature as Webpack\u2019s."]}),"\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:"Integration with Nx"}),": Module Federation with Vite does not have an official plugin to integrate it with Nx."]}),"\n"]})]})}function u(e={}){const{wrapper:t}={...(0,o.R)(),...e.components};return t?(0,s.jsx)(t,{...e,children:(0,s.jsx)(c,{...e})}):c(e)}},8453:(e,t,n)=>{n.d(t,{R:()=>r,x:()=>a});var i=n(6540);const s={},o=i.createContext(s);function r(e){const t=i.useContext(o);return i.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function a(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:r(e.components),i.createElement(o.Provider,{value:t},e.children)}}}]);