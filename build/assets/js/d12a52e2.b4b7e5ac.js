"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[4225],{75:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>a,default:()=>m,frontMatter:()=>l,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/tools/old/api/orchestration/mlflow-api","title":"MLflow API Documentation","description":"MLflow provides a RESTful API for experiment tracking, model registry, and model serving. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/orchestration/mlflow-api.md","sourceDirName":"ai-architecture/tools/old/api/orchestration","slug":"/ai-architecture/tools/old/api/orchestration/mlflow-api","permalink":"/docs/ai-architecture/tools/old/api/orchestration/mlflow-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/orchestration/mlflow-api.md","tags":[],"version":"current","frontMatter":{"id":"mlflow-api","title":"MLflow API Documentation","sidebar_label":"MLflow API"},"sidebar":"tutorialSidebar","previous":{"title":"Kubeflow API","permalink":"/docs/ai-architecture/tools/old/api/orchestration/kubeflow-api"},"next":{"title":"Visualization","permalink":"/docs/ai-architecture/tools/old/api/visualization/"}}');var t=i(4848),s=i(8453);const l={id:"mlflow-api",title:"MLflow API Documentation",sidebar_label:"MLflow API"},a="MLflow API Documentation",o={},c=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Experiment Management",id:"experiment-management",level:3},{value:"Create Experiment",id:"create-experiment",level:4},{value:"List Experiments",id:"list-experiments",level:4},{value:"Run Management",id:"run-management",level:3},{value:"Create Run",id:"create-run",level:4},{value:"Log Metric",id:"log-metric",level:4},{value:"Log Parameter",id:"log-parameter",level:4},{value:"Model Registry",id:"model-registry",level:3},{value:"Register Model",id:"register-model",level:4},{value:"Create Model Version",id:"create-model-version",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function d(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,s.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"mlflow-api-documentation",children:"MLflow API Documentation"})}),"\n",(0,t.jsx)(n.p,{children:"MLflow provides a RESTful API for experiment tracking, model registry, and model serving. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,t.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"https://mlflow.91.life/api/v1\n"})}),"\n",(0,t.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,t.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,t.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,t.jsx)(n.h3,{id:"experiment-management",children:"Experiment Management"}),"\n",(0,t.jsx)(n.h4,{id:"create-experiment",children:"Create Experiment"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /experiments\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "name": "my-experiment",\n    "artifact_location": "s3://my-bucket/experiments/my-experiment",\n    "tags": {\n        "project": "ml-pipeline",\n        "team": "data-science"\n    }\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "experiment_id": "123",\n    "name": "my-experiment",\n    "artifact_location": "s3://my-bucket/experiments/my-experiment",\n    "lifecycle_stage": "active",\n    "tags": {\n        "project": "ml-pipeline",\n        "team": "data-science"\n    }\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"list-experiments",children:"List Experiments"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"GET /experiments\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"view_type"}),' (optional): "ACTIVE_ONLY", "DELETED_ONLY", or "ALL"']}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"max_results"})," (optional): Maximum number of experiments to return"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "experiments": [\n        {\n            "experiment_id": "123",\n            "name": "my-experiment",\n            "artifact_location": "s3://my-bucket/experiments/my-experiment",\n            "lifecycle_stage": "active",\n            "tags": {\n                "project": "ml-pipeline",\n                "team": "data-science"\n            }\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"run-management",children:"Run Management"}),"\n",(0,t.jsx)(n.h4,{id:"create-run",children:"Create Run"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /runs\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "experiment_id": "123",\n    "start_time": "2024-03-20T10:00:00Z",\n    "tags": {\n        "mlflow.user": "<EMAIL>",\n        "mlflow.source.name": "train.py"\n    }\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "run": {\n        "info": {\n            "run_id": "abc123...",\n            "experiment_id": "123",\n            "status": "RUNNING",\n            "start_time": "2024-03-20T10:00:00Z",\n            "end_time": null,\n            "lifecycle_stage": "active"\n        },\n        "data": {\n            "metrics": {},\n            "params": {},\n            "tags": {\n                "mlflow.user": "<EMAIL>",\n                "mlflow.source.name": "train.py"\n            }\n        }\n    }\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"log-metric",children:"Log Metric"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /runs/{run_id}/metrics\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "key": "accuracy",\n    "value": 0.95,\n    "timestamp": "2024-03-20T10:00:00Z",\n    "step": 1\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"log-parameter",children:"Log Parameter"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /runs/{run_id}/params\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "key": "learning_rate",\n    "value": "0.001"\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"model-registry",children:"Model Registry"}),"\n",(0,t.jsx)(n.h4,{id:"register-model",children:"Register Model"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /registered-models\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "name": "my-model",\n    "description": "My ML model",\n    "tags": {\n        "framework": "pytorch",\n        "version": "1.0"\n    }\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "registered_model": {\n        "name": "my-model",\n        "creation_timestamp": "2024-03-20T10:00:00Z",\n        "last_updated_timestamp": "2024-03-20T10:00:00Z",\n        "description": "My ML model",\n        "latest_versions": [],\n        "tags": {\n            "framework": "pytorch",\n            "version": "1.0"\n        }\n    }\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"create-model-version",children:"Create Model Version"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /model-versions\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "name": "my-model",\n    "source": "s3://my-bucket/models/model.pkl",\n    "run_id": "abc123...",\n    "tags": {\n        "stage": "production"\n    }\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "model_version": {\n        "name": "my-model",\n        "version": "1",\n        "creation_timestamp": "2024-03-20T10:00:00Z",\n        "last_updated_timestamp": "2024-03-20T10:00:00Z",\n        "current_stage": "None",\n        "description": "",\n        "source": "s3://my-bucket/models/model.pkl",\n        "run_id": "abc123...",\n        "status": "READY",\n        "tags": {\n            "stage": "production"\n        }\n    }\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,t.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'import mlflow\nimport mlflow.sklearn\nfrom sklearn.ensemble import RandomForestClassifier\n\n# Set tracking URI\nmlflow.set_tracking_uri("https://mlflow.91.life")\n\n# Start run\nwith mlflow.start_run(experiment_id="123") as run:\n    # Train model\n    model = RandomForestClassifier()\n    model.fit(X_train, y_train)\n    \n    # Log parameters\n    mlflow.log_param("n_estimators", 100)\n    mlflow.log_param("max_depth", 10)\n    \n    # Log metrics\n    mlflow.log_metric("accuracy", 0.95)\n    mlflow.log_metric("f1_score", 0.92)\n    \n    # Log model\n    mlflow.sklearn.log_model(model, "model")\n    \n    # Register model\n    mlflow.register_model(\n        f"runs:/{run.info.run_id}/model",\n        "my-model"\n    )\n'})}),"\n",(0,t.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:'# Create experiment\ncurl -X POST https://mlflow.91.life/api/v1/experiments \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "my-experiment",\n    "artifact_location": "s3://my-bucket/experiments/my-experiment"\n  }\'\n\n# Log metric\ncurl -X POST https://mlflow.91.life/api/v1/runs/abc123/metrics \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "key": "accuracy",\n    "value": 0.95,\n    "timestamp": "2024-03-20T10:00:00Z"\n  }\'\n'})}),"\n",(0,t.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Code"}),(0,t.jsx)(n.th,{children:"Description"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"400"}),(0,t.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"401"}),(0,t.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"403"}),(0,t.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"404"}),(0,t.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"409"}),(0,t.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"429"}),(0,t.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"500"}),(0,t.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,t.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,t.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Experiment Organization"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Use meaningful experiment names"}),"\n",(0,t.jsx)(n.li,{children:"Tag experiments with relevant metadata"}),"\n",(0,t.jsx)(n.li,{children:"Organize experiments by project/team"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Run Management"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Log all relevant parameters and metrics"}),"\n",(0,t.jsx)(n.li,{children:"Use consistent naming conventions"}),"\n",(0,t.jsx)(n.li,{children:"Include run descriptions"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Model Registry"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Version models appropriately"}),"\n",(0,t.jsx)(n.li,{children:"Document model changes"}),"\n",(0,t.jsx)(n.li,{children:"Use stage transitions (Staging, Production)"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Performance"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Batch metric logging when possible"}),"\n",(0,t.jsx)(n.li,{children:"Use appropriate artifact storage"}),"\n",(0,t.jsx)(n.li,{children:"Implement proper error handling"}),"\n"]}),"\n"]}),"\n"]})]})}function m(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(d,{...e})}):d(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>a});var r=i(6540);const t={},s=r.createContext(t);function l(e){const n=r.useContext(s);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:l(e.components),r.createElement(s.Provider,{value:n},e.children)}}}]);