"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[1441],{2426:(e,n,r)=>{r.r(n),r.d(n,{assets:()=>l,contentTitle:()=>d,default:()=>h,frontMatter:()=>s,metadata:()=>t,toc:()=>o});const t=JSON.parse('{"id":"ai-architecture/api/monitoring/dashboards","title":"Monitoring Dashboards","description":"Create and manage custom monitoring dashboards.","source":"@site/docs/ai-architecture/api/monitoring/dashboards.md","sourceDirName":"ai-architecture/api/monitoring","slug":"/ai-architecture/api/monitoring/dashboards","permalink":"/docs/ai-architecture/api/monitoring/dashboards","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/monitoring/dashboards.md","tags":[],"version":"current","sidebarPosition":4,"frontMatter":{"sidebar_position":4},"sidebar":"tutorialSidebar","previous":{"title":"Monitoring Metrics","permalink":"/docs/ai-architecture/api/monitoring/metrics"},"next":{"title":"Monitoring Reports","permalink":"/docs/ai-architecture/api/monitoring/reports"}}');var a=r(4848),i=r(8453);const s={sidebar_position:4},d="Monitoring Dashboards",l={},o=[{value:"Create Dashboard",id:"create-dashboard",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Dashboard",id:"get-dashboard",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"List Dashboards",id:"list-dashboards",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Panel Types",id:"panel-types",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Dashboard Best Practices",id:"dashboard-best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,i.R)(),...e.components};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.header,{children:(0,a.jsx)(n.h1,{id:"monitoring-dashboards",children:"Monitoring Dashboards"})}),"\n",(0,a.jsx)(n.p,{children:"Create and manage custom monitoring dashboards."}),"\n",(0,a.jsx)(n.h2,{id:"create-dashboard",children:"Create Dashboard"}),"\n",(0,a.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"POST /v1/monitoring/dashboards\n"})}),"\n",(0,a.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "name": "Model Performance Dashboard",\n  "description": "Overview of model performance metrics",\n  "layout": "grid",\n  "panels": [\n    {\n      "title": "Model Latency",\n      "type": "line",\n      "metrics": ["model_latency"],\n      "position": {\n        "x": 0,\n        "y": 0,\n        "width": 6,\n        "height": 4\n      },\n      "options": {\n        "interval": "5m",\n        "aggregation": "avg"\n      }\n    },\n    {\n      "title": "Error Rate",\n      "type": "gauge",\n      "metrics": ["model_errors"],\n      "position": {\n        "x": 6,\n        "y": 0,\n        "width": 6,\n        "height": 4\n      },\n      "options": {\n        "thresholds": {\n          "warning": 5,\n          "critical": 10\n        }\n      }\n    }\n  ],\n  "refresh_interval": "1m",\n  "tags": ["models", "performance"]\n}\n'})}),"\n",(0,a.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "dashboard": {\n      "id": "dashboard_123",\n      "name": "Model Performance Dashboard",\n      "description": "Overview of model performance metrics",\n      "layout": "grid",\n      "panels": [\n        {\n          "id": "panel_123",\n          "title": "Model Latency",\n          "type": "line",\n          "metrics": ["model_latency"],\n          "position": {\n            "x": 0,\n            "y": 0,\n            "width": 6,\n            "height": 4\n          }\n        },\n        {\n          "id": "panel_124",\n          "title": "Error Rate",\n          "type": "gauge",\n          "metrics": ["model_errors"],\n          "position": {\n            "x": 6,\n            "y": 0,\n            "width": 6,\n            "height": 4\n          }\n        }\n      ],\n      "refresh_interval": "1m",\n      "tags": ["models", "performance"],\n      "created_at": "2024-03-14T12:00:00Z",\n      "updated_at": "2024-03-14T12:00:00Z"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"get-dashboard",children:"Get Dashboard"}),"\n",(0,a.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"GET /v1/monitoring/dashboards/{dashboard_id}\n"})}),"\n",(0,a.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "dashboard": {\n      "id": "dashboard_123",\n      "name": "Model Performance Dashboard",\n      "description": "Overview of model performance metrics",\n      "layout": "grid",\n      "panels": [\n        {\n          "id": "panel_123",\n          "title": "Model Latency",\n          "type": "line",\n          "metrics": ["model_latency"],\n          "position": {\n            "x": 0,\n            "y": 0,\n            "width": 6,\n            "height": 4\n          },\n          "data": {\n            "values": [\n              {\n                "timestamp": "2024-03-14T12:00:00Z",\n                "value": 150.5\n              }\n            ]\n          }\n        }\n      ],\n      "refresh_interval": "1m",\n      "tags": ["models", "performance"],\n      "created_at": "2024-03-14T12:00:00Z",\n      "updated_at": "2024-03-14T12:00:00Z"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"list-dashboards",children:"List Dashboards"}),"\n",(0,a.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"GET /v1/monitoring/dashboards\n"})}),"\n",(0,a.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,a.jsxs)(n.table,{children:[(0,a.jsx)(n.thead,{children:(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.th,{children:"Parameter"}),(0,a.jsx)(n.th,{children:"Type"}),(0,a.jsx)(n.th,{children:"Description"})]})}),(0,a.jsxs)(n.tbody,{children:[(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"page"}),(0,a.jsx)(n.td,{children:"integer"}),(0,a.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"limit"}),(0,a.jsx)(n.td,{children:"integer"}),(0,a.jsx)(n.td,{children:"Items per page (default: 100)"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"tags"}),(0,a.jsx)(n.td,{children:"string"}),(0,a.jsx)(n.td,{children:"Filter by tags"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"search"}),(0,a.jsx)(n.td,{children:"string"}),(0,a.jsx)(n.td,{children:"Search query"})]})]})]}),"\n",(0,a.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "dashboards": [\n      {\n        "id": "dashboard_123",\n        "name": "Model Performance Dashboard",\n        "description": "Overview of model performance metrics",\n        "tags": ["models", "performance"],\n        "created_at": "2024-03-14T12:00:00Z",\n        "updated_at": "2024-03-14T12:00:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 100,\n      "page": 1,\n      "limit": 100,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"panel-types",children:"Panel Types"}),"\n",(0,a.jsxs)(n.table,{children:[(0,a.jsx)(n.thead,{children:(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.th,{children:"Type"}),(0,a.jsx)(n.th,{children:"Description"}),(0,a.jsx)(n.th,{children:"Use Case"})]})}),(0,a.jsxs)(n.tbody,{children:[(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"line"}),(0,a.jsx)(n.td,{children:"Line chart"}),(0,a.jsx)(n.td,{children:"Time series data"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"bar"}),(0,a.jsx)(n.td,{children:"Bar chart"}),(0,a.jsx)(n.td,{children:"Categorical data"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"gauge"}),(0,a.jsx)(n.td,{children:"Gauge chart"}),(0,a.jsx)(n.td,{children:"Current value with thresholds"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"pie"}),(0,a.jsx)(n.td,{children:"Pie chart"}),(0,a.jsx)(n.td,{children:"Distribution data"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"table"}),(0,a.jsx)(n.td,{children:"Data table"}),(0,a.jsx)(n.td,{children:"Detailed metrics"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"stat"}),(0,a.jsx)(n.td,{children:"Statistic"}),(0,a.jsx)(n.td,{children:"Single value"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"heatmap"}),(0,a.jsx)(n.td,{children:"Heat map"}),(0,a.jsx)(n.td,{children:"Correlation data"})]})]})]}),"\n",(0,a.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,a.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create dashboard\ndashboard = client.monitoring.create_dashboard(\n    name="Model Performance Dashboard",\n    description="Overview of model performance metrics",\n    panels=[\n        {\n            "title": "Model Latency",\n            "type": "line",\n            "metrics": ["model_latency"],\n            "position": {"x": 0, "y": 0, "width": 6, "height": 4}\n        }\n    ]\n)\n\n# Get dashboard\ndashboard_details = client.monitoring.get_dashboard("dashboard_123")\n\n# List dashboards\ndashboards = client.monitoring.list_dashboards(\n    page=1,\n    limit=100,\n    tags=["models"]\n)\n'})}),"\n",(0,a.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create dashboard\nconst dashboard = await client.monitoring.createDashboard({\n  name: 'Model Performance Dashboard',\n  description: 'Overview of model performance metrics',\n  panels: [\n    {\n      title: 'Model Latency',\n      type: 'line',\n      metrics: ['model_latency'],\n      position: { x: 0, y: 0, width: 6, height: 4 }\n    }\n  ]\n});\n\n// Get dashboard\nconst dashboardDetails = await client.monitoring.getDashboard('dashboard_123');\n\n// List dashboards\nconst dashboards = await client.monitoring.listDashboards({\n  page: 1,\n  limit: 100,\n  tags: ['models']\n});\n"})}),"\n",(0,a.jsx)(n.h2,{id:"dashboard-best-practices",children:"Dashboard Best Practices"}),"\n",(0,a.jsxs)(n.ol,{children:["\n",(0,a.jsx)(n.li,{children:"Organize panels logically"}),"\n",(0,a.jsx)(n.li,{children:"Use appropriate visualizations"}),"\n",(0,a.jsx)(n.li,{children:"Set meaningful thresholds"}),"\n",(0,a.jsx)(n.li,{children:"Include key metrics"}),"\n",(0,a.jsx)(n.li,{children:"Add context and labels"}),"\n",(0,a.jsx)(n.li,{children:"Set refresh intervals"}),"\n",(0,a.jsx)(n.li,{children:"Use tags for organization"}),"\n",(0,a.jsx)(n.li,{children:"Share with team members"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,i.R)(),...e.components};return n?(0,a.jsx)(n,{...e,children:(0,a.jsx)(c,{...e})}):c(e)}},8453:(e,n,r)=>{r.d(n,{R:()=>s,x:()=>d});var t=r(6540);const a={},i=t.createContext(a);function s(e){const n=t.useContext(i);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function d(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(a):e.components||a:s(e.components),t.createElement(i.Provider,{value:n},e.children)}}}]);