"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3073],{8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>a});var r=i(6540);const s={},t=r.createContext(s);function l(e){const n=r.useContext(t);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:l(e.components),r.createElement(t.Provider,{value:n},e.children)}},9469:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>a,default:()=>p,frontMatter:()=>l,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/tools/old/api/orchestration/kubeflow-api","title":"Kubeflow API Documentation","description":"Kubeflow provides a comprehensive API for managing ML pipelines, notebooks, and model serving in Kubernetes. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/orchestration/kubeflow-api.md","sourceDirName":"ai-architecture/tools/old/api/orchestration","slug":"/ai-architecture/tools/old/api/orchestration/kubeflow-api","permalink":"/docs/ai-architecture/tools/old/api/orchestration/kubeflow-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/orchestration/kubeflow-api.md","tags":[],"version":"current","frontMatter":{"id":"kubeflow-api","title":"Kubeflow API Documentation","sidebar_label":"Kubeflow API"},"sidebar":"tutorialSidebar","previous":{"title":"KServe API","permalink":"/docs/ai-architecture/tools/old/api/orchestration/kserve-api"},"next":{"title":"MLflow API","permalink":"/docs/ai-architecture/tools/old/api/orchestration/mlflow-api"}}');var s=i(4848),t=i(8453);const l={id:"kubeflow-api",title:"Kubeflow API Documentation",sidebar_label:"Kubeflow API"},a="Kubeflow API Documentation",o={},c=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Pipeline Management",id:"pipeline-management",level:3},{value:"Create Pipeline",id:"create-pipeline",level:4},{value:"List Pipelines",id:"list-pipelines",level:4},{value:"Pipeline Run Management",id:"pipeline-run-management",level:3},{value:"Create Pipeline Run",id:"create-pipeline-run",level:4},{value:"Get Pipeline Run Status",id:"get-pipeline-run-status",level:4},{value:"Notebook Management",id:"notebook-management",level:3},{value:"Create Notebook Server",id:"create-notebook-server",level:4},{value:"List Notebook Servers",id:"list-notebook-servers",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function d(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,t.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"kubeflow-api-documentation",children:"Kubeflow API Documentation"})}),"\n",(0,s.jsx)(n.p,{children:"Kubeflow provides a comprehensive API for managing ML pipelines, notebooks, and model serving in Kubernetes. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,s.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"https://kubeflow.91.life/api/v1\n"})}),"\n",(0,s.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,s.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,s.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,s.jsx)(n.h3,{id:"pipeline-management",children:"Pipeline Management"}),"\n",(0,s.jsx)(n.h4,{id:"create-pipeline",children:"Create Pipeline"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /pipelines\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "name": "training-pipeline",\n    "description": "End-to-end model training pipeline",\n    "pipeline_spec": {\n        "pipeline_id": "training-pipeline-v1",\n        "pipeline_manifest": "base64_encoded_pipeline_yaml",\n        "parameters": [\n            {\n                "name": "learning_rate",\n                "value": "0.001"\n            },\n            {\n                "name": "batch_size",\n                "value": "32"\n            }\n        ]\n    }\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "pipeline_id": "training-pipeline-v1",\n    "name": "training-pipeline",\n    "description": "End-to-end model training pipeline",\n    "created_at": "2024-03-20T10:00:00Z",\n    "status": "active"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"list-pipelines",children:"List Pipelines"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /pipelines\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"sort_by"})," (optional): Field to sort by"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"filter"})," (optional): Filter expression"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "pipelines": [\n        {\n            "pipeline_id": "training-pipeline-v1",\n            "name": "training-pipeline",\n            "description": "End-to-end model training pipeline",\n            "created_at": "2024-03-20T10:00:00Z",\n            "status": "active"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"pipeline-run-management",children:"Pipeline Run Management"}),"\n",(0,s.jsx)(n.h4,{id:"create-pipeline-run",children:"Create Pipeline Run"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /pipelines/{pipeline_id}/runs\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "name": "training-run-001",\n    "parameters": {\n        "learning_rate": "0.001",\n        "batch_size": "32",\n        "epochs": "10"\n    },\n    "service_account": "pipeline-runner",\n    "resources": {\n        "cpu": "2",\n        "memory": "4Gi",\n        "gpu": "1"\n    }\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "run_id": "run-001",\n    "pipeline_id": "training-pipeline-v1",\n    "name": "training-run-001",\n    "status": "PENDING",\n    "created_at": "2024-03-20T10:00:00Z",\n    "parameters": {\n        "learning_rate": "0.001",\n        "batch_size": "32",\n        "epochs": "10"\n    }\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"get-pipeline-run-status",children:"Get Pipeline Run Status"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /pipelines/{pipeline_id}/runs/{run_id}\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "run_id": "run-001",\n    "pipeline_id": "training-pipeline-v1",\n    "name": "training-run-001",\n    "status": "RUNNING",\n    "created_at": "2024-03-20T10:00:00Z",\n    "started_at": "2024-03-20T10:01:00Z",\n    "finished_at": null,\n    "parameters": {\n        "learning_rate": "0.001",\n        "batch_size": "32",\n        "epochs": "10"\n    },\n    "metrics": {\n        "accuracy": 0.95,\n        "loss": 0.05\n    }\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"notebook-management",children:"Notebook Management"}),"\n",(0,s.jsx)(n.h4,{id:"create-notebook-server",children:"Create Notebook Server"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /notebooks\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "name": "data-science-notebook",\n    "image": "jupyter/tensorflow-notebook:latest",\n    "resources": {\n        "cpu": "2",\n        "memory": "4Gi",\n        "gpu": "1"\n    },\n    "volumes": [\n        {\n            "name": "data-volume",\n            "mount_path": "/home/<USER>/data",\n            "size": "10Gi"\n        }\n    ],\n    "environment_variables": {\n        "MLFLOW_TRACKING_URI": "https://mlflow.91.life"\n    }\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "notebook_id": "notebook-001",\n    "name": "data-science-notebook",\n    "status": "PENDING",\n    "url": "https://notebook.91.life/notebook-001",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"list-notebook-servers",children:"List Notebook Servers"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /notebooks\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "notebooks": [\n        {\n            "notebook_id": "notebook-001",\n            "name": "data-science-notebook",\n            "status": "RUNNING",\n            "url": "https://notebook.91.life/notebook-001",\n            "created_at": "2024-03-20T10:00:00Z"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,s.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'from kubeflow import Client\nfrom kubeflow.pipeline import Pipeline, PipelineRun\n\n# Initialize client\nclient = Client(host="https://kubeflow.91.life")\n\n# Create pipeline\npipeline = Pipeline(\n    name="training-pipeline",\n    description="End-to-end model training pipeline",\n    pipeline_spec={\n        "pipeline_id": "training-pipeline-v1",\n        "pipeline_manifest": pipeline_yaml,\n        "parameters": [\n            {"name": "learning_rate", "value": "0.001"},\n            {"name": "batch_size", "value": "32"}\n        ]\n    }\n)\npipeline_id = client.create_pipeline(pipeline)\n\n# Create pipeline run\nrun = PipelineRun(\n    name="training-run-001",\n    pipeline_id=pipeline_id,\n    parameters={\n        "learning_rate": "0.001",\n        "batch_size": "32",\n        "epochs": "10"\n    }\n)\nrun_id = client.create_pipeline_run(run)\n\n# Monitor run status\nstatus = client.get_pipeline_run_status(pipeline_id, run_id)\nprint(f"Run status: {status}")\n'})}),"\n",(0,s.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:'# Create pipeline\ncurl -X POST https://kubeflow.91.life/api/v1/pipelines \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "training-pipeline",\n    "description": "End-to-end model training pipeline",\n    "pipeline_spec": {\n      "pipeline_id": "training-pipeline-v1",\n      "pipeline_manifest": "base64_encoded_pipeline_yaml",\n      "parameters": [\n        {"name": "learning_rate", "value": "0.001"},\n        {"name": "batch_size", "value": "32"}\n      ]\n    }\n  }\'\n\n# Create pipeline run\ncurl -X POST https://kubeflow.91.life/api/v1/pipelines/training-pipeline-v1/runs \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "training-run-001",\n    "parameters": {\n      "learning_rate": "0.001",\n      "batch_size": "32",\n      "epochs": "10"\n    }\n  }\'\n'})}),"\n",(0,s.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Code"}),(0,s.jsx)(n.th,{children:"Description"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"400"}),(0,s.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"401"}),(0,s.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"403"}),(0,s.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"404"}),(0,s.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"409"}),(0,s.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"429"}),(0,s.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"500"}),(0,s.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,s.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,s.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,s.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Pipeline Management"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Version your pipeline definitions"}),"\n",(0,s.jsx)(n.li,{children:"Use meaningful names and descriptions"}),"\n",(0,s.jsx)(n.li,{children:"Document pipeline parameters"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Resource Management"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Set appropriate resource limits"}),"\n",(0,s.jsx)(n.li,{children:"Monitor resource usage"}),"\n",(0,s.jsx)(n.li,{children:"Clean up unused resources"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Security"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Use service accounts with minimal permissions"}),"\n",(0,s.jsx)(n.li,{children:"Implement proper access controls"}),"\n",(0,s.jsx)(n.li,{children:"Secure sensitive parameters"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Monitoring"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Monitor pipeline run status"}),"\n",(0,s.jsx)(n.li,{children:"Track resource usage"}),"\n",(0,s.jsx)(n.li,{children:"Set up alerts for failures"}),"\n"]}),"\n"]}),"\n"]})]})}function p(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(d,{...e})}):d(e)}}}]);