"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[9879],{3767:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>a,contentTitle:()=>l,default:()=>h,frontMatter:()=>d,metadata:()=>r,toc:()=>o});const r=JSON.parse('{"id":"ai-architecture/api/models/predictions","title":"Model Predictions","description":"Make predictions using your AI models.","source":"@site/docs/ai-architecture/api/models/predictions.md","sourceDirName":"ai-architecture/api/models","slug":"/ai-architecture/api/models/predictions","permalink":"/docs/ai-architecture/api/models/predictions","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/models/predictions.md","tags":[],"version":"current","sidebarPosition":3,"frontMatter":{"sidebar_position":3},"sidebar":"tutorialSidebar","previous":{"title":"Model Details","permalink":"/docs/ai-architecture/api/models/model-details"},"next":{"title":"Monitoring Alerts","permalink":"/docs/ai-architecture/api/monitoring/alerts"}}');var i=t(4848),s=t(8453);const d={sidebar_position:3},l="Model Predictions",a={},o=[{value:"Endpoint",id:"endpoint",level:2},{value:"Path Parameters",id:"path-parameters",level:2},{value:"Request Body",id:"request-body",level:2},{value:"Example Request",id:"example-request",level:2},{value:"Example Response",id:"example-response",level:2},{value:"Error Codes",id:"error-codes",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Best Practices",id:"best-practices",level:2},{value:"Rate Limits",id:"rate-limits",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,s.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"model-predictions",children:"Model Predictions"})}),"\n",(0,i.jsx)(n.p,{children:"Make predictions using your AI models."}),"\n",(0,i.jsx)(n.h2,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"POST /v1/models/{model_id}/predict\n"})}),"\n",(0,i.jsx)(n.h2,{id:"path-parameters",children:"Path Parameters"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Parameter"}),(0,i.jsx)(n.th,{children:"Type"}),(0,i.jsx)(n.th,{children:"Required"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsx)(n.tbody,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"model_id"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Yes"}),(0,i.jsx)(n.td,{children:"ID of the model"})]})})]}),"\n",(0,i.jsx)(n.h2,{id:"request-body",children:"Request Body"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "input": {\n    "text": "Your input text here",\n    "parameters": {\n      "temperature": 0.7,\n      "max_tokens": 100,\n      "top_p": 1.0\n    }\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"example-request",children:"Example Request"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-bash",children:'curl -X POST \\\n     -H "Authorization: Bearer YOUR_API_KEY" \\\n     -H "Content-Type: application/json" \\\n     -d \'{\n       "input": {\n         "text": "Hello, how are you?",\n         "parameters": {\n           "temperature": 0.7,\n           "max_tokens": 100\n         }\n       }\n     }\' \\\n     "https://api.ai-platform.example.com/v1/models/model_123/predict"\n'})}),"\n",(0,i.jsx)(n.h2,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "prediction": {\n      "id": "pred_123",\n      "model_id": "model_123",\n      "input": {\n        "text": "Hello, how are you?",\n        "parameters": {\n          "temperature": 0.7,\n          "max_tokens": 100\n        }\n      },\n      "output": {\n        "text": "I\'m doing well, thank you for asking! How can I help you today?",\n        "confidence": 0.95\n      },\n      "created_at": "2024-03-14T12:00:00Z",\n      "processing_time": 150\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Code"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"invalid_request"}),(0,i.jsx)(n.td,{children:"Invalid input parameters"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"model_not_found"}),(0,i.jsx)(n.td,{children:"Model not found"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"model_not_ready"}),(0,i.jsx)(n.td,{children:"Model is not ready for use"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"rate_limit_exceeded"}),(0,i.jsx)(n.td,{children:"Too many requests"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Make a prediction\nprediction = client.models.predict(\n    model_id="model_123",\n    input={\n        "text": "Hello, how are you?",\n        "parameters": {\n            "temperature": 0.7,\n            "max_tokens": 100\n        }\n    }\n)\n'})}),"\n",(0,i.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Make a prediction\nconst prediction = await client.models.predict('model_123', {\n  input: {\n    text: 'Hello, how are you?',\n    parameters: {\n      temperature: 0.7,\n      max_tokens: 100\n    }\n  }\n});\n"})}),"\n",(0,i.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"Always validate input data before sending"}),"\n",(0,i.jsx)(n.li,{children:"Use appropriate model parameters"}),"\n",(0,i.jsx)(n.li,{children:"Handle rate limits and errors gracefully"}),"\n",(0,i.jsx)(n.li,{children:"Cache predictions when possible"}),"\n",(0,i.jsx)(n.li,{children:"Monitor model performance"}),"\n"]}),"\n",(0,i.jsx)(n.h2,{id:"rate-limits",children:"Rate Limits"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Plan"}),(0,i.jsx)(n.th,{children:"Requests per minute"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Standard"}),(0,i.jsx)(n.td,{children:"100"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Pro"}),(0,i.jsx)(n.td,{children:"500"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"Enterprise"}),(0,i.jsx)(n.td,{children:"1000"})]})]})]})]})}function h(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(c,{...e})}):c(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>d,x:()=>l});var r=t(6540);const i={},s=r.createContext(i);function d(e){const n=r.useContext(s);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:d(e.components),r.createElement(s.Provider,{value:n},e.children)}}}]);