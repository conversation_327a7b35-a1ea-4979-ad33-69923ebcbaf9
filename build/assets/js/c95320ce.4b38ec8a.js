"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3889],{1071:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>d,contentTitle:()=>a,default:()=>u,frontMatter:()=>l,metadata:()=>t,toc:()=>c});const t=JSON.parse('{"id":"ai-architecture/tools/old/api/data-management/trino-api","title":"Trino API Documentation","description":"Trino provides a RESTful API for executing SQL queries and managing query execution. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/data-management/trino-api.md","sourceDirName":"ai-architecture/tools/old/api/data-management","slug":"/ai-architecture/tools/old/api/data-management/trino-api","permalink":"/docs/ai-architecture/tools/old/api/data-management/trino-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/data-management/trino-api.md","tags":[],"version":"current","frontMatter":{"id":"trino-api","title":"Trino API Documentation","sidebar_label":"Trino API"},"sidebar":"tutorialSidebar","previous":{"title":"OpenMetadata API","permalink":"/docs/ai-architecture/tools/old/api/data-management/openmetadata-api"},"next":{"title":"Model Management","permalink":"/docs/ai-architecture/tools/old/api/model-management/"}}');var r=i(4848),s=i(8453);const l={id:"trino-api",title:"Trino API Documentation",sidebar_label:"Trino API"},a="Trino API Documentation",d={},c=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Query Execution",id:"query-execution",level:3},{value:"Submit Query",id:"submit-query",level:4},{value:"Get Query Results",id:"get-query-results",level:4},{value:"Cancel Query",id:"cancel-query",level:4},{value:"Query Management",id:"query-management",level:3},{value:"List Queries",id:"list-queries",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,s.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"trino-api-documentation",children:"Trino API Documentation"})}),"\n",(0,r.jsx)(n.p,{children:"Trino provides a RESTful API for executing SQL queries and managing query execution. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,r.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"https://trino.example.com/v1\n"})}),"\n",(0,r.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,r.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,r.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,r.jsx)(n.h3,{id:"query-execution",children:"Query Execution"}),"\n",(0,r.jsx)(n.h4,{id:"submit-query",children:"Submit Query"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"POST /statement\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Headers:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-Trino-User"}),": Username"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-Trino-Schema"}),": Default schema"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-Trino-Catalog"}),": Default catalog"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-Trino-Time-Zone"}),': Time zone (e.g., "UTC")']}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-sql",children:"SELECT * FROM my_table WHERE date >= DATE '2024-03-20'\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "id": "20240320_100000_00000_abcde",\n    "infoUri": "https://trino.example.com/ui/query.html?20240320_100000_00000_abcde",\n    "nextUri": "https://trino.example.com/v1/statement/20240320_100000_00000_abcde/1",\n    "stats": {\n        "state": "RUNNING",\n        "queued": false,\n        "scheduled": true,\n        "nodes": 1,\n        "totalSplits": 10,\n        "queuedSplits": 0,\n        "runningSplits": 5,\n        "completedSplits": 5,\n        "cpuTimeMillis": 1000,\n        "wallTimeMillis": 2000,\n        "queuedTimeMillis": 0,\n        "elapsedTimeMillis": 2000,\n        "processedRows": 1000,\n        "processedBytes": 10000,\n        "peakMemoryBytes": 1000000\n    }\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"get-query-results",children:"Get Query Results"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /statement/{query-id}/{token}\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "id": "20240320_100000_00000_abcde",\n    "infoUri": "https://trino.example.com/ui/query.html?20240320_100000_00000_abcde",\n    "nextUri": "https://trino.example.com/v1/statement/20240320_100000_00000_abcde/2",\n    "data": [\n        ["2024-03-20", 100, "value1"],\n        ["2024-03-20", 200, "value2"]\n    ],\n    "columns": [\n        {\n            "name": "date",\n            "type": "date",\n            "typeSignature": {\n                "rawType": "date",\n                "arguments": []\n            }\n        },\n        {\n            "name": "count",\n            "type": "bigint",\n            "typeSignature": {\n                "rawType": "bigint",\n                "arguments": []\n            }\n        },\n        {\n            "name": "value",\n            "type": "varchar",\n            "typeSignature": {\n                "rawType": "varchar",\n                "arguments": []\n            }\n        }\n    ],\n    "stats": {\n        "state": "RUNNING",\n        "queued": false,\n        "scheduled": true,\n        "nodes": 1,\n        "totalSplits": 10,\n        "queuedSplits": 0,\n        "runningSplits": 5,\n        "completedSplits": 5,\n        "cpuTimeMillis": 1000,\n        "wallTimeMillis": 2000,\n        "queuedTimeMillis": 0,\n        "elapsedTimeMillis": 2000,\n        "processedRows": 1000,\n        "processedBytes": 10000,\n        "peakMemoryBytes": 1000000\n    }\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"cancel-query",children:"Cancel Query"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"DELETE /statement/{query-id}\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "id": "20240320_100000_00000_abcde",\n    "infoUri": "https://trino.example.com/ui/query.html?20240320_100000_00000_abcde",\n    "stats": {\n        "state": "CANCELED",\n        "queued": false,\n        "scheduled": false,\n        "nodes": 1,\n        "totalSplits": 10,\n        "queuedSplits": 0,\n        "runningSplits": 0,\n        "completedSplits": 5,\n        "cpuTimeMillis": 1000,\n        "wallTimeMillis": 2000,\n        "queuedTimeMillis": 0,\n        "elapsedTimeMillis": 2000,\n        "processedRows": 1000,\n        "processedBytes": 10000,\n        "peakMemoryBytes": 1000000\n    }\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"query-management",children:"Query Management"}),"\n",(0,r.jsx)(n.h4,{id:"list-queries",children:"List Queries"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /query\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"state"})," (optional): Filter by query state"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"user"})," (optional): Filter by user"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"limit"})," (optional): Maximum number of queries to return"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "queries": [\n        {\n            "queryId": "20240320_100000_00000_abcde",\n            "session": {\n                "user": "<EMAIL>",\n                "schema": "default",\n                "catalog": "hive",\n                "timeZone": "UTC"\n            },\n            "state": "RUNNING",\n            "memoryPool": "general",\n            "scheduled": true,\n            "self": "https://trino.example.com/v1/query/20240320_100000_00000_abcde",\n            "query": "SELECT * FROM my_table WHERE date >= DATE \'2024-03-20\'",\n            "queryStats": {\n                "createTime": "2024-03-20T10:00:00Z",\n                "endTime": null,\n                "elapsedTimeMillis": 2000,\n                "queuedTimeMillis": 0,\n                "totalDrivers": 10,\n                "queuedDrivers": 0,\n                "runningDrivers": 5,\n                "completedDrivers": 5,\n                "blockedDrivers": 0,\n                "cumulativeUserMemory": 1000000,\n                "userMemoryReservation": 1000000,\n                "peakUserMemoryReservation": 1000000,\n                "totalCpuTime": 1000,\n                "totalScheduledTime": 2000,\n                "peakTotalMemory": 1000000,\n                "totalAllocation": 1000000\n            }\n        }\n    ]\n}\n'})}),"\n",(0,r.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,r.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'import trino\nimport pandas as pd\n\n# Initialize client\nconn = trino.dbapi.connect(\n    host="trino.example.com",\n    port=443,\n    user="<EMAIL>",\n    catalog="hive",\n    schema="default",\n    auth=trino.auth.OAuth2Authentication("your-token")\n)\n\n# Execute query\ncursor = conn.cursor()\ncursor.execute("""\n    SELECT date, count, value\n    FROM my_table\n    WHERE date >= DATE \'2024-03-20\'\n    LIMIT 1000\n""")\n\n# Fetch results\nrows = cursor.fetchall()\ncolumns = [desc[0] for desc in cursor.description]\ndf = pd.DataFrame(rows, columns=columns)\nprint(df)\n'})}),"\n",(0,r.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:'# Submit query\ncurl -X POST https://trino.example.com/v1/statement \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "X-Trino-User: <EMAIL>" \\\n  -H "X-Trino-Schema: default" \\\n  -H "X-Trino-Catalog: hive" \\\n  -H "X-Trino-Time-Zone: UTC" \\\n  -d "SELECT * FROM my_table WHERE date >= DATE \'2024-03-20\'"\n\n# Get query results\ncurl -X GET https://trino.example.com/v1/statement/20240320_100000_00000_abcde/1 \\\n  -H "Authorization: Bearer ${TOKEN}"\n\n# Cancel query\ncurl -X DELETE https://trino.example.com/v1/statement/20240320_100000_00000_abcde \\\n  -H "Authorization: Bearer ${TOKEN}"\n'})}),"\n",(0,r.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,r.jsxs)(n.table,{children:[(0,r.jsx)(n.thead,{children:(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.th,{children:"Code"}),(0,r.jsx)(n.th,{children:"Description"})]})}),(0,r.jsxs)(n.tbody,{children:[(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"400"}),(0,r.jsx)(n.td,{children:"Bad Request - Invalid SQL query"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"401"}),(0,r.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"403"}),(0,r.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"404"}),(0,r.jsx)(n.td,{children:"Not Found - Query doesn't exist"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"429"}),(0,r.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"500"}),(0,r.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,r.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,r.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,r.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Query Optimization"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Use appropriate filters"}),"\n",(0,r.jsx)(n.li,{children:"Limit result set size"}),"\n",(0,r.jsx)(n.li,{children:"Use proper indexing"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Resource Management"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Monitor query execution"}),"\n",(0,r.jsx)(n.li,{children:"Cancel long-running queries"}),"\n",(0,r.jsx)(n.li,{children:"Use appropriate timeouts"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Performance"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Use prepared statements"}),"\n",(0,r.jsx)(n.li,{children:"Implement proper error handling"}),"\n",(0,r.jsx)(n.li,{children:"Use connection pooling"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Security"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Use parameterized queries"}),"\n",(0,r.jsx)(n.li,{children:"Implement proper access controls"}),"\n",(0,r.jsx)(n.li,{children:"Monitor query patterns"}),"\n"]}),"\n"]}),"\n"]})]})}function u(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(o,{...e})}):o(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>a});var t=i(6540);const r={},s=t.createContext(r);function l(e){const n=t.useContext(s);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:l(e.components),t.createElement(s.Provider,{value:n},e.children)}}}]);