"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3042],{3042:(e,t,i)=>{i.r(t),i.d(t,{default:()=>h});i(6540);var n=i(539),r=i(4737),o=i(9562),s=i(5932),a=i(4848);function h(){const e=(0,n.T)({id:"theme.NotFound.title",message:"Page Not Found"});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.be,{title:e}),(0,a.jsx)(o.A,{children:(0,a.jsx)(s.A,{})})]})}},5932:(e,t,i)=>{i.d(t,{A:()=>a});i(6540);var n=i(4164),r=i(539),o=i(9303),s=i(4848);function a({className:e}){return(0,s.jsx)("main",{className:(0,n.A)("container margin-vert--xl",e),children:(0,s.jsx)("div",{className:"row",children:(0,s.jsxs)("div",{className:"col col--6 col--offset-3",children:[(0,s.jsx)(o.A,{as:"h1",className:"hero__title",children:(0,s.jsx)(r.A,{id:"theme.NotFound.title",description:"The title of the 404 page",children:"Page Not Found"})}),(0,s.jsx)("p",{children:(0,s.jsx)(r.A,{id:"theme.NotFound.p1",description:"The first paragraph of the 404 page",children:"We could not find what you were looking for."})}),(0,s.jsx)("p",{children:(0,s.jsx)(r.A,{id:"theme.NotFound.p2",description:"The 2nd paragraph of the 404 page",children:"Please contact the owner of the site that linked you to the original URL and let them know their link is broken."})})]})})})}}}]);