"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3107],{8453:(e,n,t)=>{t.d(n,{R:()=>r,x:()=>a});var i=t(6540);const s={},l=i.createContext(s);function r(e){const n=i.useContext(l);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:r(e.components),i.createElement(l.Provider,{value:n},e.children)}},9738:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>c,contentTitle:()=>a,default:()=>h,frontMatter:()=>r,metadata:()=>i,toc:()=>d});const i=JSON.parse('{"id":"ai-architecture/tools/old/api/data-management/minio-api","title":"MinIO API Documentation","description":"MinIO provides an S3-compatible API for object storage operations. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/data-management/minio-api.md","sourceDirName":"ai-architecture/tools/old/api/data-management","slug":"/ai-architecture/tools/old/api/data-management/minio-api","permalink":"/docs/ai-architecture/tools/old/api/data-management/minio-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/data-management/minio-api.md","tags":[],"version":"current","frontMatter":{"id":"minio-api","title":"MinIO API Documentation","sidebar_label":"MinIO API"},"sidebar":"tutorialSidebar","previous":{"title":"LakeFS API","permalink":"/docs/ai-architecture/tools/old/api/data-management/lakefs-api"},"next":{"title":"OpenMetadata API","permalink":"/docs/ai-architecture/tools/old/api/data-management/openmetadata-api"}}');var s=t(4848),l=t(8453);const r={id:"minio-api",title:"MinIO API Documentation",sidebar_label:"MinIO API"},a="MinIO API Documentation",c={},d=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Bucket Operations",id:"bucket-operations",level:3},{value:"Create Bucket",id:"create-bucket",level:4},{value:"List Buckets",id:"list-buckets",level:4},{value:"Object Operations",id:"object-operations",level:3},{value:"Upload Object",id:"upload-object",level:4},{value:"Get Object",id:"get-object",level:4},{value:"List Objects",id:"list-objects",level:4},{value:"Multipart Upload",id:"multipart-upload",level:3},{value:"Initiate Multipart Upload",id:"initiate-multipart-upload",level:4},{value:"Upload Part",id:"upload-part",level:4},{value:"Complete Multipart Upload",id:"complete-multipart-upload",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,l.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"minio-api-documentation",children:"MinIO API Documentation"})}),"\n",(0,s.jsx)(n.p,{children:"MinIO provides an S3-compatible API for object storage operations. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,s.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"https://minio.example.com\n"})}),"\n",(0,s.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,s.jsx)(n.p,{children:"All API requests require authentication using AWS Signature Version 4. Include the following headers:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"Authorization: AWS4-HMAC-SHA256 Credential=<access-key>/<date>/<region>/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=<signature>\nx-amz-date: <timestamp>\n"})}),"\n",(0,s.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,s.jsx)(n.h3,{id:"bucket-operations",children:"Bucket Operations"}),"\n",(0,s.jsx)(n.h4,{id:"create-bucket",children:"Create Bucket"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"PUT /{bucket-name}\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Headers:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"x-amz-acl"})," (optional): Bucket access control list"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"x-amz-bucket-object-lock-enabled"})," (optional): Enable object lock"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "bucket": "my-bucket",\n    "created": true,\n    "location": "us-east-1"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"list-buckets",children:"List Buckets"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "buckets": [\n        {\n            "name": "my-bucket",\n            "creation_date": "2024-03-20T10:00:00Z"\n        }\n    ],\n    "owner": {\n        "id": "minio",\n        "display_name": "minio"\n    }\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"object-operations",children:"Object Operations"}),"\n",(0,s.jsx)(n.h4,{id:"upload-object",children:"Upload Object"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"PUT /{bucket-name}/{object-name}\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Headers:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"Content-Type"}),": MIME type of the object"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"x-amz-meta-*"}),": Custom metadata"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"x-amz-storage-class"}),": Storage class (STANDARD, REDUCED_REDUNDANCY)"]}),"\n"]}),"\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"Request Body:"}),"\nBinary content of the object"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "etag": "\\"d41d8cd98f00b204e9800998ecf8427e\\"",\n    "version_id": "null"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"get-object",children:"Get Object"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /{bucket-name}/{object-name}\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"versionId"})," (optional): Object version ID"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"response-content-type"})," (optional): Override response content type"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"response-content-disposition"})," (optional): Override response content disposition"]}),"\n"]}),"\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"Response:"}),"\nBinary content of the object"]}),"\n",(0,s.jsx)(n.h4,{id:"list-objects",children:"List Objects"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /{bucket-name}\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"prefix"})," (optional): Filter objects by prefix"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"delimiter"})," (optional): Character used to group keys"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"max-keys"})," (optional): Maximum number of keys to return"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"marker"})," (optional): Key to start listing from"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "name": "my-bucket",\n    "prefix": "",\n    "marker": "",\n    "max_keys": 1000,\n    "is_truncated": false,\n    "contents": [\n        {\n            "key": "example.txt",\n            "last_modified": "2024-03-20T10:00:00Z",\n            "etag": "\\"d41d8cd98f00b204e9800998ecf8427e\\"",\n            "size": 1024,\n            "storage_class": "STANDARD",\n            "owner": {\n                "id": "minio",\n                "display_name": "minio"\n            }\n        }\n    ]\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"multipart-upload",children:"Multipart Upload"}),"\n",(0,s.jsx)(n.h4,{id:"initiate-multipart-upload",children:"Initiate Multipart Upload"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /{bucket-name}/{object-name}?uploads\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Headers:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"Content-Type"}),": MIME type of the object"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"x-amz-meta-*"}),": Custom metadata"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "bucket": "my-bucket",\n    "key": "large-file.zip",\n    "upload_id": "abc123..."\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"upload-part",children:"Upload Part"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"PUT /{bucket-name}/{object-name}?partNumber={part-number}&uploadId={upload-id}\n"})}),"\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"Request Body:"}),"\nBinary content of the part"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "etag": "\\"d41d8cd98f00b204e9800998ecf8427e\\""\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"complete-multipart-upload",children:"Complete Multipart Upload"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /{bucket-name}/{object-name}?uploadId={upload-id}\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "parts": [\n        {\n            "part_number": 1,\n            "etag": "\\"d41d8cd98f00b204e9800998ecf8427e\\""\n        }\n    ]\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "location": "https://minio.example.com/my-bucket/large-file.zip",\n    "bucket": "my-bucket",\n    "key": "large-file.zip",\n    "etag": "\\"d41d8cd98f00b204e9800998ecf8427e\\""\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,s.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'from minio import Minio\nfrom minio.error import S3Error\n\n# Initialize client\nclient = Minio(\n    "minio.example.com",\n    access_key="your-access-key",\n    secret_key="your-secret-key",\n    secure=True\n)\n\n# Create bucket\nclient.make_bucket("my-bucket")\n\n# Upload object\nclient.put_object(\n    "my-bucket",\n    "example.txt",\n    data="Hello, World!",\n    length=13,\n    content_type="text/plain"\n)\n\n# List objects\nobjects = client.list_objects("my-bucket", prefix="example")\nfor obj in objects:\n    print(f"Object: {obj.object_name}, Size: {obj.size}")\n\n# Download object\ndata = client.get_object("my-bucket", "example.txt")\nprint(data.read().decode())\n'})}),"\n",(0,s.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:'# Create bucket\ncurl -X PUT https://minio.example.com/my-bucket \\\n  -H "Authorization: AWS4-HMAC-SHA256 Credential=${ACCESS_KEY}/20240320/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=${SIGNATURE}" \\\n  -H "x-amz-date: 20240320T100000Z"\n\n# Upload object\ncurl -X PUT https://minio.example.com/my-bucket/example.txt \\\n  -H "Authorization: AWS4-HMAC-SHA256 Credential=${ACCESS_KEY}/20240320/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=${SIGNATURE}" \\\n  -H "x-amz-date: 20240320T100000Z" \\\n  -H "Content-Type: text/plain" \\\n  -d "Hello, World!"\n\n# List objects\ncurl -X GET https://minio.example.com/my-bucket \\\n  -H "Authorization: AWS4-HMAC-SHA256 Credential=${ACCESS_KEY}/20240320/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=${SIGNATURE}" \\\n  -H "x-amz-date: 20240320T100000Z"\n'})}),"\n",(0,s.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Code"}),(0,s.jsx)(n.th,{children:"Description"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"400"}),(0,s.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"401"}),(0,s.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"403"}),(0,s.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"404"}),(0,s.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"409"}),(0,s.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"429"}),(0,s.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"500"}),(0,s.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,s.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,s.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,s.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Bucket Management"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Use meaningful bucket names"}),"\n",(0,s.jsx)(n.li,{children:"Configure appropriate access policies"}),"\n",(0,s.jsx)(n.li,{children:"Enable versioning when needed"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Object Operations"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Use appropriate content types"}),"\n",(0,s.jsx)(n.li,{children:"Set proper metadata"}),"\n",(0,s.jsx)(n.li,{children:"Implement proper error handling"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Performance"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Use multipart upload for large files"}),"\n",(0,s.jsx)(n.li,{children:"Implement proper retry logic"}),"\n",(0,s.jsx)(n.li,{children:"Use appropriate chunk sizes"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Security"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Rotate access keys regularly"}),"\n",(0,s.jsx)(n.li,{children:"Use HTTPS for all operations"}),"\n",(0,s.jsx)(n.li,{children:"Implement proper access controls"}),"\n"]}),"\n"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(o,{...e})}):o(e)}}}]);