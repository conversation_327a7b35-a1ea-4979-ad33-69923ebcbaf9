"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8828],{218:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>l,default:()=>h,frontMatter:()=>a,metadata:()=>r,toc:()=>d});const r=JSON.parse('{"id":"research/fhir/fhir","title":"FHIR Database Strategy","description":"1. Introduction","source":"@site/docs/research/fhir/fhir.md","sourceDirName":"research/fhir","slug":"/research/fhir/","permalink":"/docs/research/fhir/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/research/fhir/fhir.md","tags":[],"version":"current","frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"FHIR","permalink":"/docs/category/fhir"},"next":{"title":"API Reference","permalink":"/docs/ai-architecture/api/"}}');var s=i(4848),t=i(8453);const a={},l="FHIR Database Strategy",o={},d=[{value:"1. Introduction",id:"1-introduction",level:2},{value:"2. Core Database Requirements for FHIR",id:"2-core-database-requirements-for-fhir",level:2},{value:"3. Database Options Explored",id:"3-database-options-explored",level:2},{value:"3.1. PostgreSQL (Traditional SQL + Extensions)",id:"31-postgresql-traditional-sql--extensions",level:3},{value:"3.2. MongoDB (Document Database)",id:"32-mongodb-document-database",level:3},{value:"3.3. Distributed SQL (Spanner, CockroachDB)",id:"33-distributed-sql-spanner-cockroachdb",level:3},{value:"4. Suggested Approach: CockroachDB (Distributed SQL)",id:"4-suggested-approach-cockroachdb-distributed-sql",level:2},{value:"4.1. Proposed Data Model (Table per Resource Type)",id:"41-proposed-data-model-table-per-resource-type",level:3},{value:"4.2. Indexing Strategy",id:"42-indexing-strategy",level:3},{value:"4.3. Normalization Strategy",id:"43-normalization-strategy",level:3},{value:"4.4. Handling Relationships (<code>_include</code>/<code>_revinclude</code>)",id:"44-handling-relationships-_include_revinclude",level:3},{value:"4.5. Performance &amp; Scalability",id:"45-performance--scalability",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"fhir-database-strategy",children:"FHIR Database Strategy"})}),"\n",(0,s.jsx)(n.h2,{id:"1-introduction",children:"1. Introduction"}),"\n",(0,s.jsx)(n.p,{children:"This document summarizes the research and decisions regarding the database\nstrategy for our scalable FHIR server backend. The core challenge is balancing\nthe need to efficiently store and query complex FHIR resources, support standard\nFHIR search capabilities (including relationships and normalization), while\nensuring the system can scale horizontally to handle potentially massive\nhealthcare data volumes and high throughput."}),"\n",(0,s.jsx)(n.h2,{id:"2-core-database-requirements-for-fhir",children:"2. Core Database Requirements for FHIR"}),"\n",(0,s.jsx)(n.p,{children:"Based on the FHIR specification and anticipated usage patterns, the database solution\nmust effectively support:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Resource Storage:"})," Efficiently store FHIR resources, which are complex, semi-structured documents (often\nrepresented as JSON)."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"FHIR Search:"})," Execute standard FHIR search parameters efficiently, including searches on nested fields within\nresources."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Search Normalization:"})," Support common search expectations like case-insensitivity and diacritic-insensitivity\nfor string parameters, and potentially normalization for other types (dates, tokens, quantities)."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Relationship Handling:"})," Efficiently retrieve related resources as specified by ",(0,s.jsx)(n.code,{children:"_include"})," and ",(0,s.jsx)(n.code,{children:"_revinclude"}),"\nparameters."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"High Availability (HA):"})," Ensure data durability and service uptime through redundancy/failover mechanisms."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Horizontal Scalability:"})," Ability to scale out by adding more servers to handle growing data volumes and user\nload (sharding)."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Write Performance:"})," Maintain acceptable insert and update performance even with the necessary indexes for\nefficient searching."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Data Integrity:"})," Ensure consistency appropriate for healthcare data (level of transactional guarantee needed)."]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"3-database-options-explored",children:"3. Database Options Explored"}),"\n",(0,s.jsx)(n.p,{children:"We considered several database categories:"}),"\n",(0,s.jsx)(n.h3,{id:"31-postgresql-traditional-sql--extensions",children:"3.1. PostgreSQL (Traditional SQL + Extensions)"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Models:"})," HAPI FHIR (normalized index tables) or Aidbox (JSONB + Functional Indexes)."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Pros:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Mature ACID transactions and relational integrity features."}),"\n",(0,s.jsxs)(n.li,{children:["Powerful SQL query language, including native ",(0,s.jsx)(n.code,{children:"JOIN"})," operations ideal for efficiently handling\n",(0,s.jsx)(n.code,{children:"_include"}),"/",(0,s.jsx)(n.code,{children:"_revinclude"})," (often lower latency)."]}),"\n",(0,s.jsx)(n.li,{children:"Strong JSONB support allows storing FHIR documents effectively."}),"\n",(0,s.jsx)(n.li,{children:"Functional Indexes (Aidbox style) allow indexing expressions on JSONB data, enabling search within documents."}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Cons:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Horizontal scaling (sharding) is traditionally complex."})," Cross-shard JOINs, transactions, and referential\nintegrity are difficult to manage efficiently without specialized extensions (e.g., CitusDB) or architectures.\nOften requires significant operational expertise or vertical scaling limits."]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"32-mongodb-document-database",children:"3.2. MongoDB (Document Database)"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Models:"})," Nested Map or Binary Proto + Extracted Search Params."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Pros:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Excellent horizontal scaling"})," via native, mature sharding capabilities. Easier to scale out for large\ndatasets/throughput compared to traditional SQL sharding."]}),"\n",(0,s.jsx)(n.li,{children:"Document model aligns naturally with FHIR resource structure."}),"\n",(0,s.jsx)(n.li,{children:"Flexible schema evolution."}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Cons:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"No native JOINs across collections."})," Handling ",(0,s.jsx)(n.code,{children:"_include"}),"/",(0,s.jsx)(n.code,{children:"_revinclude"})," requires multiple application-level\nqueries (increasing latency) or complex ",(0,s.jsx)(n.code,{children:"$lookup"})," aggregations."]}),"\n",(0,s.jsx)(n.li,{children:"Multi-document/multi-shard ACID transactions are more complex than traditional SQL."}),"\n",(0,s.jsx)(n.li,{children:"Relational integrity is not enforced by the database; requires application logic."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"33-distributed-sql-spanner-cockroachdb",children:"3.3. Distributed SQL (Spanner, CockroachDB)"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Models:"})," Relational tables, potentially with JSON/JSONB columns."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Pros:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:["Designed for ",(0,s.jsx)(n.strong,{children:"both horizontal scalability AND strong consistency"})," (often serializable ACID)."]}),"\n",(0,s.jsxs)(n.li,{children:["Support distributed SQL ",(0,s.jsx)(n.strong,{children:"JOINs"}),", potentially offering low latency for ",(0,s.jsx)(n.code,{children:"_include"}),"/",(0,s.jsx)(n.code,{children:"_revinclude"})," similar\nto traditional SQL but in a scalable architecture."]}),"\n",(0,s.jsx)(n.li,{children:"Often provide SQL interfaces (PostgreSQL compatible for CockroachDB)."}),"\n",(0,s.jsx)(n.li,{children:"Support JSON types and functional index equivalents (Expression Indexes in CockroachDB, Generated Columns\nin Spanner)."}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Cons:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Can be complex to operate, tune, and understand (distributed system nuances)."}),"\n",(0,s.jsx)(n.li,{children:"Potentially higher cost (especially managed services like Spanner)."}),"\n",(0,s.jsx)(n.li,{children:"Newer ecosystem compared to traditional SQL or MongoDB."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"4-suggested-approach-cockroachdb-distributed-sql",children:"4. Suggested Approach: CockroachDB (Distributed SQL)"}),"\n",(0,s.jsxs)(n.p,{children:["Based on the requirements, particularly the need for both ",(0,s.jsxs)(n.strong,{children:["efficient relationship handling (JOINs for\n",(0,s.jsx)(n.code,{children:"_include"}),"/",(0,s.jsx)(n.code,{children:"_revinclude"}),") and native horizontal scalability"]}),", we suggest to proceed with ",(0,s.jsx)(n.strong,{children:"CockroachDB"}),"."]}),"\n",(0,s.jsxs)(n.p,{children:[(0,s.jsx)(n.strong,{children:"Rationale:"})," CockroachDB provides a unique combination that addresses the core challenges. It allows\nus to leverage the power and familiarity of SQL for complex queries and JOINs while offering the seamless\nhorizontal scalability required for potentially massive FHIR datasets. Its support for JSONB and PostgreSQL-compatible\nExpression Indexes directly addresses the need to store and efficiently query FHIR resource documents."]}),"\n",(0,s.jsx)(n.h3,{id:"41-proposed-data-model-table-per-resource-type",children:"4.1. Proposed Data Model (Table per Resource Type)"}),"\n",(0,s.jsx)(n.p,{children:"We will use a relational schema in CockroachDB, likely with a table per FHIR resource type:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-sql",children:"-- Example table for Patients (simplified)\nCREATE TABLE patients (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- Internal primary key\n    logical_id STRING NOT NULL UNIQUE,           -- FHIR logical ID (e.g., 'pat123')\n    version_id STRING NOT NULL,                  -- FHIR version ID\n    last_updated TIMESTAMPTZ NOT NULL,           -- FHIR meta.lastUpdated\n    is_deleted BOOL DEFAULT false,              -- For soft deletes\n    resource_data JSONB NOT NULL,                -- Stores the full FHIR resource as JSONB\n    -- Potentially add other top-level indexed metadata if frequently queried without JSONB access\n    INDEX (last_updated),\n    INDEX (is_deleted) WHERE is_deleted=true -- Index only deleted resources if needed often\n    -- CockroachDB automatically interleaves tables for locality if desired (e.g., observations interleaved with patients)\n);\n\n-- Example table for Observations (simplified)\nCREATE TABLE observations (\n    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n    logical_id STRING NOT NULL UNIQUE,\n    version_id STRING NOT NULL,\n    last_updated TIMESTAMPTZ NOT NULL,\n    is_deleted BOOL DEFAULT false,\n    resource_data JSONB NOT NULL,\n    -- Index top-level metadata\n    INDEX (last_updated)\n);\n"})}),"\n",(0,s.jsx)(n.h3,{id:"42-indexing-strategy",children:"4.2. Indexing Strategy"}),"\n",(0,s.jsx)(n.p,{children:"We will leverage CockroachDB's indexing capabilities:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Primary/Standard Indexes: On primary keys (id) and frequently queried top-level metadata (logical_id, last_updated)."}),"\n",(0,s.jsxs)(n.li,{children:["Expression Indexes (Functional Indexes): These are crucial for searching within the resource_data JSONB column.\nWe will create indexes on expressions that extract and potentially normalize FHIR search parameter values.","\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-sql",children:"-- Example: Index for case-insensitive search on first family name\nCREATE INDEX idx_patient_family_normalized ON patients ((lower(resource_data->'name'->0->>'family')));\n\n-- Example: Index for Observation code (first coding system/code)\nCREATE INDEX idx_observation_code ON observations ((resource_data->'code'->'coding'->0->>'system'), (resource_data->'code'->'coding'->0->>'code'));\n\n-- Example: Index for Observation date\nCREATE INDEX idx_observation_date ON observations (((resource_data->>'effectiveDateTime')::TIMESTAMPTZ)); -- Cast JSON text to timestamp\n"})}),"\n"]}),"\n",(0,s.jsx)(n.li,{children:"GIN Indexes (Optional): For more complex JSONB searching (e.g., checking for the existence of keys or matching\nmultiple values within arrays using operators like @>), GIN indexes on the resource_data column could be used,\nthough targeted expression indexes are generally preferred for specific search parameters."}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"43-normalization-strategy",children:"4.3. Normalization Strategy"}),"\n",(0,s.jsx)(n.p,{children:"Normalization (case-insensitivity, diacritics, UTC dates) will primarily be handled within the Expression Index\ndefinitions:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:["Strings: Use functions like ",(0,s.jsx)(n.code,{children:"lower()"})," within the index definition. Accent removal might require custom functions\nor pre-processing if not built-in. Search terms must be similarly normalized at query time."]}),"\n",(0,s.jsxs)(n.li,{children:["Dates: Cast JSON string dates to ",(0,s.jsx)(n.code,{children:"TIMESTAMPTZ"})," within the index expression, which normalizes them to UTC for\nreliable comparison.\nOther Types: Extract relevant components (e.g., token system/code, reference parts) using JSONB operators\nwithin the index expressions."]}),"\n"]}),"\n",(0,s.jsxs)(n.h3,{id:"44-handling-relationships-_include_revinclude",children:["4.4. Handling Relationships (",(0,s.jsx)(n.code,{children:"_include"}),"/",(0,s.jsx)(n.code,{children:"_revinclude"}),")"]}),"\n",(0,s.jsx)(n.p,{children:"Leverage standard SQL JOINs:"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["The application translates the FHIR search (with ",(0,s.jsx)(n.code,{children:"_include"})," or ",(0,s.jsx)(n.code,{children:"_revinclude"}),") into a SQL query."]}),"\n",(0,s.jsx)(n.li,{children:"This query JOINs the resource table(s) with appropriate index expressions or other tables based on the\nrelationships defined by the FHIR parameters."}),"\n"]}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-sql",children:"-- Conceptual Example: GET /Patient?_id=pat123&_revinclude=Observation:subject\nSELECT\n    p.resource_data AS patient_resource,\n    o.resource_data AS observation_resource\nFROM\n    patients p\nLEFT JOIN -- Find observations referencing this patient\n    observations o ON (o.resource_data->'subject'->>'reference') = ('Patient/' || p.logical_id) -- Join based on reference path in JSONB\nWHERE\n    p.logical_id = 'pat123'; -- Find the specific patient\n-- Note: Performance depends on having an appropriate expression index on the reference path in the 'observations' table.\n"})}),"\n",(0,s.jsxs)(n.ol,{start:"3",children:["\n",(0,s.jsx)(n.li,{children:"CockroachDB's distributed SQL engine optimizes and executes the JOIN across the necessary nodes in the cluster."}),"\n",(0,s.jsx)(n.li,{children:"Results are returned to the application for Bundle construction."}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"45-performance--scalability",children:"4.5. Performance & Scalability"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Reads: Performance relies on well-designed Expression Indexes for specific searches and efficient distributed\nJOIN execution for includes/revincludes."}),"\n",(0,s.jsx)(n.li,{children:"Writes: Indexes add overhead. CockroachDB manages Raft replication for consistency, which impacts write latency.\nTuning batching and transaction sizes is important."}),"\n",(0,s.jsx)(n.li,{children:"Scalability: Add more CockroachDB nodes. The database automatically rebalances data ranges (shards) across nodes.\nPerformance scales near-linearly for many workloads if schema/queries are designed well to avoid hotspots."}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(c,{...e})}):c(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>a,x:()=>l});var r=i(6540);const s={},t=r.createContext(s);function a(e){const n=r.useContext(t);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:a(e.components),r.createElement(t.Provider,{value:n},e.children)}}}]);