"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2694],{7445:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>s,contentTitle:()=>a,default:()=>u,frontMatter:()=>r,metadata:()=>i,toc:()=>c});var i=n(7664),l=n(4848),o=n(8453);const r={slug:"welcome",title:"Welcome to the 91.life AI Platform Documentation",author:"hortense, jean, albin, alban",tags:["welcome","documentation"]},a=void 0,s={authorsImageUrls:[void 0]},c=[{value:"What&#39;s New",id:"whats-new",level:2},{value:"Getting Started",id:"getting-started",level:2},{value:"Stay Updated",id:"stay-updated",level:2}];function d(e){const t={a:"a",h2:"h2",li:"li",ol:"ol",p:"p",ul:"ul",...(0,o.R)(),...e.components};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.p,{children:"Welcome to the official documentation for the 91.life AI Platform! This documentation site will serve as your comprehensive guide to understanding, implementing, and maintaining our enterprise AI platform."}),"\n",(0,l.jsx)(t.h2,{id:"whats-new",children:"What's New"}),"\n",(0,l.jsxs)(t.ul,{children:["\n",(0,l.jsx)(t.li,{children:"Complete documentation structure"}),"\n",(0,l.jsx)(t.li,{children:"API reference"}),"\n",(0,l.jsx)(t.li,{children:"Implementation guides"}),"\n",(0,l.jsx)(t.li,{children:"Best practices"}),"\n",(0,l.jsx)(t.li,{children:"Security guidelines"}),"\n"]}),"\n",(0,l.jsx)(t.h2,{id:"getting-started",children:"Getting Started"}),"\n",(0,l.jsx)(t.p,{children:"To get started with the documentation:"}),"\n",(0,l.jsxs)(t.ol,{children:["\n",(0,l.jsxs)(t.li,{children:["Check out the ",(0,l.jsx)(t.a,{href:"/docs/intro",children:"Getting Started"})," guide"]}),"\n",(0,l.jsxs)(t.li,{children:["Review the ",(0,l.jsx)(t.a,{href:"/docs/architecture-overview",children:"Architecture Overview"})]}),"\n",(0,l.jsxs)(t.li,{children:["Explore the ",(0,l.jsx)(t.a,{href:"/docs/implementation",children:"Implementation"})," section"]}),"\n"]}),"\n",(0,l.jsx)(t.h2,{id:"stay-updated",children:"Stay Updated"}),"\n",(0,l.jsx)(t.p,{children:"We'll be regularly updating this documentation with:"}),"\n",(0,l.jsxs)(t.ul,{children:["\n",(0,l.jsx)(t.li,{children:"New features and capabilities"}),"\n",(0,l.jsx)(t.li,{children:"Best practices and guidelines"}),"\n",(0,l.jsx)(t.li,{children:"Security updates"}),"\n",(0,l.jsx)(t.li,{children:"API changes"}),"\n"]}),"\n",(0,l.jsx)(t.p,{children:"Stay tuned for more updates!"})]})}function u(e={}){const{wrapper:t}={...(0,o.R)(),...e.components};return t?(0,l.jsx)(t,{...e,children:(0,l.jsx)(d,{...e})}):d(e)}},7664:e=>{e.exports=JSON.parse('{"permalink":"/blog/welcome","source":"@site/blog/2024-03-21-welcome.md","title":"Welcome to the 91.life AI Platform Documentation","description":"Welcome to the official documentation for the 91.life AI Platform! This documentation site will serve as your comprehensive guide to understanding, implementing, and maintaining our enterprise AI platform.","date":"2024-03-21T00:00:00.000Z","tags":[{"inline":true,"label":"welcome","permalink":"/blog/tags/welcome"},{"inline":true,"label":"documentation","permalink":"/blog/tags/documentation"}],"readingTime":0.52,"hasTruncateMarker":false,"authors":[{"name":"hortense, jean, albin, alban","key":null,"page":null}],"frontMatter":{"slug":"welcome","title":"Welcome to the 91.life AI Platform Documentation","author":"hortense, jean, albin, alban","tags":["welcome","documentation"]},"unlisted":false}')},8453:(e,t,n)=>{n.d(t,{R:()=>r,x:()=>a});var i=n(6540);const l={},o=i.createContext(l);function r(e){const t=i.useContext(o);return i.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function a(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(l):e.components||l:r(e.components),i.createElement(o.Provider,{value:t},e.children)}}}]);