"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7239],{6063:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>c,contentTitle:()=>l,default:()=>o,frontMatter:()=>i,metadata:()=>r,toc:()=>u});const r=JSON.parse('{"id":"ai-architecture/tools/old/api/data-management/feature-store-api","title":"Feature Store API Documentation","description":"The Feature Store provides a RESTful API for managing and serving ML features. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/data-management/feature-store-api.md","sourceDirName":"ai-architecture/tools/old/api/data-management","slug":"/ai-architecture/tools/old/api/data-management/feature-store-api","permalink":"/docs/ai-architecture/tools/old/api/data-management/feature-store-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/data-management/feature-store-api.md","tags":[],"version":"current","frontMatter":{"id":"feature-store-api","title":"Feature Store API Documentation","sidebar_label":"Feature Store API"},"sidebar":"tutorialSidebar","previous":{"title":"Data Quality API","permalink":"/docs/ai-architecture/tools/old/api/data-management/data-quality-api"},"next":{"title":"LakeFS API","permalink":"/docs/ai-architecture/tools/old/api/data-management/lakefs-api"}}');var s=t(4848),a=t(8453);const i={id:"feature-store-api",title:"Feature Store API Documentation",sidebar_label:"Feature Store API"},l="Feature Store API Documentation",c={},u=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Feature Management",id:"feature-management",level:3},{value:"Create Feature",id:"create-feature",level:4},{value:"List Features",id:"list-features",level:4},{value:"Feature Set Management",id:"feature-set-management",level:3},{value:"Create Feature Set",id:"create-feature-set",level:4},{value:"List Feature Sets",id:"list-feature-sets",level:4},{value:"Feature Serving",id:"feature-serving",level:3},{value:"Get Feature Values",id:"get-feature-values",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function d(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,a.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"feature-store-api-documentation",children:"Feature Store API Documentation"})}),"\n",(0,s.jsx)(n.p,{children:"The Feature Store provides a RESTful API for managing and serving ML features. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,s.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{children:"https://feature-store.91.life/api/v1\n"})}),"\n",(0,s.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,s.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,s.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,s.jsx)(n.h3,{id:"feature-management",children:"Feature Management"}),"\n",(0,s.jsx)(n.h4,{id:"create-feature",children:"Create Feature"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /features\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "name": "user_click_count",\n    "description": "Number of clicks per user",\n    "type": "INT64",\n    "entity": "user",\n    "data_source": {\n        "type": "batch",\n        "path": "s3://my-bucket/features/user_clicks.parquet",\n        "format": "parquet"\n    },\n    "tags": {\n        "team": "recommendation",\n        "importance": "high"\n    }\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "feature_id": "user_click_count",\n    "name": "user_click_count",\n    "description": "Number of clicks per user",\n    "type": "INT64",\n    "entity": "user",\n    "created_at": "2024-03-20T10:00:00Z",\n    "status": "active"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"list-features",children:"List Features"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /features\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"entity"})," (optional): Filter by entity"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"tag"})," (optional): Filter by tag"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "features": [\n        {\n            "feature_id": "user_click_count",\n            "name": "user_click_count",\n            "description": "Number of clicks per user",\n            "type": "INT64",\n            "entity": "user",\n            "created_at": "2024-03-20T10:00:00Z",\n            "status": "active"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"feature-set-management",children:"Feature Set Management"}),"\n",(0,s.jsx)(n.h4,{id:"create-feature-set",children:"Create Feature Set"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /feature-sets\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "name": "user_features",\n    "description": "User-related features",\n    "features": [\n        "user_click_count",\n        "user_purchase_amount",\n        "user_last_active"\n    ],\n    "entity": "user",\n    "tags": {\n        "team": "recommendation",\n        "version": "v1"\n    }\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "feature_set_id": "user_features_v1",\n    "name": "user_features",\n    "description": "User-related features",\n    "features": [\n        "user_click_count",\n        "user_purchase_amount",\n        "user_last_active"\n    ],\n    "entity": "user",\n    "created_at": "2024-03-20T10:00:00Z",\n    "status": "active"\n}\n'})}),"\n",(0,s.jsx)(n.h4,{id:"list-feature-sets",children:"List Feature Sets"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"GET /feature-sets\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"entity"})," (optional): Filter by entity"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"tag"})," (optional): Filter by tag"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "feature_sets": [\n        {\n            "feature_set_id": "user_features_v1",\n            "name": "user_features",\n            "description": "User-related features",\n            "features": [\n                "user_click_count",\n                "user_purchase_amount",\n                "user_last_active"\n            ],\n            "entity": "user",\n            "created_at": "2024-03-20T10:00:00Z",\n            "status": "active"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,s.jsx)(n.h3,{id:"feature-serving",children:"Feature Serving"}),"\n",(0,s.jsx)(n.h4,{id:"get-feature-values",children:"Get Feature Values"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-http",children:"POST /feature-sets/{feature_set_id}/values\n"})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "entity_keys": ["user1", "user2", "user3"],\n    "feature_names": ["user_click_count", "user_purchase_amount"],\n    "timestamp": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Response:"})}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-json",children:'{\n    "values": [\n        {\n            "entity_key": "user1",\n            "features": {\n                "user_click_count": 42,\n                "user_purchase_amount": 150.50\n            }\n        },\n        {\n            "entity_key": "user2",\n            "features": {\n                "user_click_count": 15,\n                "user_purchase_amount": 75.25\n            }\n        },\n        {\n            "entity_key": "user3",\n            "features": {\n                "user_click_count": 28,\n                "user_purchase_amount": 200.00\n            }\n        }\n    ],\n    "metadata": {\n        "timestamp": "2024-03-20T10:00:00Z",\n        "feature_set_id": "user_features_v1"\n    }\n}\n'})}),"\n",(0,s.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,s.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-python",children:'from feature_store import Client\n\n# Initialize client\nclient = Client(\n    host="https://feature-store.91.life",\n    auth_token="your-token"\n)\n\n# Create feature\nfeature = {\n    "name": "user_click_count",\n    "description": "Number of clicks per user",\n    "type": "INT64",\n    "entity": "user",\n    "data_source": {\n        "type": "batch",\n        "path": "s3://my-bucket/features/user_clicks.parquet",\n        "format": "parquet"\n    }\n}\nfeature_id = client.create_feature(feature)\n\n# Get feature values\nvalues = client.get_feature_values(\n    feature_set_id="user_features_v1",\n    entity_keys=["user1", "user2", "user3"],\n    feature_names=["user_click_count", "user_purchase_amount"]\n)\nprint(values)\n'})}),"\n",(0,s.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,s.jsx)(n.pre,{children:(0,s.jsx)(n.code,{className:"language-bash",children:'# Create feature\ncurl -X POST https://feature-store.91.life/api/v1/features \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "user_click_count",\n    "description": "Number of clicks per user",\n    "type": "INT64",\n    "entity": "user",\n    "data_source": {\n      "type": "batch",\n      "path": "s3://my-bucket/features/user_clicks.parquet",\n      "format": "parquet"\n    }\n  }\'\n\n# Get feature values\ncurl -X POST https://feature-store.91.life/api/v1/feature-sets/user_features_v1/values \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "entity_keys": ["user1", "user2", "user3"],\n    "feature_names": ["user_click_count", "user_purchase_amount"],\n    "timestamp": "2024-03-20T10:00:00Z"\n  }\'\n'})}),"\n",(0,s.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,s.jsxs)(n.table,{children:[(0,s.jsx)(n.thead,{children:(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.th,{children:"Code"}),(0,s.jsx)(n.th,{children:"Description"})]})}),(0,s.jsxs)(n.tbody,{children:[(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"400"}),(0,s.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"401"}),(0,s.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"403"}),(0,s.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"404"}),(0,s.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"409"}),(0,s.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"429"}),(0,s.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,s.jsxs)(n.tr,{children:[(0,s.jsx)(n.td,{children:"500"}),(0,s.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,s.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,s.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,s.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Feature Management"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Use meaningful feature names"}),"\n",(0,s.jsx)(n.li,{children:"Document feature definitions"}),"\n",(0,s.jsx)(n.li,{children:"Version features appropriately"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Feature Sets"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Group related features"}),"\n",(0,s.jsx)(n.li,{children:"Use consistent naming"}),"\n",(0,s.jsx)(n.li,{children:"Document feature sets"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Performance"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Batch feature requests"}),"\n",(0,s.jsx)(n.li,{children:"Use appropriate caching"}),"\n",(0,s.jsx)(n.li,{children:"Monitor latency"}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Security"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Implement proper access controls"}),"\n",(0,s.jsx)(n.li,{children:"Monitor feature usage"}),"\n",(0,s.jsx)(n.li,{children:"Audit feature changes"}),"\n"]}),"\n"]}),"\n"]})]})}function o(e={}){const{wrapper:n}={...(0,a.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(d,{...e})}):d(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>i,x:()=>l});var r=t(6540);const s={},a=r.createContext(s);function i(e){const n=r.useContext(a);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:i(e.components),r.createElement(a.Provider,{value:n},e.children)}}}]);