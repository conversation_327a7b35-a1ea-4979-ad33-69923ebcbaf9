"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[534],{8453:(n,e,i)=>{i.d(e,{R:()=>l,x:()=>s});var r=i(6540);const t={},o=r.createContext(t);function l(n){const e=r.useContext(o);return r.useMemo((function(){return"function"==typeof n?n(e):{...e,...n}}),[e,n])}function s(n){let e;return e=n.disableParentContext?"function"==typeof n.components?n.components(t):n.components||t:l(n.components),r.createElement(o.Provider,{value:e},n.children)}},9713:(n,e,i)=>{i.r(e),i.d(e,{assets:()=>a,contentTitle:()=>s,default:()=>h,frontMatter:()=>l,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/tools/old/api/monitoring/monitoring","title":"Monitoring APIs","description":"This section contains documentation for APIs related to monitoring and alerting in the MLOps platform. These APIs provide comprehensive functionality for monitoring model performance, system health, and data quality.","source":"@site/docs/ai-architecture/tools/old/api/monitoring/index.md","sourceDirName":"ai-architecture/tools/old/api/monitoring","slug":"/ai-architecture/tools/old/api/monitoring/","permalink":"/docs/ai-architecture/tools/old/api/monitoring/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/monitoring/index.md","tags":[],"version":"current","frontMatter":{"id":"monitoring","title":"Monitoring APIs","sidebar_label":"Monitoring"},"sidebar":"tutorialSidebar","previous":{"title":"Model Serving API","permalink":"/docs/ai-architecture/tools/old/api/model-management/model-serving-api"},"next":{"title":"Alerting API","permalink":"/docs/ai-architecture/tools/old/api/monitoring/alerting-api"}}');var t=i(4848),o=i(8453);const l={id:"monitoring",title:"Monitoring APIs",sidebar_label:"Monitoring"},s="Monitoring APIs",a={},c=[{value:"Model Monitoring",id:"model-monitoring",level:2},{value:"Alerting",id:"alerting",level:2},{value:"Best Practices",id:"best-practices",level:2},{value:"Related Resources",id:"related-resources",level:2}];function d(n){const e={a:"a",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,o.R)(),...n.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(e.header,{children:(0,t.jsx)(e.h1,{id:"monitoring-apis",children:"Monitoring APIs"})}),"\n",(0,t.jsx)(e.p,{children:"This section contains documentation for APIs related to monitoring and alerting in the MLOps platform. These APIs provide comprehensive functionality for monitoring model performance, system health, and data quality."}),"\n",(0,t.jsx)(e.h2,{id:"model-monitoring",children:"Model Monitoring"}),"\n",(0,t.jsx)(e.p,{children:"APIs for monitoring model performance and behavior."}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:[(0,t.jsx)(e.a,{href:"/docs/ai-architecture/tools/old/api/monitoring/monitoring-api",children:"Monitoring API"})," - Endpoints for monitoring model performance and system health","\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Model performance metrics"}),"\n",(0,t.jsx)(e.li,{children:"Prediction monitoring"}),"\n",(0,t.jsx)(e.li,{children:"Resource utilization"}),"\n",(0,t.jsx)(e.li,{children:"System health checks"}),"\n",(0,t.jsx)(e.li,{children:"Data drift detection"}),"\n",(0,t.jsx)(e.li,{children:"Model bias monitoring"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(e.h2,{id:"alerting",children:"Alerting"}),"\n",(0,t.jsx)(e.p,{children:"APIs for managing alerts and notifications."}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:[(0,t.jsx)(e.a,{href:"/docs/ai-architecture/tools/old/api/monitoring/alerting-api",children:"Alerting API"})," - Endpoints for managing alerts and notifications","\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Alert rule management"}),"\n",(0,t.jsx)(e.li,{children:"Notification channels"}),"\n",(0,t.jsx)(e.li,{children:"Alert history"}),"\n",(0,t.jsx)(e.li,{children:"Alert aggregation"}),"\n",(0,t.jsx)(e.li,{children:"Incident management"}),"\n",(0,t.jsx)(e.li,{children:"Alert routing"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(e.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,t.jsxs)(e.ol,{children:["\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:(0,t.jsx)(e.strong,{children:"Monitoring Setup"})}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Define key metrics"}),"\n",(0,t.jsx)(e.li,{children:"Set appropriate thresholds"}),"\n",(0,t.jsx)(e.li,{children:"Configure monitoring frequency"}),"\n",(0,t.jsx)(e.li,{children:"Implement proper logging"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:(0,t.jsx)(e.strong,{children:"Alert Management"})}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Create meaningful alert rules"}),"\n",(0,t.jsx)(e.li,{children:"Configure notification channels"}),"\n",(0,t.jsx)(e.li,{children:"Set up alert routing"}),"\n",(0,t.jsx)(e.li,{children:"Implement alert aggregation"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(e.li,{children:["\n",(0,t.jsx)(e.p,{children:(0,t.jsx)(e.strong,{children:"Performance Optimization"})}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsx)(e.li,{children:"Monitor resource usage"}),"\n",(0,t.jsx)(e.li,{children:"Track latency metrics"}),"\n",(0,t.jsx)(e.li,{children:"Optimize monitoring overhead"}),"\n",(0,t.jsx)(e.li,{children:"Scale monitoring systems"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(e.h2,{id:"related-resources",children:"Related Resources"}),"\n",(0,t.jsxs)(e.ul,{children:["\n",(0,t.jsxs)(e.li,{children:[(0,t.jsx)(e.a,{href:"/docs/ai-architecture/tools/old/api/model-management/",children:"Model Management"})," - Manage model deployments"]}),"\n",(0,t.jsxs)(e.li,{children:[(0,t.jsx)(e.a,{href:"/docs/ai-architecture/tools/old/api/data-management/",children:"Data Management"})," - Monitor data quality"]}),"\n",(0,t.jsxs)(e.li,{children:[(0,t.jsx)(e.a,{href:"/docs/ai-architecture/tools/old/api/orchestration/",children:"Orchestration"})," - Monitor pipeline health"]}),"\n"]})]})}function h(n={}){const{wrapper:e}={...(0,o.R)(),...n.components};return e?(0,t.jsx)(e,{...n,children:(0,t.jsx)(d,{...n})}):d(n)}}}]);