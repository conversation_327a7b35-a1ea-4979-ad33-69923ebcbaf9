"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[9134],{4852:(e,t,r)=>{r.r(t),r.d(t,{assets:()=>a,contentTitle:()=>o,default:()=>h,frontMatter:()=>i,metadata:()=>c,toc:()=>d});const c=JSON.parse('{"id":"adrs/global/record-architecture-decisions","title":"1. Record architecture decisions","description":"Date: 2025-01-23","source":"@site/docs/adrs/global/0001-record-architecture-decisions.md","sourceDirName":"adrs/global","slug":"/adrs/global/record-architecture-decisions","permalink":"/docs/adrs/global/record-architecture-decisions","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/global/0001-record-architecture-decisions.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"Global","permalink":"/docs/category/global"},"next":{"title":"2. Monorepo per Project","permalink":"/docs/adrs/global/monorepo"}}');var s=r(4848),n=r(8453);const i={},o="1. Record architecture decisions",a={},d=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2}];function l(e){const t={a:"a",h1:"h1",h2:"h2",header:"header",p:"p",...(0,n.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.header,{children:(0,s.jsx)(t.h1,{id:"1-record-architecture-decisions",children:"1. Record architecture decisions"})}),"\n",(0,s.jsx)(t.p,{children:"Date: 2025-01-23"}),"\n",(0,s.jsx)(t.h2,{id:"status",children:"Status"}),"\n",(0,s.jsx)(t.p,{children:"Accepted"}),"\n",(0,s.jsx)(t.h2,{id:"context",children:"Context"}),"\n",(0,s.jsx)(t.p,{children:"We need to record the architectural decisions made on this project."}),"\n",(0,s.jsx)(t.h2,{id:"decision",children:"Decision"}),"\n",(0,s.jsxs)(t.p,{children:["We will use Architecture Decision Records, as ",(0,s.jsx)(t.a,{href:"http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions",children:"described by Michael Nygard"}),"."]}),"\n",(0,s.jsx)(t.h2,{id:"consequences",children:"Consequences"}),"\n",(0,s.jsxs)(t.p,{children:["See Michael Nygard's article, linked above. For a lightweight ADR toolset, see Nat Pryce's ",(0,s.jsx)(t.a,{href:"https://github.com/npryce/adr-tools",children:"adr-tools"}),"."]})]})}function h(e={}){const{wrapper:t}={...(0,n.R)(),...e.components};return t?(0,s.jsx)(t,{...e,children:(0,s.jsx)(l,{...e})}):l(e)}},8453:(e,t,r)=>{r.d(t,{R:()=>i,x:()=>o});var c=r(6540);const s={},n=c.createContext(s);function i(e){const t=c.useContext(n);return c.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function o(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:i(e.components),c.createElement(n.Provider,{value:t},e.children)}}}]);