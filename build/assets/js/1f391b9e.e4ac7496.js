"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6061],{215:(e,n,t)=>{t.d(n,{A:()=>v});var i=t(6540),a=t(8542);function s(e){const n=e.map((e=>({...e,parentIndex:-1,children:[]}))),t=Array(7).fill(-1);n.forEach(((e,n)=>{const i=t.slice(2,e.level);e.parentIndex=Math.max(...i),t[e.level]=n}));const i=[];return n.forEach((e=>{const{parentIndex:t,...a}=e;t>=0?n[t].children.push(a):i.push(a)})),i}function r({toc:e,minHeadingLevel:n,maxHeadingLevel:t}){return e.flatMap((e=>{const i=r({toc:e.children,minHeadingLevel:n,maxHeadingLevel:t});return function(e){return e.level>=n&&e.level<=t}(e)?[{...e,children:i}]:i}))}function l(e){const n=e.getBoundingClientRect();return n.top===n.bottom?l(e.parentNode):n}function c(e,{anchorTopOffset:n}){const t=e.find((e=>l(e).top>=n));if(t){return function(e){return e.top>0&&e.bottom<window.innerHeight/2}(l(t))?t:e[e.indexOf(t)-1]??null}return e[e.length-1]??null}function o(){const e=(0,i.useRef)(0),{navbar:{hideOnScroll:n}}=(0,a.p)();return(0,i.useEffect)((()=>{e.current=n?0:document.querySelector(".navbar").clientHeight}),[n]),e}function d(e){const n=(0,i.useRef)(void 0),t=o();(0,i.useEffect)((()=>{if(!e)return()=>{};const{linkClassName:i,linkActiveClassName:a,minHeadingLevel:s,maxHeadingLevel:r}=e;function l(){const e=function(e){return Array.from(document.getElementsByClassName(e))}(i),l=function({minHeadingLevel:e,maxHeadingLevel:n}){const t=[];for(let i=e;i<=n;i+=1)t.push(`h${i}.anchor`);return Array.from(document.querySelectorAll(t.join()))}({minHeadingLevel:s,maxHeadingLevel:r}),o=c(l,{anchorTopOffset:t.current}),d=e.find((e=>o&&o.id===function(e){return decodeURIComponent(e.href.substring(e.href.indexOf("#")+1))}(e)));e.forEach((e=>{!function(e,t){t?(n.current&&n.current!==e&&n.current.classList.remove(a),e.classList.add(a),n.current=e):e.classList.remove(a)}(e,e===d)}))}return document.addEventListener("scroll",l),document.addEventListener("resize",l),l(),()=>{document.removeEventListener("scroll",l),document.removeEventListener("resize",l)}}),[e,t])}var m=t(6289),u=t(4848);function f({toc:e,className:n,linkClassName:t,isChild:i}){return e.length?(0,u.jsx)("ul",{className:i?void 0:n,children:e.map((e=>(0,u.jsxs)("li",{children:[(0,u.jsx)(m.A,{to:`#${e.id}`,className:t??void 0,dangerouslySetInnerHTML:{__html:e.value}}),(0,u.jsx)(f,{isChild:!0,toc:e.children,className:n,linkClassName:t})]},e.id)))}):null}const h=i.memo(f);function v({toc:e,className:n="table-of-contents table-of-contents__left-border",linkClassName:t="table-of-contents__link",linkActiveClassName:l,minHeadingLevel:c,maxHeadingLevel:o,...m}){const f=(0,a.p)(),v=c??f.tableOfContents.minHeadingLevel,x=o??f.tableOfContents.maxHeadingLevel,g=function({toc:e,minHeadingLevel:n,maxHeadingLevel:t}){return(0,i.useMemo)((()=>r({toc:s(e),minHeadingLevel:n,maxHeadingLevel:t})),[e,n,t])}({toc:e,minHeadingLevel:v,maxHeadingLevel:x});return d((0,i.useMemo)((()=>{if(t&&l)return{linkClassName:t,linkActiveClassName:l,minHeadingLevel:v,maxHeadingLevel:x}}),[t,l,v,x])),(0,u.jsx)(h,{toc:g,className:n,linkClassName:t,...m})}},665:(e,n,t)=>{t.d(n,{A:()=>o});t(6540);var i=t(4164),a=t(3452),s=t(5279),r=t(7821),l=t(4848);function c({className:e}){return(0,l.jsx)(r.A,{type:"caution",title:(0,l.jsx)(a.Rc,{}),className:(0,i.A)(e,s.G.common.unlistedBanner),children:(0,l.jsx)(a.Uh,{})})}function o(e){return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(a.AE,{}),(0,l.jsx)(c,{...e})]})}},2075:(e,n,t)=>{t.d(n,{A:()=>d});t(6540);var i=t(4164),a=t(3452),s=t(5279),r=t(7821),l=t(4848);function c({className:e}){return(0,l.jsx)(r.A,{type:"caution",title:(0,l.jsx)(a.Yh,{}),className:(0,i.A)(e,s.G.common.draftBanner),children:(0,l.jsx)(a.TT,{})})}var o=t(665);function d({metadata:e}){const{unlisted:n,frontMatter:t}=e;return(0,l.jsxs)(l.Fragment,{children:[(n||t.unlisted)&&(0,l.jsx)(o.A,{}),t.draft&&(0,l.jsx)(c,{})]})}},3452:(e,n,t)=>{t.d(n,{AE:()=>c,Rc:()=>r,TT:()=>d,Uh:()=>l,Yh:()=>o});t(6540);var i=t(539),a=t(7143),s=t(4848);function r(){return(0,s.jsx)(i.A,{id:"theme.contentVisibility.unlistedBanner.title",description:"The unlisted content banner title",children:"Unlisted page"})}function l(){return(0,s.jsx)(i.A,{id:"theme.contentVisibility.unlistedBanner.message",description:"The unlisted content banner message",children:"This page is unlisted. Search engines will not index it, and only users having a direct link can access it."})}function c(){return(0,s.jsx)(a.A,{children:(0,s.jsx)("meta",{name:"robots",content:"noindex, nofollow"})})}function o(){return(0,s.jsx)(i.A,{id:"theme.contentVisibility.draftBanner.title",description:"The draft content banner title",children:"Draft page"})}function d(){return(0,s.jsx)(i.A,{id:"theme.contentVisibility.draftBanner.message",description:"The draft content banner message",children:"This page is a draft. It will only be visible in dev and be excluded from the production build."})}},7959:(e,n,t)=>{t.d(n,{A:()=>o});t(6540);var i=t(4164),a=t(215);const s={tableOfContents:"tableOfContents_bqdL",docItemContainer:"docItemContainer_F8PC"};var r=t(4848);const l="table-of-contents__link toc-highlight",c="table-of-contents__link--active";function o({className:e,...n}){return(0,r.jsx)("div",{className:(0,i.A)(s.tableOfContents,"thin-scrollbar",e),children:(0,r.jsx)(a.A,{...n,linkClassName:l,linkActiveClassName:c})})}},9181:(e,n,t)=>{t.r(n),t.d(n,{default:()=>f});t(6540);var i=t(4164),a=t(4737),s=t(5279),r=t(9562),l=t(4809),c=t(7959),o=t(2075),d=t(5783);const m={mdxPageWrapper:"mdxPageWrapper_j9I6"};var u=t(4848);function f(e){const{content:n}=e,{metadata:t,assets:f}=n,{title:h,editUrl:v,description:x,frontMatter:g,lastUpdatedBy:p,lastUpdatedAt:A}=t,{keywords:j,wrapperClassName:b,hide_table_of_contents:L}=g,N=f.image??g.image,C=!!(v||A||p);return(0,u.jsx)(a.e3,{className:(0,i.A)(b??s.G.wrapper.mdxPages,s.G.page.mdxPage),children:(0,u.jsxs)(r.A,{children:[(0,u.jsx)(a.be,{title:h,description:x,keywords:j,image:N}),(0,u.jsx)("main",{className:"container container--fluid margin-vert--lg",children:(0,u.jsxs)("div",{className:(0,i.A)("row",m.mdxPageWrapper),children:[(0,u.jsxs)("div",{className:(0,i.A)("col",!L&&"col--8"),children:[(0,u.jsx)(o.A,{metadata:t}),(0,u.jsx)("article",{children:(0,u.jsx)(l.A,{children:(0,u.jsx)(n,{})})}),C&&(0,u.jsx)(d.A,{className:(0,i.A)("margin-top--sm",s.G.pages.pageFooterEditMetaRow),editUrl:v,lastUpdatedAt:A,lastUpdatedBy:p})]}),!L&&n.toc.length>0&&(0,u.jsx)("div",{className:"col col--2",children:(0,u.jsx)(c.A,{toc:n.toc,minHeadingLevel:g.toc_min_heading_level,maxHeadingLevel:g.toc_max_heading_level})})]})})]})})}}}]);