"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8947],{6600:e=>{e.exports=JSON.parse('{"authors":[{"name":"Hortense","title":"AI Head of 91.life","url":"https://91.life","imageURL":"https://github.com/<EMAIL>","email":"<EMAIL>","twitter":null,"github":"https://github.com/<EMAIL>","key":"hortense","page":null,"count":0},{"name":"<PERSON>","title":"CTO of 91.life","url":"https://91.life","imageURL":"https://github.com/jean91life.png","email":"<EMAIL>","twitter":null,"github":"https://github.com/jean91life","key":"jean","page":null,"count":0},{"name":"<PERSON><PERSON> Cikaj","title":"AI Engineer","url":"https://91.life","imageURL":"https://github.com/albincikaj91life.png","email":"albin <EMAIL>","twitter":null,"github":"https://github.com/albincikaj91life","key":"albincikaj","page":null,"count":0},{"name":"Alban Maxhuni, PhD","title":"AI Engineer","url":"https://91.life","imageURL":"https://github.com/alban91life.png","email":"<EMAIL>","twitter":null,"github":"https://github.com/alban91life","key":"alban","page":null,"count":0}]}')}}]);