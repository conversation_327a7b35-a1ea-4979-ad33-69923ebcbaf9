"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7332],{921:(e,i,n)=>{n.r(i),n.d(i,{assets:()=>c,contentTitle:()=>l,default:()=>h,frontMatter:()=>a,metadata:()=>t,toc:()=>o});const t=JSON.parse('{"id":"ai-architecture/api/auth/authentication","title":"Authentication","description":"This guide explains how to authenticate with the AI Platform API.","source":"@site/docs/ai-architecture/api/auth/authentication.md","sourceDirName":"ai-architecture/api/auth","slug":"/ai-architecture/api/auth/authentication","permalink":"/docs/ai-architecture/api/auth/authentication","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/auth/authentication.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"API Overview","permalink":"/docs/ai-architecture/api/api-overview"},"next":{"title":"Authorization","permalink":"/docs/ai-architecture/api/auth/authorization"}}');var r=n(4848),s=n(8453);const a={sidebar_position:1},l="Authentication",c={},o=[{value:"API Keys",id:"api-keys",level:2},{value:"Creating an API Key",id:"creating-an-api-key",level:3},{value:"Using API Keys",id:"using-api-keys",level:2},{value:"Key Security",id:"key-security",level:2},{value:"Key Permissions",id:"key-permissions",level:2},{value:"Rate Limits",id:"rate-limits",level:2},{value:"Key Expiration",id:"key-expiration",level:2},{value:"Best Practices",id:"best-practices",level:2},{value:"Example: Python SDK",id:"example-python-sdk",level:2},{value:"Example: JavaScript SDK",id:"example-javascript-sdk",level:2},{value:"Troubleshooting",id:"troubleshooting",level:2}];function d(e){const i={a:"a",code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,s.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.header,{children:(0,r.jsx)(i.h1,{id:"authentication",children:"Authentication"})}),"\n",(0,r.jsx)(i.p,{children:"This guide explains how to authenticate with the AI Platform API."}),"\n",(0,r.jsx)(i.h2,{id:"api-keys",children:"API Keys"}),"\n",(0,r.jsx)(i.p,{children:"All API requests require an API key for authentication. You can create and manage your API keys in the dashboard."}),"\n",(0,r.jsx)(i.h3,{id:"creating-an-api-key",children:"Creating an API Key"}),"\n",(0,r.jsxs)(i.ol,{children:["\n",(0,r.jsx)(i.li,{children:"Log in to your dashboard"}),"\n",(0,r.jsx)(i.li,{children:"Navigate to Settings > API Keys"}),"\n",(0,r.jsx)(i.li,{children:'Click "Create New API Key"'}),"\n",(0,r.jsx)(i.li,{children:"Give your key a descriptive name"}),"\n",(0,r.jsx)(i.li,{children:"Select the appropriate permissions"}),"\n",(0,r.jsx)(i.li,{children:"Copy the key immediately (it won't be shown again)"}),"\n"]}),"\n",(0,r.jsx)(i.h2,{id:"using-api-keys",children:"Using API Keys"}),"\n",(0,r.jsxs)(i.p,{children:["Include your API key in the ",(0,r.jsx)(i.code,{children:"Authorization"})," header of all requests:"]}),"\n",(0,r.jsx)(i.pre,{children:(0,r.jsx)(i.code,{className:"language-bash",children:'curl -H "Authorization: Bearer YOUR_API_KEY" \\\n     https://api.ai-platform.example.com/v1/models\n'})}),"\n",(0,r.jsx)(i.h2,{id:"key-security",children:"Key Security"}),"\n",(0,r.jsxs)(i.ul,{children:["\n",(0,r.jsx)(i.li,{children:"Never share your API keys"}),"\n",(0,r.jsx)(i.li,{children:"Rotate keys regularly"}),"\n",(0,r.jsx)(i.li,{children:"Use different keys for different environments"}),"\n",(0,r.jsx)(i.li,{children:"Set appropriate permissions for each key"}),"\n"]}),"\n",(0,r.jsx)(i.h2,{id:"key-permissions",children:"Key Permissions"}),"\n",(0,r.jsx)(i.p,{children:"API keys can have the following permissions:"}),"\n",(0,r.jsxs)(i.ul,{children:["\n",(0,r.jsxs)(i.li,{children:[(0,r.jsx)(i.code,{children:"read"}),": Read-only access"]}),"\n",(0,r.jsxs)(i.li,{children:[(0,r.jsx)(i.code,{children:"write"}),": Read and write access"]}),"\n",(0,r.jsxs)(i.li,{children:[(0,r.jsx)(i.code,{children:"admin"}),": Full administrative access"]}),"\n"]}),"\n",(0,r.jsx)(i.h2,{id:"rate-limits",children:"Rate Limits"}),"\n",(0,r.jsx)(i.p,{children:"API keys are subject to rate limits based on your plan:"}),"\n",(0,r.jsxs)(i.table,{children:[(0,r.jsx)(i.thead,{children:(0,r.jsxs)(i.tr,{children:[(0,r.jsx)(i.th,{children:"Plan"}),(0,r.jsx)(i.th,{children:"Requests per minute"})]})}),(0,r.jsxs)(i.tbody,{children:[(0,r.jsxs)(i.tr,{children:[(0,r.jsx)(i.td,{children:"Standard"}),(0,r.jsx)(i.td,{children:"100"})]}),(0,r.jsxs)(i.tr,{children:[(0,r.jsx)(i.td,{children:"Pro"}),(0,r.jsx)(i.td,{children:"500"})]}),(0,r.jsxs)(i.tr,{children:[(0,r.jsx)(i.td,{children:"Enterprise"}),(0,r.jsx)(i.td,{children:"1000"})]})]})]}),"\n",(0,r.jsx)(i.h2,{id:"key-expiration",children:"Key Expiration"}),"\n",(0,r.jsx)(i.p,{children:"API keys can be set to expire after a certain period. You can configure this in the dashboard."}),"\n",(0,r.jsx)(i.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,r.jsxs)(i.ol,{children:["\n",(0,r.jsx)(i.li,{children:"Use environment variables to store API keys"}),"\n",(0,r.jsx)(i.li,{children:"Implement key rotation"}),"\n",(0,r.jsx)(i.li,{children:"Monitor key usage"}),"\n",(0,r.jsx)(i.li,{children:"Set up alerts for suspicious activity"}),"\n"]}),"\n",(0,r.jsx)(i.h2,{id:"example-python-sdk",children:"Example: Python SDK"}),"\n",(0,r.jsx)(i.pre,{children:(0,r.jsx)(i.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Make authenticated requests\nmodels = client.models.list()\n'})}),"\n",(0,r.jsx)(i.h2,{id:"example-javascript-sdk",children:"Example: JavaScript SDK"}),"\n",(0,r.jsx)(i.pre,{children:(0,r.jsx)(i.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Make authenticated requests\nconst models = await client.models.list();\n"})}),"\n",(0,r.jsx)(i.h2,{id:"troubleshooting",children:"Troubleshooting"}),"\n",(0,r.jsx)(i.p,{children:"Common authentication issues:"}),"\n",(0,r.jsxs)(i.ol,{children:["\n",(0,r.jsx)(i.li,{children:"Invalid API key"}),"\n",(0,r.jsx)(i.li,{children:"Expired API key"}),"\n",(0,r.jsx)(i.li,{children:"Insufficient permissions"}),"\n",(0,r.jsx)(i.li,{children:"Rate limit exceeded"}),"\n"]}),"\n",(0,r.jsxs)(i.p,{children:["If you encounter any issues, check the ",(0,r.jsx)(i.a,{href:"/docs/ai-architecture/api/api-overview#error-handling",children:"Error Handling"})," section for more information."]})]})}function h(e={}){const{wrapper:i}={...(0,s.R)(),...e.components};return i?(0,r.jsx)(i,{...e,children:(0,r.jsx)(d,{...e})}):d(e)}},8453:(e,i,n)=>{n.d(i,{R:()=>a,x:()=>l});var t=n(6540);const r={},s=t.createContext(r);function a(e){const i=t.useContext(s);return t.useMemo((function(){return"function"==typeof e?e(i):{...i,...e}}),[i,e])}function l(e){let i;return i=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:a(e.components),t.createElement(s.Provider,{value:i},e.children)}}}]);