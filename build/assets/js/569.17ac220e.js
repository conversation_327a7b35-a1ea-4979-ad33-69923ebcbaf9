"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[569],{569:(e,t,n)=>{n.d(t,{A:()=>U});var s=n(6540),a=n(4164),i=n(9562),r=n(1357),o=n(539),l=n(9117),c=n(8542),m=n(9303),u=n(4848);function d({year:e,yearGroupHeadingClassName:t,children:n}){return(0,u.jsxs)("div",{role:"group",children:[(0,u.jsx)(m.A,{as:"h3",className:t,children:e}),n]})}function g({items:e,yearGroupHeadingClassName:t,ListComponent:n}){if((0,c.p)().blog.sidebar.groupByYear){const s=(0,l.<PERSON>)(e);return(0,u.jsx)(u.Fragment,{children:s.map((([e,s])=>(0,u.jsx)(d,{year:e,yearGroupHeadingClassName:t,children:(0,u.jsx)(n,{items:s})},e)))})}return(0,u.jsx)(n,{items:e})}const h=(0,s.memo)(g),p="sidebar_re4s",f="sidebarItemTitle_pO2u",b="sidebarItemList_Yudw",x="sidebarItem__DBe",N="sidebarItemLink_mo7H",C="sidebarItemLinkActive_I1ZP",j="yearGroupHeading_rMGB",k=({items:e})=>(0,u.jsx)(l.OU,{items:e,ulClassName:(0,a.A)(b,"clean-list"),liClassName:x,linkClassName:N,linkActiveClassName:C});function v({sidebar:e}){const t=(0,l.Gx)(e.items);return(0,u.jsx)("aside",{className:"col col--3",children:(0,u.jsxs)("nav",{className:(0,a.A)(p,"thin-scrollbar"),"aria-label":(0,o.T)({id:"theme.blog.sidebar.navAriaLabel",message:"Blog recent posts navigation",description:"The ARIA label for recent posts in the blog sidebar"}),children:[(0,u.jsx)("div",{className:(0,a.A)(f,"margin-bottom--md"),children:e.title}),(0,u.jsx)(h,{items:t,ListComponent:k,yearGroupHeadingClassName:j})]})})}const y=(0,s.memo)(v);var A=n(8152);const P="yearGroupHeading_QT03",_=({items:e})=>(0,u.jsx)(l.OU,{items:e,ulClassName:"menu__list",liClassName:"menu__list-item",linkClassName:"menu__link",linkActiveClassName:"menu__link--active"});function w({sidebar:e}){const t=(0,l.Gx)(e.items);return(0,u.jsx)(h,{items:t,ListComponent:_,yearGroupHeadingClassName:P})}function B(e){return(0,u.jsx)(A.GX,{component:w,props:e})}const $=(0,s.memo)(B);function G({sidebar:e}){const t=(0,r.l)();return e?.items.length?"mobile"===t?(0,u.jsx)($,{sidebar:e}):(0,u.jsx)(y,{sidebar:e}):null}function U(e){const{sidebar:t,toc:n,children:s,...r}=e,o=t&&t.items.length>0;return(0,u.jsx)(i.A,{...r,children:(0,u.jsx)("div",{className:"container margin-vert--lg",children:(0,u.jsxs)("div",{className:"row",children:[(0,u.jsx)(G,{sidebar:t}),(0,u.jsx)("main",{className:(0,a.A)("col",{"col--7":o,"col--9 col--offset-1":!o}),children:s}),n&&(0,u.jsx)("div",{className:"col col--2",children:n})]})})})}},9117:(e,t,n)=>{n.d(t,{in:()=>c,OU:()=>y,Ki:()=>v,kJ:()=>f,x:()=>o,e7:()=>m,J_:()=>b,Gx:()=>k});var s=n(6540),a=(n(372),n(4848));class i extends Error{constructor(e,t){super(),this.name="ReactContextError",this.message=`Hook ${this.stack?.split("\n")[1]?.match(/at (?:\w+\.)?(?<name>\w+)/)?.groups.name??""} is called outside the <${e}>. ${t??""}`}}var r=n(9062);function o(){const e=(0,r.A)(),t=e?.data?.blogMetadata;if(!t)throw new Error("useBlogMetadata() can't be called on the current route because the blog metadata could not be found in route context");return t}const l=s.createContext(null);function c({children:e,content:t,isBlogPostPage:n=!1}){const i=function({content:e,isBlogPostPage:t}){return(0,s.useMemo)((()=>({metadata:e.metadata,frontMatter:e.frontMatter,assets:e.assets,toc:e.toc,isBlogPostPage:t})),[e,t])}({content:t,isBlogPostPage:n});return(0,a.jsx)(l.Provider,{value:i,children:e})}function m(){const e=(0,s.useContext)(l);if(null===e)throw new i("BlogPostProvider");return e}var u=n(9030),d=n(797);const g=e=>new Date(e).toISOString();function h(e){const t=e.map(x);return{author:1===t.length?t[0]:t}}function p(e,t,n){return e?{image:N({imageUrl:t(e,{absolute:!0}),caption:`title image for the blog post: ${n}`})}:{}}function f(e){const{siteConfig:t}=(0,d.A)(),{withBaseUrl:n}=(0,u.hH)(),{metadata:{blogDescription:s,blogTitle:a,permalink:i}}=e,r=`${t.url}${i}`;return{"@context":"https://schema.org","@type":"Blog","@id":r,mainEntityOfPage:r,headline:a,description:s,blogPost:e.items.map((e=>function(e,t,n){const{assets:s,frontMatter:a,metadata:i}=e,{date:r,title:o,description:l,lastUpdatedAt:c}=i,m=s.image??a.image,u=a.keywords??[],d=`${t.url}${i.permalink}`,f=c?g(c):void 0;return{"@type":"BlogPosting","@id":d,mainEntityOfPage:d,url:d,headline:o,name:o,description:l,datePublished:r,...f?{dateModified:f}:{},...h(i.authors),...p(m,n,o),...u?{keywords:u}:{}}}(e.content,t,n)))}}function b(){const e=o(),{assets:t,metadata:n}=m(),{siteConfig:s}=(0,d.A)(),{withBaseUrl:a}=(0,u.hH)(),{date:i,title:r,description:l,frontMatter:c,lastUpdatedAt:f}=n,b=t.image??c.image,x=c.keywords??[],N=f?g(f):void 0,C=`${s.url}${n.permalink}`;return{"@context":"https://schema.org","@type":"BlogPosting","@id":C,mainEntityOfPage:C,url:C,headline:r,name:r,description:l,datePublished:i,...N?{dateModified:N}:{},...h(n.authors),...p(b,a,r),...x?{keywords:x}:{},isPartOf:{"@type":"Blog","@id":`${s.url}${e.blogBasePath}`,name:e.blogTitle}}}function x(e){return{"@type":"Person",...e.name?{name:e.name}:{},...e.title?{description:e.title}:{},...e.url?{url:e.url}:{},...e.email?{email:e.email}:{},...e.imageURL?{image:e.imageURL}:{}}}function N({imageUrl:e,caption:t}){return{"@type":"ImageObject","@id":e,url:e,contentUrl:e,caption:t}}var C=n(6347),j=n(6289);n(8912);function k(e){const{pathname:t}=(0,C.zy)();return(0,s.useMemo)((()=>e.filter((e=>function(e,t){return!(e.unlisted&&!function(e,t){const n=e=>(!e||e.endsWith("/")?e:`${e}/`)?.toLowerCase();return n(e)===n(t)}(e.permalink,t))}(e,t)))),[e,t])}function v(e){const t=function(e,t){const n={};let s=0;for(const a of e){const e=t(a,s);n[e]??=[],n[e].push(a),s+=1}return n}(e,(e=>`${new Date(e.date).getFullYear()}`)),n=Object.entries(t);return n.reverse(),n}function y({items:e,ulClassName:t,liClassName:n,linkClassName:s,linkActiveClassName:i}){return(0,a.jsx)("ul",{className:t,children:e.map((e=>(0,a.jsx)("li",{className:n,children:(0,a.jsx)(j.A,{isNavLink:!0,to:e.permalink,className:s,activeClassName:i,children:e.title})},e.permalink)))})}}}]);