"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8745],{927:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>a,contentTitle:()=>d,default:()=>h,frontMatter:()=>l,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/tools/old/api/model-management/model-serving-api","title":"Model Serving API Documentation","description":"The Model Serving API provides endpoints for serving machine learning models, managing inference requests, and monitoring model performance. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/model-management/model-serving-api.md","sourceDirName":"ai-architecture/tools/old/api/model-management","slug":"/ai-architecture/tools/old/api/model-management/model-serving-api","permalink":"/docs/ai-architecture/tools/old/api/model-management/model-serving-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/model-management/model-serving-api.md","tags":[],"version":"current","frontMatter":{"id":"model-serving-api","title":"Model Serving API Documentation","sidebar_label":"Model Serving API"},"sidebar":"tutorialSidebar","previous":{"title":"Model Registry API","permalink":"/docs/ai-architecture/tools/old/api/model-management/model-registry-api"},"next":{"title":"Monitoring","permalink":"/docs/ai-architecture/tools/old/api/monitoring/"}}');var t=i(4848),s=i(8453);const l={id:"model-serving-api",title:"Model Serving API Documentation",sidebar_label:"Model Serving API"},d="Model Serving API Documentation",a={},c=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Model Inference",id:"model-inference",level:3},{value:"Batch Inference",id:"batch-inference",level:4},{value:"Real-time Inference",id:"real-time-inference",level:4},{value:"Model Status",id:"model-status",level:3},{value:"Get Model Status",id:"get-model-status",level:4},{value:"Get Model Health",id:"get-model-health",level:4},{value:"Model Configuration",id:"model-configuration",level:3},{value:"Update Model Configuration",id:"update-model-configuration",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,s.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"model-serving-api-documentation",children:"Model Serving API Documentation"})}),"\n",(0,t.jsx)(n.p,{children:"The Model Serving API provides endpoints for serving machine learning models, managing inference requests, and monitoring model performance. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,t.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"https://model-serving.91.life/api/v1\n"})}),"\n",(0,t.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,t.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,t.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,t.jsx)(n.h3,{id:"model-inference",children:"Model Inference"}),"\n",(0,t.jsx)(n.h4,{id:"batch-inference",children:"Batch Inference"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /models/{model_id}/batch\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "instances": [\n        {\n            "customer_id": "123",\n            "age": 35,\n            "income": 75000,\n            "tenure": 24,\n            "usage_frequency": 0.8\n        },\n        {\n            "customer_id": "124",\n            "age": 42,\n            "income": 85000,\n            "tenure": 36,\n            "usage_frequency": 0.6\n        }\n    ],\n    "parameters": {\n        "timeout": 30,\n        "batch_size": 32\n    }\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "predictions": [\n        {\n            "customer_id": "123",\n            "churn_probability": 0.15,\n            "prediction": "no_churn",\n            "confidence": 0.85\n        },\n        {\n            "customer_id": "124",\n            "churn_probability": 0.75,\n            "prediction": "churn",\n            "confidence": 0.75\n        }\n    ],\n    "model_id": "model_001",\n    "model_version": "1.0.0",\n    "inference_time_ms": 150\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"real-time-inference",children:"Real-time Inference"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"POST /models/{model_id}/predict\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "instance": {\n        "customer_id": "123",\n        "age": 35,\n        "income": 75000,\n        "tenure": 24,\n        "usage_frequency": 0.8\n    },\n    "parameters": {\n        "timeout": 5\n    }\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "prediction": {\n        "customer_id": "123",\n        "churn_probability": 0.15,\n        "prediction": "no_churn",\n        "confidence": 0.85\n    },\n    "model_id": "model_001",\n    "model_version": "1.0.0",\n    "inference_time_ms": 50\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"model-status",children:"Model Status"}),"\n",(0,t.jsx)(n.h4,{id:"get-model-status",children:"Get Model Status"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"GET /models/{model_id}/status\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "model_id": "model_001",\n    "status": "serving",\n    "version": "1.0.0",\n    "endpoint": "https://api.91.life/v1/predict",\n    "metrics": {\n        "requests_per_second": 100,\n        "latency_ms": {\n            "p50": 50,\n            "p90": 100,\n            "p99": 200\n        },\n        "error_rate": 0.01,\n        "cpu_usage": 45.5,\n        "memory_usage": 60.2\n    },\n    "last_updated": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,t.jsx)(n.h4,{id:"get-model-health",children:"Get Model Health"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"GET /models/{model_id}/health\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "status": "healthy",\n    "checks": {\n        "model_loaded": true,\n        "endpoint_available": true,\n        "resources_available": true\n    },\n    "last_checked": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"model-configuration",children:"Model Configuration"}),"\n",(0,t.jsx)(n.h4,{id:"update-model-configuration",children:"Update Model Configuration"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-http",children:"PATCH /models/{model_id}/config\n"})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "batch_size": 64,\n    "timeout": 30,\n    "max_retries": 3,\n    "scaling": {\n        "min_replicas": 2,\n        "max_replicas": 10,\n        "target_cpu_utilization": 80\n    },\n    "resources": {\n        "cpu": "2",\n        "memory": "4Gi"\n    }\n}\n'})}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Response:"})}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n    "model_id": "model_001",\n    "status": "updating",\n    "config": {\n        "batch_size": 64,\n        "timeout": 30,\n        "max_retries": 3,\n        "scaling": {\n            "min_replicas": 2,\n            "max_replicas": 10,\n            "target_cpu_utilization": 80\n        },\n        "resources": {\n            "cpu": "2",\n            "memory": "4Gi"\n        }\n    },\n    "updated_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,t.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'from model_serving import Client\n\n# Initialize client\nclient = Client(\n    host="https://model-serving.91.life",\n    auth_token="your-token"\n)\n\n# Batch inference\npredictions = client.batch_predict(\n    model_id="model_001",\n    instances=[\n        {\n            "customer_id": "123",\n            "age": 35,\n            "income": 75000,\n            "tenure": 24,\n            "usage_frequency": 0.8\n        }\n    ]\n)\nprint(predictions)\n\n# Get model status\nstatus = client.get_model_status("model_001")\nprint(status)\n'})}),"\n",(0,t.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-bash",children:'# Batch inference\ncurl -X POST https://model-serving.91.life/api/v1/models/model_001/batch \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "instances": [\n      {\n        "customer_id": "123",\n        "age": 35,\n        "income": 75000,\n        "tenure": 24,\n        "usage_frequency": 0.8\n      }\n    ]\n  }\'\n\n# Get model status\ncurl -X GET https://model-serving.91.life/api/v1/models/model_001/status \\\n  -H "Authorization: Bearer ${TOKEN}"\n'})}),"\n",(0,t.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Code"}),(0,t.jsx)(n.th,{children:"Description"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"400"}),(0,t.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"401"}),(0,t.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"403"}),(0,t.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"404"}),(0,t.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"408"}),(0,t.jsx)(n.td,{children:"Request Timeout - Inference timeout"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"429"}),(0,t.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"500"}),(0,t.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"503"}),(0,t.jsx)(n.td,{children:"Service Unavailable - Model not ready"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,t.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,t.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Inference Requests"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Use batch inference for multiple predictions"}),"\n",(0,t.jsx)(n.li,{children:"Set appropriate timeouts"}),"\n",(0,t.jsx)(n.li,{children:"Handle errors gracefully"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Performance"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Monitor latency metrics"}),"\n",(0,t.jsx)(n.li,{children:"Configure appropriate resources"}),"\n",(0,t.jsx)(n.li,{children:"Use auto-scaling"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Monitoring"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Track model performance"}),"\n",(0,t.jsx)(n.li,{children:"Monitor resource usage"}),"\n",(0,t.jsx)(n.li,{children:"Set up alerts"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Security"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Validate input data"}),"\n",(0,t.jsx)(n.li,{children:"Rate limit requests"}),"\n",(0,t.jsx)(n.li,{children:"Secure endpoints"}),"\n"]}),"\n"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(o,{...e})}):o(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>d});var r=i(6540);const t={},s=r.createContext(t);function l(e){const n=r.useContext(s);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function d(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:l(e.components),r.createElement(s.Provider,{value:n},e.children)}}}]);