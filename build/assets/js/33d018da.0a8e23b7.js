"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2335],{4391:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>s,contentTitle:()=>c,default:()=>h,frontMatter:()=>l,metadata:()=>r,toc:()=>o});const r=JSON.parse('{"id":"architecture/Architecture Presentation","title":"Architecture Presentation","description":"Transforming Healthcare Through Adaptive Technology","source":"@site/docs/architecture/Architecture Presentation.md","sourceDirName":"architecture","slug":"/architecture/Architecture Presentation","permalink":"/docs/architecture/Architecture Presentation","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/architecture/Architecture Presentation.md","tags":[],"version":"current","frontMatter":{"marp":true,"theme":"default","paginate":true,"header":"91 Platform: Scalable, Agile, Secure","footer":"Confidential - 91.life","style":"section {\\n  background-color: #fff;\\n  color: #333;\\n  font-family: \'Arial\', sans-serif;\\n  padding: 30px 50px;\\n}\\nh1 {\\n  color: #0066cc;\\n  font-size: 2em;\\n  margin-bottom: 0.5em;\\n}\\nh2 {\\n  color: #004d99;\\n}\\nstrong {\\n  color: #0066cc;\\n}\\nul {\\n  margin-left: 1em;\\n  margin-right: 1em;\\n}\\n.grid {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 2em;\\n  align-items: center;\\n}\\n.triple {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr 1fr;\\n  gap: 1em;\\n}\\n.center-image {\\n  text-align: center;\\n  margin: 0 auto;\\n}\\n"},"sidebar":"tutorialSidebar","previous":{"title":"Architecture","permalink":"/docs/category/architecture"},"next":{"title":"Initial Architecture","permalink":"/docs/architecture/Initial Architecture"}}');var t=i(4848),a=i(8453);const l={marp:!0,theme:"default",paginate:!0,header:"91 Platform: Scalable, Agile, Secure",footer:"Confidential - 91.life",style:"section {\n  background-color: #fff;\n  color: #333;\n  font-family: 'Arial', sans-serif;\n  padding: 30px 50px;\n}\nh1 {\n  color: #0066cc;\n  font-size: 2em;\n  margin-bottom: 0.5em;\n}\nh2 {\n  color: #004d99;\n}\nstrong {\n  color: #0066cc;\n}\nul {\n  margin-left: 1em;\n  margin-right: 1em;\n}\n.grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2em;\n  align-items: center;\n}\n.triple {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 1em;\n}\n.center-image {\n  text-align: center;\n  margin: 0 auto;\n}\n"},c="Executive Summary",s={},o=[];function d(e){const n={h1:"h1",header:"header",hr:"hr",li:"li",p:"p",strong:"strong",ul:"ul",...(0,a.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"executive-summary",children:"Executive Summary"})}),"\n",(0,t.jsxs)("div",{children:[(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Transforming Healthcare Through Adaptive Technology"})}),(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Cloud-agnostic, AI-powered medical technology platform"}),"\n",(0,t.jsx)(n.li,{children:"Seamlessly integrates with existing hospital infrastructure"}),"\n",(0,t.jsx)(n.li,{children:"Built for compliance, scalability, and security from day one"}),"\n",(0,t.jsx)(n.li,{children:"Full FHIR compliance for healthcare interoperability"}),"\n",(0,t.jsx)(n.li,{children:"Natively built with and for the use of AI"}),"\n",(0,t.jsx)(n.li,{children:"Natively built for DLT and Secure Computing"}),"\n"]})]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsx)(n.h1,{id:"technical-architecture-overview",children:"Technical Architecture Overview"}),"\n",(0,t.jsxs)("div",{children:[(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Versatile Deployment with Maximum Flexibility"})}),(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Cloud-agnostic: AWS, Azure, GCP, or on-premise"}),"\n",(0,t.jsx)(n.li,{children:"Hybrid deployment model for sensitive healthcare environments"}),"\n",(0,t.jsx)(n.li,{children:"Dapr (Distributed Application Runtime) foundation enables portable microservices"}),"\n",(0,t.jsx)(n.li,{children:"Stateless design allows elastic scaling during peak demand periods"}),"\n",(0,t.jsx)(n.li,{children:"Zero vendor lock-in advantages for global healthcare systems"}),"\n"]})]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsx)(n.h1,{id:"infrastructure-as-code",children:"Infrastructure as Code"}),"\n",(0,t.jsxs)("div",{children:[(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Automated Deployment and Scaling"})}),(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Complete infrastructure defined and managed with Terraform"}),"\n",(0,t.jsx)(n.li,{children:"Repeatable, auditable deployments across environments"}),"\n",(0,t.jsx)(n.li,{children:"95% reduction in configuration errors versus manual deployment"}),"\n",(0,t.jsx)(n.li,{children:"Disaster recovery in minutes rather than days"}),"\n",(0,t.jsx)(n.li,{children:"Automated compliance-ready infrastructure with built-in audit trails"}),"\n"]})]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsx)(n.h1,{id:"technology-stack",children:"Technology Stack"}),"\n",(0,t.jsxs)("div",{children:[(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Engineered for Performance and Reliability"})}),(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Backend services built with Go for maximum efficiency"}),"\n",(0,t.jsx)(n.li,{children:"70% lower resource utilization compared to legacy systems"}),"\n",(0,t.jsx)(n.li,{children:"99.99% uptime across all deployments"}),"\n",(0,t.jsx)(n.li,{children:"Micro-Frontend React architecture for modular, customizable UI"}),"\n",(0,t.jsx)(n.li,{children:"Dynamic deployment allows feature rollouts without downtime"}),"\n"]})]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsx)(n.h1,{id:"fhir-based-data-architecture",children:"FHIR-Based Data Architecture"}),"\n",(0,t.jsxs)("div",{children:[(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Standards-Driven Healthcare Interoperability"})}),(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Complete implementation of FHIR standards for data and communications"}),"\n",(0,t.jsx)(n.li,{children:'Bitemporal data schemas capturing both "as-of date" and "date of knowledge"'}),"\n",(0,t.jsx)(n.li,{children:"OpenLineage integration for complete data lineage tracking"}),"\n",(0,t.jsx)(n.li,{children:"Enables temporal auditing, retrospective analysis, and regulatory compliance"}),"\n",(0,t.jsx)(n.li,{children:"Supports both R4 and R5 FHIR resources with backward compatibility"}),"\n"]})]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsx)(n.h1,{id:"access-control--governance",children:"Access Control & Governance"}),"\n",(0,t.jsxs)("div",{children:[(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Enterprise-Grade Security & Compliance"})}),(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Granular permission management using ODRL (Open Digital Rights Language)"}),"\n",(0,t.jsx)(n.li,{children:"Role-based, attribute-based, and context-aware access controls"}),"\n",(0,t.jsx)(n.li,{children:"Automated policy enforcement across all system components"}),"\n",(0,t.jsx)(n.li,{children:"Real-time compliance monitoring and violation alerts"}),"\n",(0,t.jsx)(n.li,{children:"Simplified audit processes for regulatory requirements"}),"\n",(0,t.jsx)(n.li,{children:"Zero-trust architecture with cryptographic verification"}),"\n"]})]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsx)(n.h1,{id:"ai-native-platform",children:"AI-Native Platform"}),"\n",(0,t.jsxs)("div",{children:[(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Intelligence Built Into Every Layer"})}),(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Continual learning system that improves with usage"}),"\n",(0,t.jsx)(n.li,{children:"NLP exploration for all system logs and system traces"}),"\n",(0,t.jsx)(n.li,{children:"NLP exploration for all application data"}),"\n",(0,t.jsx)(n.li,{children:"ML Platform built from the ground up for internal and external researchers"}),"\n",(0,t.jsx)(n.li,{children:"Native Data Anonymisation and Secure Computing (FHE/SGX)"}),"\n"]})]}),"\n",(0,t.jsx)(n.hr,{}),"\n",(0,t.jsx)(n.h1,{id:"data-lineage--traceability",children:"Data Lineage & Traceability"}),"\n",(0,t.jsxs)("div",{children:[(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Complete Visibility Across the Data Lifecycle"})}),(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"OpenLineage implementation for end-to-end data traceability"}),"\n",(0,t.jsx)(n.li,{children:"Complete change history with bitemporal data modeling"}),"\n",(0,t.jsx)(n.li,{children:"Immutable audit trail of all data transformations"}),"\n",(0,t.jsx)(n.li,{children:"Point-in-time recovery capabilities for any historical state"}),"\n",(0,t.jsx)(n.li,{children:"Critical for clinical research, regulatory compliance, and legal discovery"}),"\n",(0,t.jsx)(n.li,{children:'Enables "time travel" through patient data history'}),"\n"]})]})]})}function h(e={}){const{wrapper:n}={...(0,a.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(d,{...e})}):d(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>l,x:()=>c});var r=i(6540);const t={},a=r.createContext(t);function l(e){const n=r.useContext(a);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function c(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:l(e.components),r.createElement(a.Provider,{value:n},e.children)}}}]);