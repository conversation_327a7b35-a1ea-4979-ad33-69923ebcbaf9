"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6736],{8453:(e,r,n)=>{n.d(r,{R:()=>l,x:()=>d});var i=n(6540);const s={},t=i.createContext(s);function l(e){const r=i.useContext(t);return i.useMemo((function(){return"function"==typeof e?e(r):{...r,...e}}),[r,e])}function d(e){let r;return r=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:l(e.components),i.createElement(t.Provider,{value:r},e.children)}},9639:(e,r,n)=>{n.r(r),n.d(r,{assets:()=>c,contentTitle:()=>d,default:()=>h,frontMatter:()=>l,metadata:()=>i,toc:()=>o});const i=JSON.parse('{"id":"ai-architecture/system-requirements/index","title":"System Requirements","description":"This section outlines the hardware and software requirements for running the AI Platform.","source":"@site/docs/ai-architecture/system-requirements/index.md","sourceDirName":"ai-architecture/system-requirements","slug":"/ai-architecture/system-requirements/","permalink":"/docs/ai-architecture/system-requirements/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/system-requirements/index.md","tags":[],"version":"current","frontMatter":{"title":"System Requirements"},"sidebar":"tutorialSidebar","previous":{"title":"Data Protection","permalink":"/docs/ai-architecture/security/data-protection/"},"next":{"title":"tools","permalink":"/docs/ai-architecture/tools/"}}');var s=n(4848),t=n(8453);const l={title:"System Requirements"},d="System Requirements",c={},o=[{value:"Hardware Requirements",id:"hardware-requirements",level:2},{value:"Minimum Requirements",id:"minimum-requirements",level:3},{value:"Recommended Requirements",id:"recommended-requirements",level:3},{value:"Software Requirements",id:"software-requirements",level:2},{value:"Operating System",id:"operating-system",level:3},{value:"Required Software",id:"required-software",level:3},{value:"Database Requirements",id:"database-requirements",level:3},{value:"Development Environment",id:"development-environment",level:2},{value:"IDE Requirements",id:"ide-requirements",level:3},{value:"Browser Requirements",id:"browser-requirements",level:3},{value:"Cloud Requirements",id:"cloud-requirements",level:2},{value:"GCP",id:"gcp",level:3},{value:"Network Requirements",id:"network-requirements",level:2},{value:"Security Requirements",id:"security-requirements",level:2}];function u(e){const r={h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.header,{children:(0,s.jsx)(r.h1,{id:"system-requirements",children:"System Requirements"})}),"\n",(0,s.jsx)(r.p,{children:"This section outlines the hardware and software requirements for running the AI Platform."}),"\n",(0,s.jsx)(r.h2,{id:"hardware-requirements",children:"Hardware Requirements"}),"\n",(0,s.jsx)(r.h3,{id:"minimum-requirements",children:"Minimum Requirements"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"CPU"}),":"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"RAM"}),":"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Storage"}),":"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Network"}),":"]}),"\n"]}),"\n",(0,s.jsx)(r.h3,{id:"recommended-requirements",children:"Recommended Requirements"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"CPU"}),":"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"RAM"}),":"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Storage"}),":"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Network"}),":"]}),"\n"]}),"\n",(0,s.jsx)(r.h2,{id:"software-requirements",children:"Software Requirements"}),"\n",(0,s.jsx)(r.h3,{id:"operating-system",children:"Operating System"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsx)(r.li,{children:"Linux (Ubuntu 20.04 LTS or later)"}),"\n",(0,s.jsx)(r.li,{children:"macOS (10.15 or later)"}),"\n",(0,s.jsx)(r.li,{children:"Windows 10/11 (with WSL2)"}),"\n"]}),"\n",(0,s.jsx)(r.h3,{id:"required-software",children:"Required Software"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Docker"}),": 20.10 or later"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Kubernetes"}),": 1.20 or later"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Node.js"}),": 18.0 or later"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Python"}),": 3.8 or later"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"Git"}),": 2.30 or later"]}),"\n"]}),"\n",(0,s.jsx)(r.h3,{id:"database-requirements",children:"Database Requirements"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"PostgreSQL"}),": 13 or later"]}),"\n",(0,s.jsxs)(r.li,{children:[(0,s.jsx)(r.strong,{children:"MongoDB"}),": 4.4 or later"]}),"\n"]}),"\n",(0,s.jsx)(r.h2,{id:"development-environment",children:"Development Environment"}),"\n",(0,s.jsx)(r.h3,{id:"ide-requirements",children:"IDE Requirements"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsx)(r.li,{children:"VS Code (recommended)"}),"\n",(0,s.jsx)(r.li,{children:"PyCharm"}),"\n",(0,s.jsx)(r.li,{children:"WebStorm"}),"\n"]}),"\n",(0,s.jsx)(r.h3,{id:"browser-requirements",children:"Browser Requirements"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsx)(r.li,{children:"Chrome 90+"}),"\n",(0,s.jsx)(r.li,{children:"Firefox 90+"}),"\n",(0,s.jsx)(r.li,{children:"Safari 14+"}),"\n",(0,s.jsx)(r.li,{children:"Edge 90+"}),"\n"]}),"\n",(0,s.jsx)(r.h2,{id:"cloud-requirements",children:"Cloud Requirements"}),"\n",(0,s.jsx)(r.h3,{id:"gcp",children:"GCP"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsx)(r.li,{children:"GKE cluster"}),"\n",(0,s.jsx)(r.li,{children:"Cloud Storage"}),"\n",(0,s.jsx)(r.li,{children:"Cloud SQL"}),"\n",(0,s.jsx)(r.li,{children:"Compute Engine"}),"\n"]}),"\n",(0,s.jsx)(r.h2,{id:"network-requirements",children:"Network Requirements"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsx)(r.li,{children:"HTTPS (443)"}),"\n",(0,s.jsx)(r.li,{children:"WebSocket support"}),"\n",(0,s.jsx)(r.li,{children:"Outbound internet access"}),"\n",(0,s.jsx)(r.li,{children:"Internal network access"}),"\n"]}),"\n",(0,s.jsx)(r.h2,{id:"security-requirements",children:"Security Requirements"}),"\n",(0,s.jsxs)(r.ul,{children:["\n",(0,s.jsx)(r.li,{children:"SSL/TLS certificates"}),"\n",(0,s.jsx)(r.li,{children:"Firewall rules"}),"\n",(0,s.jsx)(r.li,{children:"Access control"}),"\n",(0,s.jsx)(r.li,{children:"Encryption support"}),"\n"]})]})}function h(e={}){const{wrapper:r}={...(0,t.R)(),...e.components};return r?(0,s.jsx)(r,{...e,children:(0,s.jsx)(u,{...e})}):u(e)}}}]);