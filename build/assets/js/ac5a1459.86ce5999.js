"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7416],{6205:(e,n,s)=>{s.r(n),s.d(n,{assets:()=>l,contentTitle:()=>d,default:()=>h,frontMatter:()=>a,metadata:()=>i,toc:()=>c});const i=JSON.parse('{"id":"ai-architecture/api/auth/authorization","title":"Authorization","description":"Manage user roles, permissions, and access control.","source":"@site/docs/ai-architecture/api/auth/authorization.md","sourceDirName":"ai-architecture/api/auth","slug":"/ai-architecture/api/auth/authorization","permalink":"/docs/ai-architecture/api/auth/authorization","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/auth/authorization.md","tags":[],"version":"current","sidebarPosition":2,"frontMatter":{"sidebar_position":2},"sidebar":"tutorialSidebar","previous":{"title":"Authentication","permalink":"/docs/ai-architecture/api/auth/authentication"},"next":{"title":"API Tokens","permalink":"/docs/ai-architecture/api/auth/tokens"}}');var t=s(4848),r=s(8453);const a={sidebar_position:2},d="Authorization",l={},c=[{value:"User Roles",id:"user-roles",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Assign Role",id:"assign-role",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Check Permissions",id:"check-permissions",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Role Types",id:"role-types",level:2},{value:"Permission Types",id:"permission-types",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Authorization Best Practices",id:"authorization-best-practices",level:2}];function o(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,r.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"authorization",children:"Authorization"})}),"\n",(0,t.jsx)(n.p,{children:"Manage user roles, permissions, and access control."}),"\n",(0,t.jsx)(n.h2,{id:"user-roles",children:"User Roles"}),"\n",(0,t.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"GET /v1/auth/roles\n"})}),"\n",(0,t.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "roles": [\n      {\n        "id": "role_123",\n        "name": "admin",\n        "description": "Administrator with full access",\n        "permissions": [\n          "read:*",\n          "write:*",\n          "delete:*",\n          "manage:*"\n        ]\n      },\n      {\n        "id": "role_124",\n        "name": "data_scientist",\n        "description": "Data scientist with model and data access",\n        "permissions": [\n          "read:models",\n          "write:models",\n          "read:data",\n          "write:data"\n        ]\n      }\n    ]\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"assign-role",children:"Assign Role"}),"\n",(0,t.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"POST /v1/auth/users/{user_id}/roles\n"})}),"\n",(0,t.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "role_id": "role_123",\n  "scope": {\n    "type": "dataset",\n    "id": "dataset_123"\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "assignment": {\n      "id": "assignment_123",\n      "user_id": "user_123",\n      "role_id": "role_123",\n      "scope": {\n        "type": "dataset",\n        "id": "dataset_123"\n      },\n      "created_at": "2024-03-14T12:00:00Z"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"check-permissions",children:"Check Permissions"}),"\n",(0,t.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{children:"GET /v1/auth/permissions/check\n"})}),"\n",(0,t.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Parameter"}),(0,t.jsx)(n.th,{children:"Type"}),(0,t.jsx)(n.th,{children:"Required"}),(0,t.jsx)(n.th,{children:"Description"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"resource"}),(0,t.jsx)(n.td,{children:"string"}),(0,t.jsx)(n.td,{children:"Yes"}),(0,t.jsx)(n.td,{children:"Resource type (e.g., dataset)"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"action"}),(0,t.jsx)(n.td,{children:"string"}),(0,t.jsx)(n.td,{children:"Yes"}),(0,t.jsx)(n.td,{children:"Action (e.g., read, write)"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"resource_id"}),(0,t.jsx)(n.td,{children:"string"}),(0,t.jsx)(n.td,{children:"No"}),(0,t.jsx)(n.td,{children:"Specific resource ID"})]})]})]}),"\n",(0,t.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "permission": {\n      "granted": true,\n      "reason": "User has admin role",\n      "role": "admin",\n      "scope": "global"\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,t.jsx)(n.h2,{id:"role-types",children:"Role Types"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Role"}),(0,t.jsx)(n.th,{children:"Description"}),(0,t.jsx)(n.th,{children:"Permissions"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"admin"}),(0,t.jsx)(n.td,{children:"Full system access"}),(0,t.jsx)(n.td,{children:"All permissions"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"data_scientist"}),(0,t.jsx)(n.td,{children:"Model and data access"}),(0,t.jsx)(n.td,{children:"Read/write models and data"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"analyst"}),(0,t.jsx)(n.td,{children:"Data analysis access"}),(0,t.jsx)(n.td,{children:"Read data, run analysis"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"viewer"}),(0,t.jsx)(n.td,{children:"Read-only access"}),(0,t.jsx)(n.td,{children:"Read permissions only"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"permission-types",children:"Permission Types"}),"\n",(0,t.jsxs)(n.table,{children:[(0,t.jsx)(n.thead,{children:(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.th,{children:"Permission"}),(0,t.jsx)(n.th,{children:"Description"})]})}),(0,t.jsxs)(n.tbody,{children:[(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"read:*"}),(0,t.jsx)(n.td,{children:"Read access to all resources"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"write:*"}),(0,t.jsx)(n.td,{children:"Write access to all resources"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"delete:*"}),(0,t.jsx)(n.td,{children:"Delete access to all resources"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsx)(n.td,{children:"manage:*"}),(0,t.jsx)(n.td,{children:"Management access to all"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsxs)(n.td,{children:["read",":models"]}),(0,t.jsx)(n.td,{children:"Read access to models"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsxs)(n.td,{children:["write",":models"]}),(0,t.jsx)(n.td,{children:"Write access to models"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsxs)(n.td,{children:["read",":data"]}),(0,t.jsx)(n.td,{children:"Read access to data"})]}),(0,t.jsxs)(n.tr,{children:[(0,t.jsxs)(n.td,{children:["write",":data"]}),(0,t.jsx)(n.td,{children:"Write access to data"})]})]})]}),"\n",(0,t.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,t.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Get roles\nroles = client.auth.get_roles()\n\n# Assign role\nassignment = client.auth.assign_role(\n    "user_123",\n    role_id="role_123",\n    scope={\n        "type": "dataset",\n        "id": "dataset_123"\n    }\n)\n\n# Check permissions\npermission = client.auth.check_permission(\n    resource="dataset",\n    action="read",\n    resource_id="dataset_123"\n)\n'})}),"\n",(0,t.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,t.jsx)(n.pre,{children:(0,t.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Get roles\nconst roles = await client.auth.getRoles();\n\n// Assign role\nconst assignment = await client.auth.assignRole('user_123', {\n  roleId: 'role_123',\n  scope: {\n    type: 'dataset',\n    id: 'dataset_123'\n  }\n});\n\n// Check permissions\nconst permission = await client.auth.checkPermission({\n  resource: 'dataset',\n  action: 'read',\n  resourceId: 'dataset_123'\n});\n"})}),"\n",(0,t.jsx)(n.h2,{id:"authorization-best-practices",children:"Authorization Best Practices"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Follow principle of least privilege"}),"\n",(0,t.jsx)(n.li,{children:"Use role-based access control"}),"\n",(0,t.jsx)(n.li,{children:"Implement resource scoping"}),"\n",(0,t.jsx)(n.li,{children:"Regular permission audits"}),"\n",(0,t.jsx)(n.li,{children:"Document access policies"}),"\n",(0,t.jsx)(n.li,{children:"Monitor access patterns"}),"\n",(0,t.jsx)(n.li,{children:"Implement audit logging"}),"\n",(0,t.jsx)(n.li,{children:"Regular security reviews"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(o,{...e})}):o(e)}},8453:(e,n,s)=>{s.d(n,{R:()=>a,x:()=>d});var i=s(6540);const t={},r=i.createContext(t);function a(e){const n=i.useContext(r);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function d(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:a(e.components),i.createElement(r.Provider,{value:n},e.children)}}}]);