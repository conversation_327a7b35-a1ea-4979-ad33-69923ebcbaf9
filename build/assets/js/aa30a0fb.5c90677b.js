"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6715],{7197:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>d,default:()=>p,frontMatter:()=>r,metadata:()=>s,toc:()=>l});const s=JSON.parse('{"id":"ai-architecture/api/data/versioning","title":"Data Versioning","description":"Track and manage different versions of your datasets.","source":"@site/docs/ai-architecture/api/data/versioning.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/versioning","permalink":"/docs/ai-architecture/api/data/versioning","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/versioning.md","tags":[],"version":"current","sidebarPosition":3,"frontMatter":{"sidebar_position":3},"sidebar":"tutorialSidebar","previous":{"title":"Data Processing","permalink":"/docs/ai-architecture/api/data/processing"},"next":{"title":"Data Validation","permalink":"/docs/ai-architecture/api/data/validation"}}');var a=i(4848),t=i(8453);const r={sidebar_position:3},d="Data Versioning",o={},l=[{value:"Create Version",id:"create-version",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"List Versions",id:"list-versions",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Get Version Details",id:"get-version-details",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Compare Versions",id:"compare-versions",level:2},{value:"Endpoint",id:"endpoint-3",level:3},{value:"Query Parameters",id:"query-parameters-1",level:3},{value:"Example Response",id:"example-response-3",level:3},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Versioning Best Practices",id:"versioning-best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,t.R)(),...e.components};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.header,{children:(0,a.jsx)(n.h1,{id:"data-versioning",children:"Data Versioning"})}),"\n",(0,a.jsx)(n.p,{children:"Track and manage different versions of your datasets."}),"\n",(0,a.jsx)(n.h2,{id:"create-version",children:"Create Version"}),"\n",(0,a.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"POST /v1/data/datasets/{dataset_id}/versions\n"})}),"\n",(0,a.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "name": "v1.0.0",\n  "description": "Initial version with customer feedback data",\n  "tags": ["production", "validated"],\n  "metadata": {\n    "validation_score": 0.95,\n    "data_quality": "high"\n  }\n}\n'})}),"\n",(0,a.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "version": {\n      "id": "version_123",\n      "name": "v1.0.0",\n      "dataset_id": "dataset_123",\n      "description": "Initial version with customer feedback data",\n      "tags": ["production", "validated"],\n      "created_at": "2024-03-14T12:00:00Z",\n      "metadata": {\n        "validation_score": 0.95,\n        "data_quality": "high"\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"list-versions",children:"List Versions"}),"\n",(0,a.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/versions\n"})}),"\n",(0,a.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,a.jsxs)(n.table,{children:[(0,a.jsx)(n.thead,{children:(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.th,{children:"Parameter"}),(0,a.jsx)(n.th,{children:"Type"}),(0,a.jsx)(n.th,{children:"Description"})]})}),(0,a.jsxs)(n.tbody,{children:[(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"page"}),(0,a.jsx)(n.td,{children:"integer"}),(0,a.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"limit"}),(0,a.jsx)(n.td,{children:"integer"}),(0,a.jsx)(n.td,{children:"Items per page (default: 10)"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"tag"}),(0,a.jsx)(n.td,{children:"string"}),(0,a.jsx)(n.td,{children:"Filter by tag"})]})]})]}),"\n",(0,a.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "versions": [\n      {\n        "id": "version_123",\n        "name": "v1.0.0",\n        "dataset_id": "dataset_123",\n        "description": "Initial version",\n        "tags": ["production"],\n        "created_at": "2024-03-14T12:00:00Z"\n      },\n      {\n        "id": "version_124",\n        "name": "v1.0.1",\n        "dataset_id": "dataset_123",\n        "description": "Updated version",\n        "tags": ["staging"],\n        "created_at": "2024-03-15T12:00:00Z"\n      }\n    ],\n    "pagination": {\n      "total": 2,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-15T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"get-version-details",children:"Get Version Details"}),"\n",(0,a.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/versions/{version_id}\n"})}),"\n",(0,a.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "version": {\n      "id": "version_123",\n      "name": "v1.0.0",\n      "dataset_id": "dataset_123",\n      "description": "Initial version",\n      "tags": ["production"],\n      "created_at": "2024-03-14T12:00:00Z",\n      "metadata": {\n        "validation_score": 0.95,\n        "data_quality": "high"\n      },\n      "changes": {\n        "added_samples": 1000,\n        "removed_samples": 50,\n        "modified_samples": 200\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-15T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"compare-versions",children:"Compare Versions"}),"\n",(0,a.jsx)(n.h3,{id:"endpoint-3",children:"Endpoint"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/versions/compare\n"})}),"\n",(0,a.jsx)(n.h3,{id:"query-parameters-1",children:"Query Parameters"}),"\n",(0,a.jsxs)(n.table,{children:[(0,a.jsx)(n.thead,{children:(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.th,{children:"Parameter"}),(0,a.jsx)(n.th,{children:"Type"}),(0,a.jsx)(n.th,{children:"Description"})]})}),(0,a.jsxs)(n.tbody,{children:[(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"version1_id"}),(0,a.jsx)(n.td,{children:"string"}),(0,a.jsx)(n.td,{children:"First version ID"})]}),(0,a.jsxs)(n.tr,{children:[(0,a.jsx)(n.td,{children:"version2_id"}),(0,a.jsx)(n.td,{children:"string"}),(0,a.jsx)(n.td,{children:"Second version ID"})]})]})]}),"\n",(0,a.jsx)(n.h3,{id:"example-response-3",children:"Example Response"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "comparison": {\n      "version1": {\n        "id": "version_123",\n        "name": "v1.0.0"\n      },\n      "version2": {\n        "id": "version_124",\n        "name": "v1.0.1"\n      },\n      "differences": {\n        "added_samples": 100,\n        "removed_samples": 20,\n        "modified_samples": 50,\n        "metadata_changes": {\n          "validation_score": {\n            "from": 0.95,\n            "to": 0.97\n          }\n        }\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-15T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,a.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,a.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create version\nversion = client.data.create_version(\n    "dataset_123",\n    name="v1.0.0",\n    description="Initial version",\n    tags=["production"]\n)\n\n# List versions\nversions = client.data.list_versions(\n    "dataset_123",\n    page=1,\n    limit=10\n)\n\n# Get version details\nversion_details = client.data.get_version(\n    "dataset_123",\n    "version_123"\n)\n\n# Compare versions\ncomparison = client.data.compare_versions(\n    "dataset_123",\n    version1_id="version_123",\n    version2_id="version_124"\n)\n'})}),"\n",(0,a.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,a.jsx)(n.pre,{children:(0,a.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create version\nconst version = await client.data.createVersion('dataset_123', {\n  name: 'v1.0.0',\n  description: 'Initial version',\n  tags: ['production']\n});\n\n// List versions\nconst versions = await client.data.listVersions('dataset_123', {\n  page: 1,\n  limit: 10\n});\n\n// Get version details\nconst versionDetails = await client.data.getVersion(\n  'dataset_123',\n  'version_123'\n);\n\n// Compare versions\nconst comparison = await client.data.compareVersions('dataset_123', {\n  version1Id: 'version_123',\n  version2Id: 'version_124'\n});\n"})}),"\n",(0,a.jsx)(n.h2,{id:"versioning-best-practices",children:"Versioning Best Practices"}),"\n",(0,a.jsxs)(n.ol,{children:["\n",(0,a.jsx)(n.li,{children:"Use semantic versioning"}),"\n",(0,a.jsx)(n.li,{children:"Document version changes"}),"\n",(0,a.jsx)(n.li,{children:"Tag important versions"}),"\n",(0,a.jsx)(n.li,{children:"Maintain version history"}),"\n",(0,a.jsx)(n.li,{children:"Track metadata changes"}),"\n",(0,a.jsx)(n.li,{children:"Implement rollback capability"}),"\n",(0,a.jsx)(n.li,{children:"Validate version integrity"}),"\n",(0,a.jsx)(n.li,{children:"Set up version policies"}),"\n"]})]})}function p(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,a.jsx)(n,{...e,children:(0,a.jsx)(c,{...e})}):c(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>r,x:()=>d});var s=i(6540);const a={},t=s.createContext(a);function r(e){const n=s.useContext(t);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function d(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(a):e.components||a:r(e.components),s.createElement(t.Provider,{value:n},e.children)}}}]);