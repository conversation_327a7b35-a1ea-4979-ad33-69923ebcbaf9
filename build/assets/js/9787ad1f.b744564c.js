"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[4270],{6913:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>a,contentTitle:()=>l,default:()=>h,frontMatter:()=>r,metadata:()=>d,toc:()=>c});const d=JSON.parse('{"id":"ai-architecture/api/models/model-details","title":"Model Details","description":"Retrieve detailed information about a specific AI model.","source":"@site/docs/ai-architecture/api/models/model-details.md","sourceDirName":"ai-architecture/api/models","slug":"/ai-architecture/api/models/model-details","permalink":"/docs/ai-architecture/api/models/model-details","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/models/model-details.md","tags":[],"version":"current","sidebarPosition":2,"frontMatter":{"sidebar_position":2},"sidebar":"tutorialSidebar","previous":{"title":"List Models","permalink":"/docs/ai-architecture/api/models/list-models"},"next":{"title":"Model Predictions","permalink":"/docs/ai-architecture/api/models/predictions"}}');var i=n(4848),s=n(8453);const r={sidebar_position:2},l="Model Details",a={},c=[{value:"Endpoint",id:"endpoint",level:2},{value:"Path Parameters",id:"path-parameters",level:2},{value:"Example Request",id:"example-request",level:2},{value:"Example Response",id:"example-response",level:2},{value:"Error Codes",id:"error-codes",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Model Types",id:"model-types",level:2},{value:"Model Status",id:"model-status",level:2}];function o(e){const t={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,s.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(t.header,{children:(0,i.jsx)(t.h1,{id:"model-details",children:"Model Details"})}),"\n",(0,i.jsx)(t.p,{children:"Retrieve detailed information about a specific AI model."}),"\n",(0,i.jsx)(t.h2,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{children:"GET /v1/models/{model_id}\n"})}),"\n",(0,i.jsx)(t.h2,{id:"path-parameters",children:"Path Parameters"}),"\n",(0,i.jsxs)(t.table,{children:[(0,i.jsx)(t.thead,{children:(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.th,{children:"Parameter"}),(0,i.jsx)(t.th,{children:"Type"}),(0,i.jsx)(t.th,{children:"Required"}),(0,i.jsx)(t.th,{children:"Description"})]})}),(0,i.jsx)(t.tbody,{children:(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"model_id"}),(0,i.jsx)(t.td,{children:"string"}),(0,i.jsx)(t.td,{children:"Yes"}),(0,i.jsx)(t.td,{children:"ID of the model"})]})})]}),"\n",(0,i.jsx)(t.h2,{id:"example-request",children:"Example Request"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{className:"language-bash",children:'curl -H "Authorization: Bearer YOUR_API_KEY" \\\n     "https://api.ai-platform.example.com/v1/models/model_123"\n'})}),"\n",(0,i.jsx)(t.h2,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "model": {\n      "id": "model_123",\n      "name": "GPT-4",\n      "type": "language",\n      "status": "active",\n      "version": "1.0.0",\n      "description": "Advanced language model for text generation",\n      "capabilities": [\n        "text-generation",\n        "text-completion",\n        "code-generation"\n      ],\n      "parameters": {\n        "max_tokens": 4096,\n        "temperature": 0.7,\n        "top_p": 1.0\n      },\n      "created_at": "2024-03-14T12:00:00Z",\n      "updated_at": "2024-03-14T12:00:00Z",\n      "metrics": {\n        "accuracy": 0.95,\n        "latency": 150,\n        "throughput": 1000\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(t.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,i.jsxs)(t.table,{children:[(0,i.jsx)(t.thead,{children:(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.th,{children:"Code"}),(0,i.jsx)(t.th,{children:"Description"})]})}),(0,i.jsxs)(t.tbody,{children:[(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"not_found"}),(0,i.jsx)(t.td,{children:"Model not found"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"unauthorized"}),(0,i.jsx)(t.td,{children:"Invalid or missing API key"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"rate_limit_exceeded"}),(0,i.jsx)(t.td,{children:"Too many requests"})]})]})]}),"\n",(0,i.jsx)(t.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(t.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Get model details\nmodel = client.models.get("model_123")\n'})}),"\n",(0,i.jsx)(t.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(t.pre,{children:(0,i.jsx)(t.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Get model details\nconst model = await client.models.get('model_123');\n"})}),"\n",(0,i.jsx)(t.h2,{id:"model-types",children:"Model Types"}),"\n",(0,i.jsxs)(t.table,{children:[(0,i.jsx)(t.thead,{children:(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.th,{children:"Type"}),(0,i.jsx)(t.th,{children:"Description"})]})}),(0,i.jsxs)(t.tbody,{children:[(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"language"}),(0,i.jsx)(t.td,{children:"Natural language processing"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"vision"}),(0,i.jsx)(t.td,{children:"Computer vision"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"audio"}),(0,i.jsx)(t.td,{children:"Audio processing"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"custom"}),(0,i.jsx)(t.td,{children:"Custom model type"})]})]})]}),"\n",(0,i.jsx)(t.h2,{id:"model-status",children:"Model Status"}),"\n",(0,i.jsxs)(t.table,{children:[(0,i.jsx)(t.thead,{children:(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.th,{children:"Status"}),(0,i.jsx)(t.th,{children:"Description"})]})}),(0,i.jsxs)(t.tbody,{children:[(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"active"}),(0,i.jsx)(t.td,{children:"Model is ready for use"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"training"}),(0,i.jsx)(t.td,{children:"Model is currently training"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"failed"}),(0,i.jsx)(t.td,{children:"Model training failed"})]}),(0,i.jsxs)(t.tr,{children:[(0,i.jsx)(t.td,{children:"archived"}),(0,i.jsx)(t.td,{children:"Model is archived"})]})]})]})]})}function h(e={}){const{wrapper:t}={...(0,s.R)(),...e.components};return t?(0,i.jsx)(t,{...e,children:(0,i.jsx)(o,{...e})}):o(e)}},8453:(e,t,n)=>{n.d(t,{R:()=>r,x:()=>l});var d=n(6540);const i={},s=d.createContext(i);function r(e){const t=d.useContext(s);return d.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function l(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:r(e.components),d.createElement(s.Provider,{value:t},e.children)}}}]);