"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2924],{7135:(t,e,s)=>{s.r(e),s.d(e,{assets:()=>c,contentTitle:()=>r,default:()=>h,frontMatter:()=>l,metadata:()=>n,toc:()=>d});const n=JSON.parse('{"id":"ai-architecture/api/data/statistics","title":"Data Statistics","description":"Analyze and understand your datasets through comprehensive statistics.","source":"@site/docs/ai-architecture/api/data/statistics.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/statistics","permalink":"/docs/ai-architecture/api/data/statistics","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/statistics.md","tags":[],"version":"current","sidebarPosition":8,"frontMatter":{"sidebar_position":8},"sidebar":"tutorialSidebar","previous":{"title":"Data Export","permalink":"/docs/ai-architecture/api/data/export"},"next":{"title":"Data Monitoring","permalink":"/docs/ai-architecture/api/data/monitoring"}}');var i=s(4848),a=s(8453);const l={sidebar_position:8},r="Data Statistics",c={},d=[{value:"Generate Statistics",id:"generate-statistics",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Statistics Results",id:"get-statistics-results",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"List Statistics",id:"list-statistics",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Statistics Status",id:"statistics-status",level:2},{value:"Available Metrics",id:"available-metrics",level:2},{value:"Basic Statistics",id:"basic-statistics",level:3},{value:"Distribution Analysis",id:"distribution-analysis",level:3},{value:"Correlation Analysis",id:"correlation-analysis",level:3},{value:"Quality Metrics",id:"quality-metrics",level:3},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Statistics Best Practices",id:"statistics-best-practices",level:2}];function o(t){const e={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,a.R)(),...t.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e.header,{children:(0,i.jsx)(e.h1,{id:"data-statistics",children:"Data Statistics"})}),"\n",(0,i.jsx)(e.p,{children:"Analyze and understand your datasets through comprehensive statistics."}),"\n",(0,i.jsx)(e.h2,{id:"generate-statistics",children:"Generate Statistics"}),"\n",(0,i.jsx)(e.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"POST /v1/data/datasets/{dataset_id}/statistics\n"})}),"\n",(0,i.jsx)(e.h3,{id:"request-body",children:"Request Body"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{className:"language-json",children:'{\n  "metrics": [\n    "basic_stats",\n    "distribution",\n    "correlation",\n    "quality_metrics"\n  ],\n  "options": {\n    "include_visualizations": true,\n    "save_results": true,\n    "format": "json"\n  }\n}\n'})}),"\n",(0,i.jsx)(e.h3,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{className:"language-json",children:'{\n  "data": {\n    "statistics": {\n      "id": "stats_123",\n      "dataset_id": "dataset_123",\n      "status": "processing",\n      "created_at": "2024-03-14T12:00:00Z",\n      "metrics": [\n        "basic_stats",\n        "distribution",\n        "correlation",\n        "quality_metrics"\n      ]\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(e.h2,{id:"get-statistics-results",children:"Get Statistics Results"}),"\n",(0,i.jsx)(e.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"GET /v1/data/datasets/{dataset_id}/statistics/{statistics_id}\n"})}),"\n",(0,i.jsx)(e.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{className:"language-json",children:'{\n  "data": {\n    "statistics": {\n      "id": "stats_123",\n      "dataset_id": "dataset_123",\n      "status": "completed",\n      "created_at": "2024-03-14T12:00:00Z",\n      "completed_at": "2024-03-14T12:01:00Z",\n      "results": {\n        "basic_stats": {\n          "total_samples": 10000,\n          "features": {\n            "text": {\n              "type": "text",\n              "unique_values": 9500,\n              "null_count": 0,\n              "min_length": 10,\n              "max_length": 1000,\n              "avg_length": 150\n            },\n            "label": {\n              "type": "categorical",\n              "unique_values": 3,\n              "null_count": 0,\n              "distribution": {\n                "positive": 0.6,\n                "negative": 0.3,\n                "neutral": 0.1\n              }\n            }\n          }\n        },\n        "distribution": {\n          "text_length": {\n            "mean": 150,\n            "std": 50,\n            "percentiles": {\n              "25": 100,\n              "50": 150,\n              "75": 200\n            },\n            "histogram": {\n              "bins": [0, 50, 100, 150, 200, 250],\n              "counts": [100, 1000, 4000, 3000, 1500, 400]\n            }\n          }\n        },\n        "correlation": {\n          "text_length_label": {\n            "correlation": 0.2,\n            "p_value": 0.001\n          }\n        },\n        "quality_metrics": {\n          "completeness": 1.0,\n          "consistency": 0.95,\n          "accuracy": 0.98,\n          "issues": [\n            {\n              "type": "inconsistent_format",\n              "severity": "warning",\n              "count": 50,\n              "description": "Text contains inconsistent formatting"\n            }\n          ]\n        }\n      },\n      "visualizations": {\n        "text_length_dist": "https://storage.ai-platform.example.com/visualizations/stats_123/text_length_dist.png",\n        "label_dist": "https://storage.ai-platform.example.com/visualizations/stats_123/label_dist.png",\n        "correlation_matrix": "https://storage.ai-platform.example.com/visualizations/stats_123/correlation_matrix.png"\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:01:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(e.h2,{id:"list-statistics",children:"List Statistics"}),"\n",(0,i.jsx)(e.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{children:"GET /v1/data/datasets/{dataset_id}/statistics\n"})}),"\n",(0,i.jsx)(e.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,i.jsxs)(e.table,{children:[(0,i.jsx)(e.thead,{children:(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.th,{children:"Parameter"}),(0,i.jsx)(e.th,{children:"Type"}),(0,i.jsx)(e.th,{children:"Description"})]})}),(0,i.jsxs)(e.tbody,{children:[(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.td,{children:"page"}),(0,i.jsx)(e.td,{children:"integer"}),(0,i.jsx)(e.td,{children:"Page number (default: 1)"})]}),(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.td,{children:"limit"}),(0,i.jsx)(e.td,{children:"integer"}),(0,i.jsx)(e.td,{children:"Items per page (default: 10)"})]}),(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.td,{children:"status"}),(0,i.jsx)(e.td,{children:"string"}),(0,i.jsx)(e.td,{children:"Filter by status"})]})]})]}),"\n",(0,i.jsx)(e.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{className:"language-json",children:'{\n  "data": {\n    "statistics": [\n      {\n        "id": "stats_123",\n        "dataset_id": "dataset_123",\n        "status": "completed",\n        "created_at": "2024-03-14T12:00:00Z",\n        "completed_at": "2024-03-14T12:01:00Z",\n        "metrics": [\n          "basic_stats",\n          "distribution",\n          "correlation",\n          "quality_metrics"\n        ]\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:01:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(e.h2,{id:"statistics-status",children:"Statistics Status"}),"\n",(0,i.jsxs)(e.table,{children:[(0,i.jsx)(e.thead,{children:(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.th,{children:"Status"}),(0,i.jsx)(e.th,{children:"Description"})]})}),(0,i.jsxs)(e.tbody,{children:[(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.td,{children:"processing"}),(0,i.jsx)(e.td,{children:"Statistics generation running"})]}),(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.td,{children:"completed"}),(0,i.jsx)(e.td,{children:"Statistics generation completed"})]}),(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.td,{children:"failed"}),(0,i.jsx)(e.td,{children:"Statistics generation failed"})]}),(0,i.jsxs)(e.tr,{children:[(0,i.jsx)(e.td,{children:"cancelled"}),(0,i.jsx)(e.td,{children:"Statistics generation cancelled"})]})]})]}),"\n",(0,i.jsx)(e.h2,{id:"available-metrics",children:"Available Metrics"}),"\n",(0,i.jsx)(e.h3,{id:"basic-statistics",children:"Basic Statistics"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Total samples"}),"\n",(0,i.jsx)(e.li,{children:"Feature types"}),"\n",(0,i.jsx)(e.li,{children:"Unique values"}),"\n",(0,i.jsx)(e.li,{children:"Null counts"}),"\n",(0,i.jsx)(e.li,{children:"Min/max/mean values"}),"\n",(0,i.jsx)(e.li,{children:"Value distributions"}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"distribution-analysis",children:"Distribution Analysis"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Histograms"}),"\n",(0,i.jsx)(e.li,{children:"Percentiles"}),"\n",(0,i.jsx)(e.li,{children:"Skewness"}),"\n",(0,i.jsx)(e.li,{children:"Kurtosis"}),"\n",(0,i.jsx)(e.li,{children:"Outlier detection"}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"correlation-analysis",children:"Correlation Analysis"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Feature correlations"}),"\n",(0,i.jsx)(e.li,{children:"P-values"}),"\n",(0,i.jsx)(e.li,{children:"Correlation matrices"}),"\n",(0,i.jsx)(e.li,{children:"Feature importance"}),"\n"]}),"\n",(0,i.jsx)(e.h3,{id:"quality-metrics",children:"Quality Metrics"}),"\n",(0,i.jsxs)(e.ul,{children:["\n",(0,i.jsx)(e.li,{children:"Completeness"}),"\n",(0,i.jsx)(e.li,{children:"Consistency"}),"\n",(0,i.jsx)(e.li,{children:"Accuracy"}),"\n",(0,i.jsx)(e.li,{children:"Data quality issues"}),"\n",(0,i.jsx)(e.li,{children:"Anomaly detection"}),"\n"]}),"\n",(0,i.jsx)(e.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(e.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Generate statistics\nstats = client.data.generate_statistics(\n    "dataset_123",\n    metrics=[\n        "basic_stats",\n        "distribution",\n        "correlation",\n        "quality_metrics"\n    ]\n)\n\n# Get statistics results\nresults = client.data.get_statistics(\n    "dataset_123",\n    "stats_123"\n)\n\n# List statistics\nstats_list = client.data.list_statistics(\n    "dataset_123",\n    page=1,\n    limit=10\n)\n'})}),"\n",(0,i.jsx)(e.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(e.pre,{children:(0,i.jsx)(e.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Generate statistics\nconst stats = await client.data.generateStatistics('dataset_123', {\n  metrics: [\n    'basic_stats',\n    'distribution',\n    'correlation',\n    'quality_metrics'\n  ]\n});\n\n// Get statistics results\nconst results = await client.data.getStatistics(\n  'dataset_123',\n  'stats_123'\n);\n\n// List statistics\nconst statsList = await client.data.listStatistics('dataset_123', {\n  page: 1,\n  limit: 10\n});\n"})}),"\n",(0,i.jsx)(e.h2,{id:"statistics-best-practices",children:"Statistics Best Practices"}),"\n",(0,i.jsxs)(e.ol,{children:["\n",(0,i.jsx)(e.li,{children:"Choose relevant metrics"}),"\n",(0,i.jsx)(e.li,{children:"Monitor data quality"}),"\n",(0,i.jsx)(e.li,{children:"Track distributions"}),"\n",(0,i.jsx)(e.li,{children:"Analyze correlations"}),"\n",(0,i.jsx)(e.li,{children:"Detect anomalies"}),"\n",(0,i.jsx)(e.li,{children:"Generate visualizations"}),"\n",(0,i.jsx)(e.li,{children:"Document findings"}),"\n",(0,i.jsx)(e.li,{children:"Regular analysis"}),"\n"]})]})}function h(t={}){const{wrapper:e}={...(0,a.R)(),...t.components};return e?(0,i.jsx)(e,{...t,children:(0,i.jsx)(o,{...t})}):o(t)}},8453:(t,e,s)=>{s.d(e,{R:()=>l,x:()=>r});var n=s(6540);const i={},a=n.createContext(i);function l(t){const e=n.useContext(a);return n.useMemo((function(){return"function"==typeof t?t(e):{...e,...t}}),[e,t])}function r(t){let e;return e=t.disableParentContext?"function"==typeof t.components?t.components(i):t.components||i:l(t.components),n.createElement(a.Provider,{value:e},t.children)}}}]);