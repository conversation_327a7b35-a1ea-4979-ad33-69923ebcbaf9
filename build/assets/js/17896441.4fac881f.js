"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8401],{215:(e,n,t)=>{t.d(n,{A:()=>b});var s=t(6540),a=t(8542);function i(e){const n=e.map((e=>({...e,parentIndex:-1,children:[]}))),t=Array(7).fill(-1);n.forEach(((e,n)=>{const s=t.slice(2,e.level);e.parentIndex=Math.max(...s),t[e.level]=n}));const s=[];return n.forEach((e=>{const{parentIndex:t,...a}=e;t>=0?n[t].children.push(a):s.push(a)})),s}function o({toc:e,minHeadingLevel:n,maxHeadingLevel:t}){return e.flatMap((e=>{const s=o({toc:e.children,minHeadingLevel:n,maxHeadingLevel:t});return function(e){return e.level>=n&&e.level<=t}(e)?[{...e,children:s}]:s}))}function l(e){const n=e.getBoundingClientRect();return n.top===n.bottom?l(e.parentNode):n}function r(e,{anchorTopOffset:n}){const t=e.find((e=>l(e).top>=n));if(t){return function(e){return e.top>0&&e.bottom<window.innerHeight/2}(l(t))?t:e[e.indexOf(t)-1]??null}return e[e.length-1]??null}function c(){const e=(0,s.useRef)(0),{navbar:{hideOnScroll:n}}=(0,a.p)();return(0,s.useEffect)((()=>{e.current=n?0:document.querySelector(".navbar").clientHeight}),[n]),e}function d(e){const n=(0,s.useRef)(void 0),t=c();(0,s.useEffect)((()=>{if(!e)return()=>{};const{linkClassName:s,linkActiveClassName:a,minHeadingLevel:i,maxHeadingLevel:o}=e;function l(){const e=function(e){return Array.from(document.getElementsByClassName(e))}(s),l=function({minHeadingLevel:e,maxHeadingLevel:n}){const t=[];for(let s=e;s<=n;s+=1)t.push(`h${s}.anchor`);return Array.from(document.querySelectorAll(t.join()))}({minHeadingLevel:i,maxHeadingLevel:o}),c=r(l,{anchorTopOffset:t.current}),d=e.find((e=>c&&c.id===function(e){return decodeURIComponent(e.href.substring(e.href.indexOf("#")+1))}(e)));e.forEach((e=>{!function(e,t){t?(n.current&&n.current!==e&&n.current.classList.remove(a),e.classList.add(a),n.current=e):e.classList.remove(a)}(e,e===d)}))}return document.addEventListener("scroll",l),document.addEventListener("resize",l),l(),()=>{document.removeEventListener("scroll",l),document.removeEventListener("resize",l)}}),[e,t])}var u=t(6289),m=t(4848);function h({toc:e,className:n,linkClassName:t,isChild:s}){return e.length?(0,m.jsx)("ul",{className:s?void 0:n,children:e.map((e=>(0,m.jsxs)("li",{children:[(0,m.jsx)(u.A,{to:`#${e.id}`,className:t??void 0,dangerouslySetInnerHTML:{__html:e.value}}),(0,m.jsx)(h,{isChild:!0,toc:e.children,className:n,linkClassName:t})]},e.id)))}):null}const v=s.memo(h);function b({toc:e,className:n="table-of-contents table-of-contents__left-border",linkClassName:t="table-of-contents__link",linkActiveClassName:l,minHeadingLevel:r,maxHeadingLevel:c,...u}){const h=(0,a.p)(),b=r??h.tableOfContents.minHeadingLevel,x=c??h.tableOfContents.maxHeadingLevel,g=function({toc:e,minHeadingLevel:n,maxHeadingLevel:t}){return(0,s.useMemo)((()=>o({toc:i(e),minHeadingLevel:n,maxHeadingLevel:t})),[e,n,t])}({toc:e,minHeadingLevel:b,maxHeadingLevel:x});return d((0,s.useMemo)((()=>{if(t&&l)return{linkClassName:t,linkActiveClassName:l,minHeadingLevel:b,maxHeadingLevel:x}}),[t,l,b,x])),(0,m.jsx)(v,{toc:g,className:n,linkClassName:t,...u})}},665:(e,n,t)=>{t.d(n,{A:()=>c});t(6540);var s=t(4164),a=t(3452),i=t(5279),o=t(7821),l=t(4848);function r({className:e}){return(0,l.jsx)(o.A,{type:"caution",title:(0,l.jsx)(a.Rc,{}),className:(0,s.A)(e,i.G.common.unlistedBanner),children:(0,l.jsx)(a.Uh,{})})}function c(e){return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(a.AE,{}),(0,l.jsx)(r,{...e})]})}},1865:(e,n,t)=>{t.d(n,{A:()=>o});t(6540);var s=t(4164),a=t(6289),i=t(4848);function o(e){const{permalink:n,title:t,subLabel:o,isNext:l}=e;return(0,i.jsxs)(a.A,{className:(0,s.A)("pagination-nav__link",l?"pagination-nav__link--next":"pagination-nav__link--prev"),to:n,children:[o&&(0,i.jsx)("div",{className:"pagination-nav__sublabel",children:o}),(0,i.jsx)("div",{className:"pagination-nav__label",children:t})]})}},2075:(e,n,t)=>{t.d(n,{A:()=>d});t(6540);var s=t(4164),a=t(3452),i=t(5279),o=t(7821),l=t(4848);function r({className:e}){return(0,l.jsx)(o.A,{type:"caution",title:(0,l.jsx)(a.Yh,{}),className:(0,s.A)(e,i.G.common.draftBanner),children:(0,l.jsx)(a.TT,{})})}var c=t(665);function d({metadata:e}){const{unlisted:n,frontMatter:t}=e;return(0,l.jsxs)(l.Fragment,{children:[(n||t.unlisted)&&(0,l.jsx)(c.A,{}),t.draft&&(0,l.jsx)(r,{})]})}},3452:(e,n,t)=>{t.d(n,{AE:()=>r,Rc:()=>o,TT:()=>d,Uh:()=>l,Yh:()=>c});t(6540);var s=t(539),a=t(7143),i=t(4848);function o(){return(0,i.jsx)(s.A,{id:"theme.contentVisibility.unlistedBanner.title",description:"The unlisted content banner title",children:"Unlisted page"})}function l(){return(0,i.jsx)(s.A,{id:"theme.contentVisibility.unlistedBanner.message",description:"The unlisted content banner message",children:"This page is unlisted. Search engines will not index it, and only users having a direct link can access it."})}function r(){return(0,i.jsx)(a.A,{children:(0,i.jsx)("meta",{name:"robots",content:"noindex, nofollow"})})}function c(){return(0,i.jsx)(s.A,{id:"theme.contentVisibility.draftBanner.title",description:"The draft content banner title",children:"Draft page"})}function d(){return(0,i.jsx)(s.A,{id:"theme.contentVisibility.draftBanner.message",description:"The draft content banner message",children:"This page is a draft. It will only be visible in dev and be excluded from the production build."})}},3600:(e,n,t)=>{t.d(n,{A:()=>o});t(6540);var s=t(539),a=t(1865),i=t(4848);function o(e){const{previous:n,next:t}=e;return(0,i.jsxs)("nav",{className:"pagination-nav docusaurus-mt-lg","aria-label":(0,s.T)({id:"theme.docs.paginator.navAriaLabel",message:"Docs pages",description:"The ARIA label for the docs pagination"}),children:[n&&(0,i.jsx)(a.A,{...n,subLabel:(0,i.jsx)(s.A,{id:"theme.docs.paginator.previous",description:"The label used to navigate to the previous doc",children:"Previous"})}),t&&(0,i.jsx)(a.A,{...t,subLabel:(0,i.jsx)(s.A,{id:"theme.docs.paginator.next",description:"The label used to navigate to the next doc",children:"Next"}),isNext:!0})]})}},3953:(e,n,t)=>{t.d(n,{A:()=>l});t(6540);var s=t(4164),a=t(6289);const i={tag:"tag_zVej",tagRegular:"tagRegular_sFm0",tagWithCount:"tagWithCount_h2kH"};var o=t(4848);function l({permalink:e,label:n,count:t,description:l}){return(0,o.jsxs)(a.A,{href:e,title:l,className:(0,s.A)(i.tag,t?i.tagWithCount:i.tagRegular),children:[n,t&&(0,o.jsx)("span",{children:t})]})}},4685:(e,n,t)=>{t.r(n),t.d(n,{default:()=>S});var s=t(6540),a=t(4737),i=t(3444),o=t(4848);const l=s.createContext(null);function r({children:e,content:n}){const t=function(e){return(0,s.useMemo)((()=>({metadata:e.metadata,frontMatter:e.frontMatter,assets:e.assets,contentTitle:e.contentTitle,toc:e.toc})),[e])}(n);return(0,o.jsx)(l.Provider,{value:t,children:e})}function c(){const e=(0,s.useContext)(l);if(null===e)throw new i.dV("DocProvider");return e}function d(){const{metadata:e,frontMatter:n,assets:t}=c();return(0,o.jsx)(a.be,{title:e.title,description:e.description,keywords:n.keywords,image:t.image??n.image})}var u=t(4164),m=t(1357),h=t(3600);function v(){const{metadata:e}=c();return(0,o.jsx)(h.A,{previous:e.previous,next:e.next})}var b=t(5833),x=t(5730),g=t(5279),f=t(6239),p=t(5783);function j(){const{metadata:e}=c(),{editUrl:n,lastUpdatedAt:t,lastUpdatedBy:s,tags:a}=e,i=a.length>0,l=!!(n||t||s);return i||l?(0,o.jsxs)("footer",{className:(0,u.A)(g.G.docs.docFooter,"docusaurus-mt-lg"),children:[i&&(0,o.jsx)("div",{className:(0,u.A)("row margin-top--sm",g.G.docs.docFooterTagsRow),children:(0,o.jsx)("div",{className:"col",children:(0,o.jsx)(f.A,{tags:a})})}),l&&(0,o.jsx)(p.A,{className:(0,u.A)("margin-top--sm",g.G.docs.docFooterEditMetaRow),editUrl:n,lastUpdatedAt:t,lastUpdatedBy:s})]}):null}var A=t(9846),N=t(215),L=t(539);const C={tocCollapsibleButton:"tocCollapsibleButton_TO0P",tocCollapsibleButtonExpanded:"tocCollapsibleButtonExpanded_MG3E"};function _({collapsed:e,...n}){return(0,o.jsx)("button",{type:"button",...n,className:(0,u.A)("clean-btn",C.tocCollapsibleButton,!e&&C.tocCollapsibleButtonExpanded,n.className),children:(0,o.jsx)(L.A,{id:"theme.TOCCollapsible.toggleButtonLabel",description:"The label used by the button on the collapsible TOC component",children:"On this page"})})}const T={tocCollapsible:"tocCollapsible_ETCw",tocCollapsibleContent:"tocCollapsibleContent_vkbj",tocCollapsibleExpanded:"tocCollapsibleExpanded_sAul"};function k({toc:e,className:n,minHeadingLevel:t,maxHeadingLevel:s}){const{collapsed:a,toggleCollapsed:i}=(0,A.u)({initialState:!0});return(0,o.jsxs)("div",{className:(0,u.A)(T.tocCollapsible,!a&&T.tocCollapsibleExpanded,n),children:[(0,o.jsx)(_,{collapsed:a,onClick:i}),(0,o.jsx)(A.N,{lazy:!0,className:T.tocCollapsibleContent,collapsed:a,children:(0,o.jsx)(N.A,{toc:e,minHeadingLevel:t,maxHeadingLevel:s})})]})}const H={tocMobile:"tocMobile_ITEo"};function M(){const{toc:e,frontMatter:n}=c();return(0,o.jsx)(k,{toc:e,minHeadingLevel:n.toc_min_heading_level,maxHeadingLevel:n.toc_max_heading_level,className:(0,u.A)(g.G.docs.docTocMobile,H.tocMobile)})}var y=t(7959);function I(){const{toc:e,frontMatter:n}=c();return(0,o.jsx)(y.A,{toc:e,minHeadingLevel:n.toc_min_heading_level,maxHeadingLevel:n.toc_max_heading_level,className:g.G.docs.docTocDesktop})}var B=t(9303),w=t(4809);function E({children:e}){const n=function(){const{metadata:e,frontMatter:n,contentTitle:t}=c();return n.hide_title||void 0!==t?null:e.title}();return(0,o.jsxs)("div",{className:(0,u.A)(g.G.docs.docMarkdown,"markdown"),children:[n&&(0,o.jsx)("header",{children:(0,o.jsx)(B.A,{as:"h1",children:n})}),(0,o.jsx)(w.A,{children:e})]})}var V=t(7519),O=t(2075);const R={docItemContainer:"docItemContainer_Djhp",docItemCol:"docItemCol_VOVn"};function G({children:e}){const n=function(){const{frontMatter:e,toc:n}=c(),t=(0,m.l)(),s=e.hide_table_of_contents,a=!s&&n.length>0;return{hidden:s,mobile:a?(0,o.jsx)(M,{}):void 0,desktop:!a||"desktop"!==t&&"ssr"!==t?void 0:(0,o.jsx)(I,{})}}(),{metadata:t}=c();return(0,o.jsxs)("div",{className:"row",children:[(0,o.jsxs)("div",{className:(0,u.A)("col",!n.hidden&&R.docItemCol),children:[(0,o.jsx)(O.A,{metadata:t}),(0,o.jsx)(b.A,{}),(0,o.jsxs)("div",{className:R.docItemContainer,children:[(0,o.jsxs)("article",{children:[(0,o.jsx)(V.A,{}),(0,o.jsx)(x.A,{}),n.mobile,(0,o.jsx)(E,{children:e}),(0,o.jsx)(j,{})]}),(0,o.jsx)(v,{})]})]}),n.desktop&&(0,o.jsx)("div",{className:"col col--3",children:n.desktop})]})}function S(e){const n=`docs-doc-id-${e.content.metadata.id}`,t=e.content;return(0,o.jsx)(r,{content:e.content,children:(0,o.jsxs)(a.e3,{className:n,children:[(0,o.jsx)(d,{}),(0,o.jsx)(G,{children:(0,o.jsx)(t,{})})]})})}},5730:(e,n,t)=>{t.d(n,{A:()=>r});t(6540);var s=t(4164),a=t(539),i=t(5279),o=t(1097),l=t(4848);function r({className:e}){const n=(0,o.r)();return n.badge?(0,l.jsx)("span",{className:(0,s.A)(e,i.G.docs.docVersionBadge,"badge badge--secondary"),children:(0,l.jsx)(a.A,{id:"theme.docs.versionBadge.label",values:{versionLabel:n.label},children:"Version: {versionLabel}"})}):null}},5833:(e,n,t)=>{t.d(n,{A:()=>x});t(6540);var s=t(4164),a=t(797),i=t(6289),o=t(539),l=t(4183),r=t(5279),c=t(7958),d=t(1097),u=t(4848);const m={unreleased:function({siteTitle:e,versionMetadata:n}){return(0,u.jsx)(o.A,{id:"theme.docs.versions.unreleasedVersionLabel",description:"The label used to tell the user that he's browsing an unreleased doc version",values:{siteTitle:e,versionLabel:(0,u.jsx)("b",{children:n.label})},children:"This is unreleased documentation for {siteTitle} {versionLabel} version."})},unmaintained:function({siteTitle:e,versionMetadata:n}){return(0,u.jsx)(o.A,{id:"theme.docs.versions.unmaintainedVersionLabel",description:"The label used to tell the user that he's browsing an unmaintained doc version",values:{siteTitle:e,versionLabel:(0,u.jsx)("b",{children:n.label})},children:"This is documentation for {siteTitle} {versionLabel}, which is no longer actively maintained."})}};function h(e){const n=m[e.versionMetadata.banner];return(0,u.jsx)(n,{...e})}function v({versionLabel:e,to:n,onClick:t}){return(0,u.jsx)(o.A,{id:"theme.docs.versions.latestVersionSuggestionLabel",description:"The label used to tell the user to check the latest version",values:{versionLabel:e,latestVersionLink:(0,u.jsx)("b",{children:(0,u.jsx)(i.A,{to:n,onClick:t,children:(0,u.jsx)(o.A,{id:"theme.docs.versions.latestVersionLinkLabel",description:"The label used for the latest version suggestion link label",children:"latest version"})})})},children:"For up-to-date documentation, see the {latestVersionLink} ({versionLabel})."})}function b({className:e,versionMetadata:n}){const{siteConfig:{title:t}}=(0,a.A)(),{pluginId:i}=(0,l.vT)({failfast:!0}),{savePreferredVersionName:o}=(0,c.g1)(i),{latestDocSuggestion:d,latestVersionSuggestion:m}=(0,l.HW)(i),b=d??(x=m).docs.find((e=>e.id===x.mainDocId));var x;return(0,u.jsxs)("div",{className:(0,s.A)(e,r.G.docs.docVersionBanner,"alert alert--warning margin-bottom--md"),role:"alert",children:[(0,u.jsx)("div",{children:(0,u.jsx)(h,{siteTitle:t,versionMetadata:n})}),(0,u.jsx)("div",{className:"margin-top--md",children:(0,u.jsx)(v,{versionLabel:m.label,to:b.path,onClick:()=>o(m.name)})})]})}function x({className:e}){const n=(0,d.r)();return n.banner?(0,u.jsx)(b,{className:e,versionMetadata:n}):null}},6239:(e,n,t)=>{t.d(n,{A:()=>r});t(6540);var s=t(4164),a=t(539),i=t(3953);const o={tags:"tags_jXut",tag:"tag_QGVx"};var l=t(4848);function r({tags:e}){return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("b",{children:(0,l.jsx)(a.A,{id:"theme.tags.tagsListLabel",description:"The label alongside a tag list",children:"Tags:"})}),(0,l.jsx)("ul",{className:(0,s.A)(o.tags,"padding--none","margin-left--sm"),children:e.map((e=>(0,l.jsx)("li",{className:o.tag,children:(0,l.jsx)(i.A,{...e})},e.permalink)))})]})}},7519:(e,n,t)=>{t.d(n,{A:()=>g});t(6540);var s=t(4164),a=t(5279),i=t(9439),o=t(3465),l=t(6289),r=t(539),c=t(9030),d=t(4848);function u(e){return(0,d.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,d.jsx)("path",{d:"M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z",fill:"currentColor"})})}const m={breadcrumbHomeIcon:"breadcrumbHomeIcon_YNFT"};function h(){const e=(0,c.Ay)("/");return(0,d.jsx)("li",{className:"breadcrumbs__item",children:(0,d.jsx)(l.A,{"aria-label":(0,r.T)({id:"theme.docs.breadcrumbs.home",message:"Home page",description:"The ARIA label for the home page in the breadcrumbs"}),className:"breadcrumbs__link",href:e,children:(0,d.jsx)(u,{className:m.breadcrumbHomeIcon})})})}const v={breadcrumbsContainer:"breadcrumbsContainer_Z_bl"};function b({children:e,href:n,isLast:t}){const s="breadcrumbs__link";return t?(0,d.jsx)("span",{className:s,itemProp:"name",children:e}):n?(0,d.jsx)(l.A,{className:s,href:n,itemProp:"item",children:(0,d.jsx)("span",{itemProp:"name",children:e})}):(0,d.jsx)("span",{className:s,children:e})}function x({children:e,active:n,index:t,addMicrodata:a}){return(0,d.jsxs)("li",{...a&&{itemScope:!0,itemProp:"itemListElement",itemType:"https://schema.org/ListItem"},className:(0,s.A)("breadcrumbs__item",{"breadcrumbs__item--active":n}),children:[e,(0,d.jsx)("meta",{itemProp:"position",content:String(t+1)})]})}function g(){const e=(0,i.OF)(),n=(0,o.Dt)();return e?(0,d.jsx)("nav",{className:(0,s.A)(a.G.docs.docBreadcrumbs,v.breadcrumbsContainer),"aria-label":(0,r.T)({id:"theme.docs.breadcrumbs.navAriaLabel",message:"Breadcrumbs",description:"The ARIA label for the breadcrumbs"}),children:(0,d.jsxs)("ul",{className:"breadcrumbs",itemScope:!0,itemType:"https://schema.org/BreadcrumbList",children:[n&&(0,d.jsx)(h,{}),e.map(((n,t)=>{const s=t===e.length-1,a="category"===n.type&&n.linkUnlisted?void 0:n.href;return(0,d.jsx)(x,{active:s,index:t,addMicrodata:!!a,children:(0,d.jsx)(b,{href:a,isLast:s,children:n.label})},t)}))]})}):null}},7959:(e,n,t)=>{t.d(n,{A:()=>c});t(6540);var s=t(4164),a=t(215);const i={tableOfContents:"tableOfContents_bqdL",docItemContainer:"docItemContainer_F8PC"};var o=t(4848);const l="table-of-contents__link toc-highlight",r="table-of-contents__link--active";function c({className:e,...n}){return(0,o.jsx)("div",{className:(0,s.A)(i.tableOfContents,"thin-scrollbar",e),children:(0,o.jsx)(a.A,{...n,linkClassName:l,linkActiveClassName:r})})}}}]);