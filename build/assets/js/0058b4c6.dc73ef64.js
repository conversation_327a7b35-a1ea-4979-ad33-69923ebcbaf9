"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[849],{6164:e=>{e.exports=JSON.parse('{"version":{"pluginId":"default","version":"current","label":"Next","banner":null,"badge":false,"noIndex":false,"className":"docs-version-current","isLast":true,"docsSidebars":{"tutorialSidebar":[{"type":"link","label":"Introduction","href":"/docs/intro","docId":"intro","unlisted":false},{"type":"category","label":"Architecture","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Architecture Presentation","href":"/docs/architecture/Architecture Presentation","docId":"architecture/Architecture Presentation","unlisted":false},{"type":"link","label":"Initial Architecture","href":"/docs/architecture/Initial Architecture","docId":"architecture/Initial Architecture","unlisted":false},{"type":"link","label":"Architecture Principles","href":"/docs/architecture/architecture_principles","docId":"architecture/architecture_principles","unlisted":false}],"href":"/docs/category/architecture"},{"type":"category","label":"Decision Records","collapsible":true,"collapsed":true,"items":[{"type":"category","label":"Global","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"1. Record architecture decisions","href":"/docs/adrs/global/record-architecture-decisions","docId":"adrs/global/record-architecture-decisions","unlisted":false},{"type":"link","label":"2. Monorepo per Project","href":"/docs/adrs/global/monorepo","docId":"adrs/global/monorepo","unlisted":false},{"type":"link","label":"3. Micro-frontends and BFF","href":"/docs/adrs/global/mfe-and-bff","docId":"adrs/global/mfe-and-bff","unlisted":false},{"type":"link","label":"4. ODRL as Policy and Entitlement Definition Language","href":"/docs/adrs/global/odrl","docId":"adrs/global/odrl","unlisted":false},{"type":"link","label":"5. OPA as Policy Evaluation Engine","href":"/docs/adrs/global/opa","docId":"adrs/global/opa","unlisted":false},{"type":"link","label":"6. Keycloak as Authentication and User Management Solution","href":"/docs/adrs/global/keycloak","docId":"adrs/global/keycloak","unlisted":false}],"href":"/docs/category/global"},{"type":"category","label":"Platform","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"1. Dapr","href":"/docs/adrs/platform/dapr","docId":"adrs/platform/dapr","unlisted":false},{"type":"link","label":"2. Dapr Pub/Sub: Apache Kafka","href":"/docs/adrs/platform/dapr-pub-sub","docId":"adrs/platform/dapr-pub-sub","unlisted":false},{"type":"link","label":"3. Dapr Secret Store: Kubernetes Secrets","href":"/docs/adrs/platform/dapr-secret-store","docId":"adrs/platform/dapr-secret-store","unlisted":false},{"type":"link","label":"4. Dapr State Store: PostgreSQL v2","href":"/docs/adrs/platform/dapr-state-management","docId":"adrs/platform/dapr-state-management","unlisted":false},{"type":"link","label":"5. Web Applications with React","href":"/docs/adrs/platform/react","docId":"adrs/platform/react","unlisted":false},{"type":"link","label":"6. Nx as Monorepo Manager and Build Tool","href":"/docs/adrs/platform/nx","docId":"adrs/platform/nx","unlisted":false},{"type":"link","label":"7. Vite as the Build Tool for React applications","href":"/docs/adrs/platform/vite","docId":"adrs/platform/vite","unlisted":false},{"type":"link","label":"4. Go Libraries and Components","href":"/docs/adrs/platform/go-libraries","docId":"adrs/platform/go-libraries","unlisted":false},{"type":"link","label":"5. Database Communication in Go","href":"/docs/adrs/platform/go-database-communication","docId":"adrs/platform/go-database-communication","unlisted":false},{"type":"link","label":"6. Feature Flags and A/B Testing Platform","href":"/docs/adrs/platform/feature-flags","docId":"adrs/platform/feature-flags","unlisted":false}],"href":"/docs/category/platform"}],"href":"/docs/category/decision-records"},{"type":"category","label":"Research","collapsible":true,"collapsed":true,"items":[{"type":"category","label":"Data Flow","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"HL7 Transmission Ingestion and Parsing Flow","href":"/docs/research/data-flow/hl7-ingestion-parsing","docId":"research/data-flow/hl7-ingestion-parsing","unlisted":false}],"href":"/docs/category/data-flow"},{"type":"category","label":"FHIR","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"FHIR Database Strategy","href":"/docs/research/fhir/","docId":"research/fhir/fhir","unlisted":false}],"href":"/docs/category/fhir"}],"href":"/docs/category/research"},{"type":"category","label":"ai-architecture","collapsible":true,"collapsed":true,"items":[{"type":"category","label":"API Reference","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"API Overview","href":"/docs/ai-architecture/api/api-overview","docId":"ai-architecture/api/api-overview","unlisted":false},{"type":"category","label":"auth","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Authentication","href":"/docs/ai-architecture/api/auth/authentication","docId":"ai-architecture/api/auth/authentication","unlisted":false},{"type":"link","label":"Authorization","href":"/docs/ai-architecture/api/auth/authorization","docId":"ai-architecture/api/auth/authorization","unlisted":false},{"type":"link","label":"API Tokens","href":"/docs/ai-architecture/api/auth/tokens","docId":"ai-architecture/api/auth/tokens","unlisted":false}]},{"type":"category","label":"data","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Data Upload","href":"/docs/ai-architecture/api/data/upload","docId":"ai-architecture/api/data/upload","unlisted":false},{"type":"link","label":"Data Management","href":"/docs/ai-architecture/api/data/management","docId":"ai-architecture/api/data/management","unlisted":false},{"type":"link","label":"Data Processing","href":"/docs/ai-architecture/api/data/processing","docId":"ai-architecture/api/data/processing","unlisted":false},{"type":"link","label":"Data Versioning","href":"/docs/ai-architecture/api/data/versioning","docId":"ai-architecture/api/data/versioning","unlisted":false},{"type":"link","label":"Data Validation","href":"/docs/ai-architecture/api/data/validation","docId":"ai-architecture/api/data/validation","unlisted":false},{"type":"link","label":"Data Preprocessing","href":"/docs/ai-architecture/api/data/preprocessing","docId":"ai-architecture/api/data/preprocessing","unlisted":false},{"type":"link","label":"Data Augmentation","href":"/docs/ai-architecture/api/data/augmentation","docId":"ai-architecture/api/data/augmentation","unlisted":false},{"type":"link","label":"Data Export","href":"/docs/ai-architecture/api/data/export","docId":"ai-architecture/api/data/export","unlisted":false},{"type":"link","label":"Data Statistics","href":"/docs/ai-architecture/api/data/statistics","docId":"ai-architecture/api/data/statistics","unlisted":false},{"type":"link","label":"Data Monitoring","href":"/docs/ai-architecture/api/data/monitoring","docId":"ai-architecture/api/data/monitoring","unlisted":false},{"type":"link","label":"Data Governance","href":"/docs/ai-architecture/api/data/governance","docId":"ai-architecture/api/data/governance","unlisted":false}]},{"type":"category","label":"models","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"List Models","href":"/docs/ai-architecture/api/models/list-models","docId":"ai-architecture/api/models/list-models","unlisted":false},{"type":"link","label":"Model Details","href":"/docs/ai-architecture/api/models/model-details","docId":"ai-architecture/api/models/model-details","unlisted":false},{"type":"link","label":"Model Predictions","href":"/docs/ai-architecture/api/models/predictions","docId":"ai-architecture/api/models/predictions","unlisted":false}]},{"type":"category","label":"monitoring","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Monitoring Alerts","href":"/docs/ai-architecture/api/monitoring/alerts","docId":"ai-architecture/api/monitoring/alerts","unlisted":false},{"type":"link","label":"Monitoring Logs","href":"/docs/ai-architecture/api/monitoring/logs","docId":"ai-architecture/api/monitoring/logs","unlisted":false},{"type":"link","label":"Monitoring Metrics","href":"/docs/ai-architecture/api/monitoring/metrics","docId":"ai-architecture/api/monitoring/metrics","unlisted":false},{"type":"link","label":"Monitoring Dashboards","href":"/docs/ai-architecture/api/monitoring/dashboards","docId":"ai-architecture/api/monitoring/dashboards","unlisted":false},{"type":"link","label":"Monitoring Reports","href":"/docs/ai-architecture/api/monitoring/reports","docId":"ai-architecture/api/monitoring/reports","unlisted":false}]}],"href":"/docs/ai-architecture/api/"},{"type":"category","label":"Implementation Guide","collapsible":true,"collapsed":true,"items":[{"type":"category","label":"Model Development","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Experiment Tracking Implementation","href":"/docs/ai-architecture/implementation/model-development/experiment-tracking/","docId":"ai-architecture/implementation/model-development/experiment-tracking/index","unlisted":false},{"type":"link","label":"ML Pipeline Orchestration","href":"/docs/ai-architecture/implementation/model-development/ml-pipeline/","docId":"ai-architecture/implementation/model-development/ml-pipeline/index","unlisted":false},{"type":"link","label":"Model Explainability Implementation","href":"/docs/ai-architecture/implementation/model-development/model-explainability/","docId":"ai-architecture/implementation/model-development/model-explainability/index","unlisted":false},{"type":"link","label":"Model Testing Implementation","href":"/docs/ai-architecture/implementation/model-development/model-testing/","docId":"ai-architecture/implementation/model-development/model-testing/index","unlisted":false}],"href":"/docs/ai-architecture/implementation/model-development/"},{"type":"category","label":"data-catalog-lineage-versioning","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Data Catalog Implementation","href":"/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_catalog","docId":"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_catalog","unlisted":false},{"type":"link","label":"Data Lineage Implementation","href":"/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_lineage","docId":"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_lineage","unlisted":false},{"type":"link","label":"Data Versioning Implementation","href":"/docs/ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning","docId":"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning","unlisted":false}]},{"type":"category","label":"feature-store","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Feature Monitoring Implementation","href":"/docs/ai-architecture/implementation/feature-store/minimalistic_feature_monitoring","docId":"ai-architecture/implementation/feature-store/minimalistic_feature_monitoring","unlisted":false},{"type":"link","label":"Feature Store Implementation","href":"/docs/ai-architecture/implementation/feature-store/minimalistic_feature_store","docId":"ai-architecture/implementation/feature-store/minimalistic_feature_store","unlisted":false}]}],"href":"/docs/ai-architecture/implementation/"},{"type":"link","label":"Introduction","href":"/docs/ai-architecture/intro","docId":"ai-architecture/intro","unlisted":false},{"type":"link","label":"Support","href":"/docs/ai-architecture/support/","docId":"ai-architecture/support/index","unlisted":false},{"type":"link","label":"Architecture Overview","href":"/docs/ai-architecture/architecture-overview/","docId":"ai-architecture/architecture-overview/index","unlisted":false},{"type":"link","label":"R&D Platform Architecture Requirements","href":"/docs/ai-architecture/architecture-requirements/","docId":"ai-architecture/architecture-requirements/index","unlisted":false},{"type":"category","label":"best-practices","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Development Best Practices","href":"/docs/ai-architecture/best-practices/development/","docId":"ai-architecture/best-practices/development/index","unlisted":false},{"type":"link","label":"Monitoring Best Practices","href":"/docs/ai-architecture/best-practices/monitoring/","docId":"ai-architecture/best-practices/monitoring/index","unlisted":false},{"type":"link","label":"Testing Best Practices","href":"/docs/ai-architecture/best-practices/testing/","docId":"ai-architecture/best-practices/testing/index","unlisted":false}]},{"type":"category","label":"components","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"AI Models","href":"/docs/ai-architecture/components/ai-models/","docId":"ai-architecture/components/ai-models/index","unlisted":false},{"type":"link","label":"API Gateway","href":"/docs/ai-architecture/components/api-gateway/","docId":"ai-architecture/components/api-gateway/index","unlisted":false},{"type":"link","label":"Data Pipeline","href":"/docs/ai-architecture/components/data-pipeline/","docId":"ai-architecture/components/data-pipeline/index","unlisted":false},{"type":"link","label":"Monitoring","href":"/docs/ai-architecture/components/monitoring/","docId":"ai-architecture/components/monitoring/index","unlisted":false}]},{"type":"category","label":"deployment","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Cloud Providers","href":"/docs/ai-architecture/deployment/cloud-providers/","docId":"ai-architecture/deployment/cloud-providers/index","unlisted":false},{"type":"link","label":"Kubernetes Deployment","href":"/docs/ai-architecture/deployment/kubernetes/","docId":"ai-architecture/deployment/kubernetes/index","unlisted":false},{"type":"link","label":"Scaling","href":"/docs/ai-architecture/deployment/scaling/","docId":"ai-architecture/deployment/scaling/index","unlisted":false}]},{"type":"category","label":"security","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Authentication","href":"/docs/ai-architecture/security/authentication/","docId":"ai-architecture/security/authentication/index","unlisted":false},{"type":"link","label":"Authorization","href":"/docs/ai-architecture/security/authorization/","docId":"ai-architecture/security/authorization/index","unlisted":false},{"type":"link","label":"Data Protection","href":"/docs/ai-architecture/security/data-protection/","docId":"ai-architecture/security/data-protection/index","unlisted":false}]},{"type":"link","label":"System Requirements","href":"/docs/ai-architecture/system-requirements/","docId":"ai-architecture/system-requirements/index","unlisted":false},{"type":"category","label":"tools","collapsible":true,"collapsed":true,"items":[{"type":"category","label":"old","collapsible":true,"collapsed":true,"items":[{"type":"category","label":"API Overview","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"MLOps Platform API Endpoints","href":"/docs/ai-architecture/tools/old/api/api-endpoints","docId":"ai-architecture/tools/old/api/api-endpoints","unlisted":false},{"type":"category","label":"Data Management","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Data Quality API","href":"/docs/ai-architecture/tools/old/api/data-management/data-quality-api","docId":"ai-architecture/tools/old/api/data-management/data-quality-api","unlisted":false},{"type":"link","label":"Feature Store API","href":"/docs/ai-architecture/tools/old/api/data-management/feature-store-api","docId":"ai-architecture/tools/old/api/data-management/feature-store-api","unlisted":false},{"type":"link","label":"LakeFS API","href":"/docs/ai-architecture/tools/old/api/data-management/lakefs-api","docId":"ai-architecture/tools/old/api/data-management/lakefs-api","unlisted":false},{"type":"link","label":"MinIO API","href":"/docs/ai-architecture/tools/old/api/data-management/minio-api","docId":"ai-architecture/tools/old/api/data-management/minio-api","unlisted":false},{"type":"link","label":"OpenMetadata API","href":"/docs/ai-architecture/tools/old/api/data-management/openmetadata-api","docId":"ai-architecture/tools/old/api/data-management/openmetadata-api","unlisted":false},{"type":"link","label":"Trino API","href":"/docs/ai-architecture/tools/old/api/data-management/trino-api","docId":"ai-architecture/tools/old/api/data-management/trino-api","unlisted":false}],"href":"/docs/ai-architecture/tools/old/api/data-management/"},{"type":"category","label":"Model Management","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Experiment Tracking API","href":"/docs/ai-architecture/tools/old/api/model-management/experiment-tracking-api","docId":"ai-architecture/tools/old/api/model-management/experiment-tracking-api","unlisted":false},{"type":"link","label":"Model Registry API","href":"/docs/ai-architecture/tools/old/api/model-management/model-registry-api","docId":"ai-architecture/tools/old/api/model-management/model-registry-api","unlisted":false},{"type":"link","label":"Model Serving API","href":"/docs/ai-architecture/tools/old/api/model-management/model-serving-api","docId":"ai-architecture/tools/old/api/model-management/model-serving-api","unlisted":false}],"href":"/docs/ai-architecture/tools/old/api/model-management/"},{"type":"category","label":"Monitoring","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Alerting API","href":"/docs/ai-architecture/tools/old/api/monitoring/alerting-api","docId":"ai-architecture/tools/old/api/monitoring/alerting-api","unlisted":false},{"type":"link","label":"Monitoring API","href":"/docs/ai-architecture/tools/old/api/monitoring/monitoring-api","docId":"ai-architecture/tools/old/api/monitoring/monitoring-api","unlisted":false}],"href":"/docs/ai-architecture/tools/old/api/monitoring/"},{"type":"category","label":"Orchestration","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"KServe API","href":"/docs/ai-architecture/tools/old/api/orchestration/kserve-api","docId":"ai-architecture/tools/old/api/orchestration/kserve-api","unlisted":false},{"type":"link","label":"Kubeflow API","href":"/docs/ai-architecture/tools/old/api/orchestration/kubeflow-api","docId":"ai-architecture/tools/old/api/orchestration/kubeflow-api","unlisted":false},{"type":"link","label":"MLflow API","href":"/docs/ai-architecture/tools/old/api/orchestration/mlflow-api","docId":"ai-architecture/tools/old/api/orchestration/mlflow-api","unlisted":false}],"href":"/docs/ai-architecture/tools/old/api/orchestration/"},{"type":"category","label":"Visualization","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"API Diagrams","href":"/docs/ai-architecture/tools/old/api/visualization/api-diagrams","docId":"ai-architecture/tools/old/api/visualization/api-diagrams","unlisted":false}],"href":"/docs/ai-architecture/tools/old/api/visualization/"}],"href":"/docs/ai-architecture/tools/old/api/"},{"type":"category","label":"architecture","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"R&D Detailed Scenarios and Flows","href":"/docs/ai-architecture/tools/old/architecture/medical-device-detailed-scenarios","docId":"ai-architecture/tools/old/architecture/medical-device-detailed-scenarios","unlisted":false},{"type":"link","label":"Medical Device ML Platform Architecture","href":"/docs/ai-architecture/tools/old/architecture/medical-device-ml-platform","docId":"ai-architecture/tools/old/architecture/medical-device-ml-platform","unlisted":false},{"type":"link","label":"Medical Device R&D Process Flows","href":"/docs/ai-architecture/tools/old/architecture/medical-device-process-flows","docId":"ai-architecture/tools/old/architecture/medical-device-process-flows","unlisted":false},{"type":"link","label":"R&D Conceptual Architecture","href":"/docs/ai-architecture/tools/old/architecture/medical-device-rd-conceptual","docId":"ai-architecture/tools/old/architecture/medical-device-rd-conceptual","unlisted":false},{"type":"link","label":"R&D Detailed Architecture","href":"/docs/ai-architecture/tools/old/architecture/medical-device-rd-detailed","docId":"ai-architecture/tools/old/architecture/medical-device-rd-detailed","unlisted":false},{"type":"link","label":"R&D Platform Architecture","href":"/docs/ai-architecture/tools/old/architecture/medical-device-rd-platform","docId":"ai-architecture/tools/old/architecture/medical-device-rd-platform","unlisted":false},{"type":"link","label":"Medical Device R&D Platform Technical Specification","href":"/docs/ai-architecture/tools/old/architecture/medical-device-rd-technical-spec","docId":"ai-architecture/tools/old/architecture/medical-device-rd-technical-spec","unlisted":false}]},{"type":"category","label":"mlops","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"MLOps Components","href":"/docs/ai-architecture/tools/old/mlops/mlops-components","docId":"ai-architecture/tools/old/mlops/mlops-components","unlisted":false}]},{"type":"category","label":"research","collapsible":true,"collapsed":true,"items":[{"type":"link","label":"Research Tools and Workflows","href":"/docs/ai-architecture/tools/old/research/research-tools-workflows","docId":"ai-architecture/tools/old/research/research-tools-workflows","unlisted":false}]}]}],"href":"/docs/ai-architecture/tools/"}]}]},"docs":{"adrs/global/keycloak":{"id":"adrs/global/keycloak","title":"6. Keycloak as Authentication and User Management Solution","description":"Date: 2025-03-17","sidebar":"tutorialSidebar"},"adrs/global/mfe-and-bff":{"id":"adrs/global/mfe-and-bff","title":"3. Micro-frontends and BFF","description":"Date: 2025-03-10","sidebar":"tutorialSidebar"},"adrs/global/monorepo":{"id":"adrs/global/monorepo","title":"2. Monorepo per Project","description":"Date: 2025-02-10","sidebar":"tutorialSidebar"},"adrs/global/odrl":{"id":"adrs/global/odrl","title":"4. ODRL as Policy and Entitlement Definition Language","description":"Date: 2025-03-17","sidebar":"tutorialSidebar"},"adrs/global/opa":{"id":"adrs/global/opa","title":"5. OPA as Policy Evaluation Engine","description":"Date: 2025-03-17","sidebar":"tutorialSidebar"},"adrs/global/record-architecture-decisions":{"id":"adrs/global/record-architecture-decisions","title":"1. Record architecture decisions","description":"Date: 2025-01-23","sidebar":"tutorialSidebar"},"adrs/platform/dapr":{"id":"adrs/platform/dapr","title":"1. Dapr","description":"Date: 2025-01-23","sidebar":"tutorialSidebar"},"adrs/platform/dapr-pub-sub":{"id":"adrs/platform/dapr-pub-sub","title":"2. Dapr Pub/Sub: Apache Kafka","description":"Date: 2025-02-05","sidebar":"tutorialSidebar"},"adrs/platform/dapr-secret-store":{"id":"adrs/platform/dapr-secret-store","title":"3. Dapr Secret Store: Kubernetes Secrets","description":"Date: 2025-02-05","sidebar":"tutorialSidebar"},"adrs/platform/dapr-state-management":{"id":"adrs/platform/dapr-state-management","title":"4. Dapr State Store: PostgreSQL v2","description":"Date: 2025-02-05","sidebar":"tutorialSidebar"},"adrs/platform/feature-flags":{"id":"adrs/platform/feature-flags","title":"6. Feature Flags and A/B Testing Platform","description":"Date: 2024-03-19","sidebar":"tutorialSidebar"},"adrs/platform/go-database-communication":{"id":"adrs/platform/go-database-communication","title":"5. Database Communication in Go","description":"Date: 2024-03-19","sidebar":"tutorialSidebar"},"adrs/platform/go-libraries":{"id":"adrs/platform/go-libraries","title":"4. Go Libraries and Components","description":"Date: 2024-03-19","sidebar":"tutorialSidebar"},"adrs/platform/nx":{"id":"adrs/platform/nx","title":"6. Nx as Monorepo Manager and Build Tool","description":"Date: 2025-03-10","sidebar":"tutorialSidebar"},"adrs/platform/react":{"id":"adrs/platform/react","title":"5. Web Applications with React","description":"Date: 2025-03-05","sidebar":"tutorialSidebar"},"adrs/platform/vite":{"id":"adrs/platform/vite","title":"7. Vite as the Build Tool for React applications","description":"Date: 2025-03-10","sidebar":"tutorialSidebar"},"ai-architecture/api/api-overview":{"id":"ai-architecture/api/api-overview","title":"API Overview","description":"Welcome to the AI Platform API documentation. This section provides comprehensive information about our API endpoints, authentication, and usage.","sidebar":"tutorialSidebar"},"ai-architecture/api/auth/authentication":{"id":"ai-architecture/api/auth/authentication","title":"Authentication","description":"This guide explains how to authenticate with the AI Platform API.","sidebar":"tutorialSidebar"},"ai-architecture/api/auth/authorization":{"id":"ai-architecture/api/auth/authorization","title":"Authorization","description":"Manage user roles, permissions, and access control.","sidebar":"tutorialSidebar"},"ai-architecture/api/auth/tokens":{"id":"ai-architecture/api/auth/tokens","title":"API Tokens","description":"Manage API tokens for authentication and access control.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/augmentation":{"id":"ai-architecture/api/data/augmentation","title":"Data Augmentation","description":"Enhance your datasets through various augmentation techniques.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/export":{"id":"ai-architecture/api/data/export","title":"Data Export","description":"Export your datasets in various formats for different use cases.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/governance":{"id":"ai-architecture/api/data/governance","title":"Data Governance","description":"Manage data access, compliance, and security policies.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/management":{"id":"ai-architecture/api/data/management","title":"Data Management","description":"Manage and organize your datasets effectively.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/monitoring":{"id":"ai-architecture/api/data/monitoring","title":"Data Monitoring","description":"Monitor and track changes in your datasets over time.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/preprocessing":{"id":"ai-architecture/api/data/preprocessing","title":"Data Preprocessing","description":"Transform and prepare your data for model training and inference.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/processing":{"id":"ai-architecture/api/data/processing","title":"Data Processing","description":"Process and transform your datasets using configurable pipelines.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/statistics":{"id":"ai-architecture/api/data/statistics","title":"Data Statistics","description":"Analyze and understand your datasets through comprehensive statistics.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/upload":{"id":"ai-architecture/api/data/upload","title":"Data Upload","description":"Upload and manage your training and inference data.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/validation":{"id":"ai-architecture/api/data/validation","title":"Data Validation","description":"Validate and ensure the quality of your datasets.","sidebar":"tutorialSidebar"},"ai-architecture/api/data/versioning":{"id":"ai-architecture/api/data/versioning","title":"Data Versioning","description":"Track and manage different versions of your datasets.","sidebar":"tutorialSidebar"},"ai-architecture/api/index":{"id":"ai-architecture/api/index","title":"API Reference","description":"Welcome to the 91.life AI Platform API documentation. This section provides comprehensive documentation for all available API endpoints, authentication methods, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/api/models/list-models":{"id":"ai-architecture/api/models/list-models","title":"List Models","description":"Retrieve a list of all available AI models in your account.","sidebar":"tutorialSidebar"},"ai-architecture/api/models/model-details":{"id":"ai-architecture/api/models/model-details","title":"Model Details","description":"Retrieve detailed information about a specific AI model.","sidebar":"tutorialSidebar"},"ai-architecture/api/models/predictions":{"id":"ai-architecture/api/models/predictions","title":"Model Predictions","description":"Make predictions using your AI models.","sidebar":"tutorialSidebar"},"ai-architecture/api/monitoring/alerts":{"id":"ai-architecture/api/monitoring/alerts","title":"Monitoring Alerts","description":"Configure and manage monitoring alerts for your AI models and data.","sidebar":"tutorialSidebar"},"ai-architecture/api/monitoring/dashboards":{"id":"ai-architecture/api/monitoring/dashboards","title":"Monitoring Dashboards","description":"Create and manage custom monitoring dashboards.","sidebar":"tutorialSidebar"},"ai-architecture/api/monitoring/logs":{"id":"ai-architecture/api/monitoring/logs","title":"Monitoring Logs","description":"Access and analyze system logs for monitoring and debugging.","sidebar":"tutorialSidebar"},"ai-architecture/api/monitoring/metrics":{"id":"ai-architecture/api/monitoring/metrics","title":"Monitoring Metrics","description":"Track and analyze system and model performance metrics.","sidebar":"tutorialSidebar"},"ai-architecture/api/monitoring/reports":{"id":"ai-architecture/api/monitoring/reports","title":"Monitoring Reports","description":"Generate and schedule monitoring reports.","sidebar":"tutorialSidebar"},"ai-architecture/architecture-overview/index":{"id":"ai-architecture/architecture-overview/index","title":"Architecture Overview","description":"The R&D Platform is engineered with a modular and scalable architecture, specifically designed to efficiently address the comprehensive data processing and machine learning requirements for heart implant manufacturers. A core tenet of our design is a minimalistic reliance on external, opinionated MLOps frameworks, prioritizing custom-built solutions and foundational cloud services to achieve greater control, adaptability, and direct integration with our stringent FDA and HIPAA compliance needs.","sidebar":"tutorialSidebar"},"ai-architecture/architecture-requirements/index":{"id":"ai-architecture/architecture-requirements/index","title":"R&D Platform Architecture Requirements","description":"This document outlines the architectural vision for our R&D Platform, designed to empower organizations with robust data processing and machine learning capabilities.","sidebar":"tutorialSidebar"},"ai-architecture/best-practices/development/index":{"id":"ai-architecture/best-practices/development/index","title":"Development Best Practices","description":"This guide covers best practices for developing on the AI Platform, including coding standards, testing, and deployment practices.","sidebar":"tutorialSidebar"},"ai-architecture/best-practices/monitoring/index":{"id":"ai-architecture/best-practices/monitoring/index","title":"Monitoring Best Practices","description":"This guide covers best practices for monitoring the AI Platform, including metrics, alerts, and observability.","sidebar":"tutorialSidebar"},"ai-architecture/best-practices/testing/index":{"id":"ai-architecture/best-practices/testing/index","title":"Testing Best Practices","description":"This guide covers best practices for testing the AI Platform, including test strategies, automation, and quality assurance.","sidebar":"tutorialSidebar"},"ai-architecture/components/ai-models/index":{"id":"ai-architecture/components/ai-models/index","title":"AI Models","description":"The AI Models component is the core of the platform, providing comprehensive support for model development, training, deployment, and monitoring.","sidebar":"tutorialSidebar"},"ai-architecture/components/api-gateway/index":{"id":"ai-architecture/components/api-gateway/index","title":"API Gateway","description":"The API Gateway component provides a unified interface for accessing platform services, handling authentication, authorization, and request routing.","sidebar":"tutorialSidebar"},"ai-architecture/components/data-pipeline/index":{"id":"ai-architecture/components/data-pipeline/index","title":"Data Pipeline","description":"The Data Pipeline component manages the entire lifecycle of data within the platform, from ingestion to processing and storage.","sidebar":"tutorialSidebar"},"ai-architecture/components/monitoring/index":{"id":"ai-architecture/components/monitoring/index","title":"Monitoring","description":"The Monitoring component provides comprehensive observability for the platform, tracking system health, performance, and usage.","sidebar":"tutorialSidebar"},"ai-architecture/deployment/cloud-providers/index":{"id":"ai-architecture/deployment/cloud-providers/index","title":"Cloud Providers","description":"This guide covers deploying the AI Platform on major cloud providers, including AWS, GCP, and Azure.","sidebar":"tutorialSidebar"},"ai-architecture/deployment/kubernetes/index":{"id":"ai-architecture/deployment/kubernetes/index","title":"Kubernetes Deployment","description":"This guide covers deploying the AI Platform on Kubernetes, including cluster setup, service deployment, and management.","sidebar":"tutorialSidebar"},"ai-architecture/deployment/scaling/index":{"id":"ai-architecture/deployment/scaling/index","title":"Scaling","description":"This guide covers scaling strategies for the AI Platform, including horizontal and vertical scaling, load balancing, and performance optimization.","sidebar":"tutorialSidebar"},"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_catalog":{"id":"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_catalog","title":"Data Catalog Implementation","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_lineage":{"id":"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_lineage","title":"Data Lineage Implementation","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning":{"id":"ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning","title":"Data Versioning Implementation","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/implementation/feature-store/minimalistic_feature_monitoring":{"id":"ai-architecture/implementation/feature-store/minimalistic_feature_monitoring","title":"Feature Monitoring Implementation","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/implementation/feature-store/minimalistic_feature_store":{"id":"ai-architecture/implementation/feature-store/minimalistic_feature_store","title":"Feature Store Implementation","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/implementation/index":{"id":"ai-architecture/implementation/index","title":"Implementation Guide","description":"This section provides comprehensive documentation for implementing the 91.life AI Platform. Our implementation follows a minimalistic approach, focusing on custom-built solutions that provide maximum control and flexibility while maintaining compliance with healthcare regulations.","sidebar":"tutorialSidebar"},"ai-architecture/implementation/model-development/experiment-tracking/index":{"id":"ai-architecture/implementation/model-development/experiment-tracking/index","title":"Experiment Tracking Implementation","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/implementation/model-development/index":{"id":"ai-architecture/implementation/model-development/index","title":"Model Development","description":"This section covers the implementation details of our model development pipeline, from experiment tracking to model testing and explainability.","sidebar":"tutorialSidebar"},"ai-architecture/implementation/model-development/ml-pipeline/index":{"id":"ai-architecture/implementation/model-development/ml-pipeline/index","title":"ML Pipeline Orchestration","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/implementation/model-development/model-explainability/index":{"id":"ai-architecture/implementation/model-development/model-explainability/index","title":"Model Explainability Implementation","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/implementation/model-development/model-testing/index":{"id":"ai-architecture/implementation/model-development/model-testing/index","title":"Model Testing Implementation","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/intro":{"id":"ai-architecture/intro","title":"Introduction","description":"Welcome! This documentation is your go-to guide for understanding the architecture behind our R&D Platform. Here, you\'ll find clear explanations about how our artificial intelligence systems are designed, how the different parts fit together, and the strategies we use to keep everything running smoothly.","sidebar":"tutorialSidebar"},"ai-architecture/security/authentication/index":{"id":"ai-architecture/security/authentication/index","title":"Authentication","description":"This guide covers authentication mechanisms for the AI Platform, including user authentication, API authentication, and security best practices.","sidebar":"tutorialSidebar"},"ai-architecture/security/authorization/index":{"id":"ai-architecture/security/authorization/index","title":"Authorization","description":"This guide covers authorization mechanisms for the AI Platform, including role-based access control, permission management, and security policies.","sidebar":"tutorialSidebar"},"ai-architecture/security/data-protection/index":{"id":"ai-architecture/security/data-protection/index","title":"Data Protection","description":"This guide covers data protection mechanisms for the AI Platform, including encryption, data masking, and security best practices.","sidebar":"tutorialSidebar"},"ai-architecture/support/index":{"id":"ai-architecture/support/index","title":"Support","description":"Contact Information","sidebar":"tutorialSidebar"},"ai-architecture/system-requirements/index":{"id":"ai-architecture/system-requirements/index","title":"System Requirements","description":"This section outlines the hardware and software requirements for running the AI Platform.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/api":{"id":"ai-architecture/tools/old/api/api","title":"API Documentation","description":"Welcome to the MLOps platform API documentation. This documentation provides detailed information about all available APIs, their endpoints, authentication methods, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/api-endpoints":{"id":"ai-architecture/tools/old/api/api-endpoints","title":"MLOps Platform API Endpoints","description":"This document provides a comprehensive list of all available endpoints for the MLOps platform services.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/data-management/data-management":{"id":"ai-architecture/tools/old/api/data-management/data-management","title":"Data Management APIs","description":"This section contains documentation for APIs related to data management, including feature stores, data quality, and storage systems. These APIs provide comprehensive functionality for managing data throughout the machine learning lifecycle.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/data-management/data-quality-api":{"id":"ai-architecture/tools/old/api/data-management/data-quality-api","title":"Data Quality API Documentation","description":"The Data Quality API provides endpoints for validating, monitoring, and ensuring data quality across the platform. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/data-management/feature-store-api":{"id":"ai-architecture/tools/old/api/data-management/feature-store-api","title":"Feature Store API Documentation","description":"The Feature Store provides a RESTful API for managing and serving ML features. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/data-management/lakefs-api":{"id":"ai-architecture/tools/old/api/data-management/lakefs-api","title":"LakeFS API Documentation","description":"LakeFS provides a RESTful API for managing data versioning and branching in your data lake. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/data-management/minio-api":{"id":"ai-architecture/tools/old/api/data-management/minio-api","title":"MinIO API Documentation","description":"MinIO provides an S3-compatible API for object storage operations. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/data-management/openmetadata-api":{"id":"ai-architecture/tools/old/api/data-management/openmetadata-api","title":"OpenMetadata API Documentation","description":"OpenMetadata provides a RESTful API for managing metadata and data lineage. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/data-management/trino-api":{"id":"ai-architecture/tools/old/api/data-management/trino-api","title":"Trino API Documentation","description":"Trino provides a RESTful API for executing SQL queries and managing query execution. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/model-management/experiment-tracking-api":{"id":"ai-architecture/tools/old/api/model-management/experiment-tracking-api","title":"Experiment Tracking API Documentation","description":"The Experiment Tracking API provides endpoints for managing machine learning experiments, tracking metrics, and comparing model performance. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/model-management/model-management":{"id":"ai-architecture/tools/old/api/model-management/model-management","title":"Model Management APIs","description":"This section contains documentation for APIs related to model management, including model serving, registry, and experiment tracking. These APIs provide comprehensive functionality for managing the entire machine learning model lifecycle.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/model-management/model-registry-api":{"id":"ai-architecture/tools/old/api/model-management/model-registry-api","title":"Model Registry API Documentation","description":"The Model Registry API provides endpoints for managing model versions, tracking model lineage, and managing model deployments. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/model-management/model-serving-api":{"id":"ai-architecture/tools/old/api/model-management/model-serving-api","title":"Model Serving API Documentation","description":"The Model Serving API provides endpoints for serving machine learning models, managing inference requests, and monitoring model performance. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/monitoring/alerting-api":{"id":"ai-architecture/tools/old/api/monitoring/alerting-api","title":"Alerting API Documentation","description":"The Alerting API provides endpoints for managing alerts, notifications, and incident response. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/monitoring/monitoring":{"id":"ai-architecture/tools/old/api/monitoring/monitoring","title":"Monitoring APIs","description":"This section contains documentation for APIs related to monitoring and alerting in the MLOps platform. These APIs provide comprehensive functionality for monitoring model performance, system health, and data quality.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/monitoring/monitoring-api":{"id":"ai-architecture/tools/old/api/monitoring/monitoring-api","title":"Monitoring API Documentation","description":"The Monitoring API provides endpoints for tracking model performance, data quality, and system health metrics. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/orchestration/kserve-api":{"id":"ai-architecture/tools/old/api/orchestration/kserve-api","title":"KServe API Documentation","description":"KServe provides a standardized API for model serving and inference in Kubernetes. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/orchestration/kubeflow-api":{"id":"ai-architecture/tools/old/api/orchestration/kubeflow-api","title":"Kubeflow API Documentation","description":"Kubeflow provides a comprehensive API for managing ML pipelines, notebooks, and model serving in Kubernetes. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/orchestration/mlflow-api":{"id":"ai-architecture/tools/old/api/orchestration/mlflow-api","title":"MLflow API Documentation","description":"MLflow provides a RESTful API for experiment tracking, model registry, and model serving. This documentation covers all available endpoints, authentication, and usage examples.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/orchestration/orchestration":{"id":"ai-architecture/tools/old/api/orchestration/orchestration","title":"Orchestration APIs","description":"This section contains documentation for APIs related to workflow orchestration and pipeline management. These APIs provide comprehensive functionality for managing machine learning workflows, model serving, and experiment tracking.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/visualization/api-diagrams":{"id":"ai-architecture/tools/old/api/visualization/api-diagrams","title":"API Interaction Diagrams","description":"This page contains diagrams illustrating the interactions between different components of the MLOps platform.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/api/visualization/visualization":{"id":"ai-architecture/tools/old/api/visualization/visualization","title":"Visualization","description":"This section contains diagrams and visual documentation for the MLOps platform. These resources help understand the architecture, workflows, and interactions between different components of the platform.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/architecture/medical-device-detailed-scenarios":{"id":"ai-architecture/tools/old/architecture/medical-device-detailed-scenarios","title":"R&D Detailed Scenarios and Flows","description":"1. Research and Development Detailed Scenarios","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/architecture/medical-device-ml-platform":{"id":"ai-architecture/tools/old/architecture/medical-device-ml-platform","title":"Medical Device ML Platform Architecture","description":"Overview","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/architecture/medical-device-process-flows":{"id":"ai-architecture/tools/old/architecture/medical-device-process-flows","title":"Medical Device R&D Process Flows","description":"1. Research and Development Process","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/architecture/medical-device-rd-conceptual":{"id":"ai-architecture/tools/old/architecture/medical-device-rd-conceptual","title":"R&D Conceptual Architecture","description":"1. System Overview","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/architecture/medical-device-rd-detailed":{"id":"ai-architecture/tools/old/architecture/medical-device-rd-detailed","title":"R&D Detailed Architecture","description":"1. System Architecture Details","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/architecture/medical-device-rd-platform":{"id":"ai-architecture/tools/old/architecture/medical-device-rd-platform","title":"R&D Platform Architecture","description":"R&D Platform Architecture","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/architecture/medical-device-rd-technical-spec":{"id":"ai-architecture/tools/old/architecture/medical-device-rd-technical-spec","title":"Medical Device R&D Platform Technical Specification","description":"1. Research Environment Technical Details","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/mlops/mlops-components":{"id":"ai-architecture/tools/old/mlops/mlops-components","title":"MLOps Components","description":"This guide provides a comprehensive overview of the components that make up a production-grade MLOps platform, categorizing them as either essential (MUST HAVE) or optional based on your specific needs.","sidebar":"tutorialSidebar"},"ai-architecture/tools/old/research/research-tools-workflows":{"id":"ai-architecture/tools/old/research/research-tools-workflows","title":"Research Tools and Workflows","description":"1. Research Environment Setup","sidebar":"tutorialSidebar"},"ai-architecture/tools/tools":{"id":"ai-architecture/tools/tools","title":"tools","description":"Tooling and Libraries","sidebar":"tutorialSidebar"},"architecture/Architecture Presentation":{"id":"architecture/Architecture Presentation","title":"Architecture Presentation","description":"Transforming Healthcare Through Adaptive Technology","sidebar":"tutorialSidebar"},"architecture/architecture_principles":{"id":"architecture/architecture_principles","title":"Architecture Principles","description":"This document outlines the core architectural principles that must guide all development and design of our SaaS platform. These principles are fundamental to maintaining a robust, maintainable, and scalable system.","sidebar":"tutorialSidebar"},"architecture/Initial Architecture":{"id":"architecture/Initial Architecture","title":"Initial Architecture","description":"|  Heart+ Rewrite Initial Architecture |  |","sidebar":"tutorialSidebar"},"intro":{"id":"intro","title":"Introduction","description":"Documentation Structure","sidebar":"tutorialSidebar"},"research/data-flow/hl7-ingestion-parsing":{"id":"research/data-flow/hl7-ingestion-parsing","title":"HL7 Transmission Ingestion and Parsing Flow","description":"This document details the flow for ingesting and parsing HL7 transmissions and subsequently outputting those HL7 messages to be consumed by other applications. It also includes the addition of a consumer application designed to save the messages to a database for reprocessing or auditing purposes.","sidebar":"tutorialSidebar"},"research/fhir/fhir":{"id":"research/fhir/fhir","title":"FHIR Database Strategy","description":"1. Introduction","sidebar":"tutorialSidebar"}}}}')}}]);