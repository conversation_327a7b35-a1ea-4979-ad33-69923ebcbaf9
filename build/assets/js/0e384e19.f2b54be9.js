"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3976],{7879:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>l,contentTitle:()=>i,default:()=>h,frontMatter:()=>c,metadata:()=>r,toc:()=>d});const r=JSON.parse('{"id":"intro","title":"Introduction","description":"Documentation Structure","source":"@site/docs/intro.md","sourceDirName":".","slug":"/intro","permalink":"/docs/intro","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/intro.md","tags":[],"version":"current","sidebarPosition":0,"frontMatter":{"sidebar_position":0},"sidebar":"tutorialSidebar","next":{"title":"Architecture","permalink":"/docs/category/architecture"}}');var s=n(4848),o=n(8453);const c={sidebar_position:0},i="Introduction",l={},d=[{value:"Documentation Structure",id:"documentation-structure",level:2},{value:"Adr Tools",id:"adr-tools",level:2},{value:"PlantUML",id:"plantuml",level:2}];function a(e){const t={a:"a",code:"code",h1:"h1",h2:"h2",header:"header",img:"img",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,o.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.header,{children:(0,s.jsx)(t.h1,{id:"introduction",children:"Introduction"})}),"\n",(0,s.jsx)(t.h2,{id:"documentation-structure",children:"Documentation Structure"}),"\n",(0,s.jsx)(t.p,{children:"This is the documentation for the 91 architecture. It is divided into several sections:"}),"\n",(0,s.jsxs)(t.ul,{children:["\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:(0,s.jsx)(t.a,{href:"/docs/category/architecture",children:"Architecture"})}),": This section contains the finalized architecture documents of the\n91 project."]}),"\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:(0,s.jsx)(t.a,{href:"/docs/category/decision-records",children:"Decision Records"})}),": This section contains the Architecture Decision Records (ADRs) of the 91 project.\nIt is divided into:","\n",(0,s.jsxs)(t.ul,{children:["\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:(0,s.jsx)(t.a,{href:"/docs/category/global",children:"Global"})}),": This section contains the global decisions that affect all teams."]}),"\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:(0,s.jsx)(t.a,{href:"/docs/category/platform/",children:"Platform"})}),": This section contains the decisions related to the platform engineering team."]}),"\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:(0,s.jsx)(t.a,{href:"/docs/category/healthcare-platform/",children:"Healthcare Platform"})}),": This section contains the decisions related to the Healthcare"]}),"\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:(0,s.jsx)(t.a,{href:"/docs/category/heartplus/",children:"Heart+"})}),": This section contains the decisions related to the Heart+ application."]}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(t.li,{children:[(0,s.jsx)(t.strong,{children:(0,s.jsx)(t.a,{href:"/docs/category/research",children:"Research"})}),": This section contains the research documents that were conducted for the ADRs."]}),"\n"]}),"\n",(0,s.jsx)(t.h2,{id:"adr-tools",children:"Adr Tools"}),"\n",(0,s.jsxs)(t.p,{children:["For Architecture Decision Records, we use ",(0,s.jsx)(t.a,{href:"https://github.com/npryce/adr-tools",children:"adr tools"})," to easily add new ADRS to\nour documentation."]}),"\n",(0,s.jsx)(t.p,{children:"If you want to add an ADR to the documentation, you should use the following steps:"}),"\n",(0,s.jsxs)(t.ol,{children:["\n",(0,s.jsxs)(t.li,{children:["Go to the directory inside ",(0,s.jsx)(t.code,{children:"adrs"})," where you want to add the ADR. For example, to add an ADR to the global decisions:"]}),"\n"]}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{className:"language-sh",children:"cd documentation/docs/adrs/global\n"})}),"\n",(0,s.jsxs)(t.ol,{start:"2",children:["\n",(0,s.jsx)(t.li,{children:"Use the following command to create a new ADR:"}),"\n"]}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{className:"language-sh",children:'adr new "My ADR Title"\n'})}),"\n",(0,s.jsx)(t.p,{children:"To create a new ADR that supercedes a previous one (ADR 9, for example), use the -s option."}),"\n",(0,s.jsx)(t.pre,{children:(0,s.jsx)(t.code,{className:"language-sh",children:'adr new -s 9 "My ADR Title"\n'})}),"\n",(0,s.jsx)(t.h2,{id:"plantuml",children:"PlantUML"}),"\n",(0,s.jsx)(t.p,{children:"All diagrams (with exceptions, of course) should be done using PlantUML."}),"\n",(0,s.jsx)(t.p,{children:"Example:"}),"\n",(0,s.jsx)(t.img,{src:"https://www.plantuml.com/plantuml/png/XLHDZzem4BtdLqptq4kroh6tQldONIZGHWJrmd8Od26uE7OOsrIqwd-l7S9NGTf4YkIPDpFlZP_oP1soyvLAFjICPOMWS6-y0rkHGur9KgFOO4dmCA20_0ITkbPIe9D6Fy2V1C8pxzDE2eAEs7ffPKmj3eci_m8P6q7MIbssmQvKwdngQtj6Qn9ExgJRjvafrOPTGC97wADTBmroqHJ-lQQbt5eW4sGAtSfmTUBrCXXEOQXNZDQn5yupjRq7uy-G8LlYDl1SIn6WZah6Wu3B8PtXRVC-EeHU-2XpqX93CqPj8xBfynRUDZin1QcObleiVunBYjk0d3WkmbuFZenOeufhjxSSHjdml1fxN3gRds6La0iJbj8BcJQHhucLsSO9fm4p4ciTxbFzrtp-l2CTUXZF8j2BClfOCnY66LNXOb35hWV9OwafT8kWxpNfupcT86yz_Mw0lKZcB3BozClSBsFH25SRR68v2IPdOn06GPWC6chuWLwv1JG0XuvW4gnPKXKOHkIK2cdR8NebIt-ymcW1hqKyD2r7tq9YHsbF6L_0rEjqZDMoE8mEHeGnwjA7pWtwrQHVSOTfjdThezFPlgPSiApTIVWIxLbJBCcCTIMJJIT6bwRVRP5dxqPGxfYmKjAbhuoheAH1JSQHa9DLOz5vLncn4UlWbMjdpE_uVG6FXVcb7x_0_FOw5ydyledUgxhm_7lGsr_3HSKzmadoHBe8lvr_"})]})}function h(e={}){const{wrapper:t}={...(0,o.R)(),...e.components};return t?(0,s.jsx)(t,{...e,children:(0,s.jsx)(a,{...e})}):a(e)}},8453:(e,t,n)=>{n.d(t,{R:()=>c,x:()=>i});var r=n(6540);const s={},o=r.createContext(s);function c(e){const t=r.useContext(o);return r.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function i(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:c(e.components),r.createElement(o.Provider,{value:t},e.children)}}}]);