"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[7587],{958:e=>{e.exports=JSON.parse('{"categoryGeneratedIndex":{"title":"Global","description":"Global Architectural Decision Records.","slug":"/category/global","permalink":"/docs/category/global","sidebar":"tutorialSidebar","navigation":{"previous":{"title":"Decision Records","permalink":"/docs/category/decision-records"},"next":{"title":"1. Record architecture decisions","permalink":"/docs/adrs/global/record-architecture-decisions"}}}}')}}]);