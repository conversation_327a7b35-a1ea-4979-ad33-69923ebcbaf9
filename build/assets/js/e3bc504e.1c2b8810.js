"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[8248],{4199:(e,i,n)=>{n.r(i),n.d(i,{assets:()=>o,contentTitle:()=>l,default:()=>h,frontMatter:()=>s,metadata:()=>t,toc:()=>c});const t=JSON.parse('{"id":"ai-architecture/tools/old/api/visualization/visualization","title":"Visualization","description":"This section contains diagrams and visual documentation for the MLOps platform. These resources help understand the architecture, workflows, and interactions between different components of the platform.","source":"@site/docs/ai-architecture/tools/old/api/visualization/index.md","sourceDirName":"ai-architecture/tools/old/api/visualization","slug":"/ai-architecture/tools/old/api/visualization/","permalink":"/docs/ai-architecture/tools/old/api/visualization/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/visualization/index.md","tags":[],"version":"current","frontMatter":{"id":"visualization","title":"Visualization","sidebar_label":"Visualization"},"sidebar":"tutorialSidebar","previous":{"title":"MLflow API","permalink":"/docs/ai-architecture/tools/old/api/orchestration/mlflow-api"},"next":{"title":"API Diagrams","permalink":"/docs/ai-architecture/tools/old/api/visualization/api-diagrams"}}');var a=n(4848),r=n(8453);const s={id:"visualization",title:"Visualization",sidebar_label:"Visualization"},l="Visualization",o={},c=[{value:"Architecture Diagrams",id:"architecture-diagrams",level:2},{value:"Best Practices",id:"best-practices",level:2},{value:"Related Resources",id:"related-resources",level:2}];function d(e){const i={a:"a",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.header,{children:(0,a.jsx)(i.h1,{id:"visualization",children:"Visualization"})}),"\n",(0,a.jsx)(i.p,{children:"This section contains diagrams and visual documentation for the MLOps platform. These resources help understand the architecture, workflows, and interactions between different components of the platform."}),"\n",(0,a.jsx)(i.h2,{id:"architecture-diagrams",children:"Architecture Diagrams"}),"\n",(0,a.jsx)(i.p,{children:"Visual representations of the platform's architecture and component interactions."}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.a,{href:"/docs/ai-architecture/tools/old/api/visualization/api-diagrams",children:"API Interaction Diagrams"})," - Diagrams illustrating interactions between different components of the platform","\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsx)(i.li,{children:"Data Flow Architecture"}),"\n",(0,a.jsx)(i.li,{children:"Model Training Pipeline"}),"\n",(0,a.jsx)(i.li,{children:"Feature Serving Flow"}),"\n",(0,a.jsx)(i.li,{children:"Metadata Management"}),"\n",(0,a.jsx)(i.li,{children:"Model Deployment Flow"}),"\n",(0,a.jsx)(i.li,{children:"Data Versioning Flow"}),"\n",(0,a.jsx)(i.li,{children:"Feature Engineering Pipeline"}),"\n",(0,a.jsx)(i.li,{children:"Model Monitoring Flow"}),"\n",(0,a.jsx)(i.li,{children:"Data Quality Validation Flow"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,a.jsx)(i.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,a.jsxs)(i.ol,{children:["\n",(0,a.jsxs)(i.li,{children:["\n",(0,a.jsx)(i.p,{children:(0,a.jsx)(i.strong,{children:"Diagram Usage"})}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsx)(i.li,{children:"Use diagrams for complex workflows"}),"\n",(0,a.jsx)(i.li,{children:"Keep diagrams up to date"}),"\n",(0,a.jsx)(i.li,{children:"Include clear labels"}),"\n",(0,a.jsx)(i.li,{children:"Use consistent styling"}),"\n"]}),"\n"]}),"\n",(0,a.jsxs)(i.li,{children:["\n",(0,a.jsx)(i.p,{children:(0,a.jsx)(i.strong,{children:"Documentation"})}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsx)(i.li,{children:"Provide context for diagrams"}),"\n",(0,a.jsx)(i.li,{children:"Explain component interactions"}),"\n",(0,a.jsx)(i.li,{children:"Document assumptions"}),"\n",(0,a.jsx)(i.li,{children:"Include version information"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,a.jsx)(i.h2,{id:"related-resources",children:"Related Resources"}),"\n",(0,a.jsxs)(i.ul,{children:["\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.a,{href:"/docs/ai-architecture/tools/old/api/model-management/",children:"Model Management"})," - Model lifecycle diagrams"]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.a,{href:"/docs/ai-architecture/tools/old/api/data-management/",children:"Data Management"})," - Data flow diagrams"]}),"\n",(0,a.jsxs)(i.li,{children:[(0,a.jsx)(i.a,{href:"/docs/ai-architecture/tools/old/api/orchestration/",children:"Orchestration"})," - Pipeline diagrams"]}),"\n"]})]})}function h(e={}){const{wrapper:i}={...(0,r.R)(),...e.components};return i?(0,a.jsx)(i,{...e,children:(0,a.jsx)(d,{...e})}):d(e)}},8453:(e,i,n)=>{n.d(i,{R:()=>s,x:()=>l});var t=n(6540);const a={},r=t.createContext(a);function s(e){const i=t.useContext(r);return t.useMemo((function(){return"function"==typeof e?e(i):{...i,...e}}),[i,e])}function l(e){let i;return i=e.disableParentContext?"function"==typeof e.components?e.components(a):e.components||a:s(e.components),t.createElement(r.Provider,{value:i},e.children)}}}]);