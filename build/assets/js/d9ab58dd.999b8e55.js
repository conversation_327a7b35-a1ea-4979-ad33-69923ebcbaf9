"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3304],{6958:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>l,contentTitle:()=>d,default:()=>p,frontMatter:()=>a,metadata:()=>s,toc:()=>o});const s=JSON.parse('{"id":"ai-architecture/api/data/export","title":"Data Export","description":"Export your datasets in various formats for different use cases.","source":"@site/docs/ai-architecture/api/data/export.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/export","permalink":"/docs/ai-architecture/api/data/export","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/export.md","tags":[],"version":"current","sidebarPosition":7,"frontMatter":{"sidebar_position":7},"sidebar":"tutorialSidebar","previous":{"title":"Data Augmentation","permalink":"/docs/ai-architecture/api/data/augmentation"},"next":{"title":"Data Statistics","permalink":"/docs/ai-architecture/api/data/statistics"}}');var r=n(4848),i=n(8453);const a={sidebar_position:7},d="Data Export",l={},o=[{value:"Create Export Job",id:"create-export-job",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Export Status",id:"get-export-status",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"List Exports",id:"list-exports",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Export Status",id:"export-status",level:2},{value:"Supported Formats",id:"supported-formats",level:2},{value:"Split Types",id:"split-types",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Export Best Practices",id:"export-best-practices",level:2}];function c(e){const t={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,i.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(t.header,{children:(0,r.jsx)(t.h1,{id:"data-export",children:"Data Export"})}),"\n",(0,r.jsx)(t.p,{children:"Export your datasets in various formats for different use cases."}),"\n",(0,r.jsx)(t.h2,{id:"create-export-job",children:"Create Export Job"}),"\n",(0,r.jsx)(t.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{children:"POST /v1/data/datasets/{dataset_id}/export\n"})}),"\n",(0,r.jsx)(t.h3,{id:"request-body",children:"Request Body"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-json",children:'{\n  "format": "json",\n  "options": {\n    "include_metadata": true,\n    "include_statistics": true,\n    "compression": "gzip",\n    "split": {\n      "type": "train_test",\n      "train_ratio": 0.8,\n      "validation_ratio": 0.1,\n      "test_ratio": 0.1,\n      "stratify_by": "label"\n    }\n  },\n  "filters": {\n    "date_range": {\n      "start": "2024-01-01",\n      "end": "2024-03-14"\n    },\n    "labels": ["positive", "negative"],\n    "min_confidence": 0.8\n  }\n}\n'})}),"\n",(0,r.jsx)(t.h3,{id:"example-response",children:"Example Response"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "export": {\n      "id": "export_123",\n      "dataset_id": "dataset_123",\n      "format": "json",\n      "status": "processing",\n      "created_at": "2024-03-14T12:00:00Z",\n      "options": {\n        "include_metadata": true,\n        "compression": "gzip"\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(t.h2,{id:"get-export-status",children:"Get Export Status"}),"\n",(0,r.jsx)(t.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{children:"GET /v1/data/datasets/{dataset_id}/exports/{export_id}\n"})}),"\n",(0,r.jsx)(t.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "export": {\n      "id": "export_123",\n      "dataset_id": "dataset_123",\n      "format": "json",\n      "status": "completed",\n      "created_at": "2024-03-14T12:00:00Z",\n      "completed_at": "2024-03-14T12:01:00Z",\n      "files": [\n        {\n          "name": "train.json.gz",\n          "size": "100MB",\n          "url": "https://storage.ai-platform.example.com/exports/export_123/train.json.gz",\n          "expires_at": "2024-03-21T12:00:00Z"\n        },\n        {\n          "name": "validation.json.gz",\n          "size": "10MB",\n          "url": "https://storage.ai-platform.example.com/exports/export_123/validation.json.gz",\n          "expires_at": "2024-03-21T12:00:00Z"\n        },\n        {\n          "name": "test.json.gz",\n          "size": "10MB",\n          "url": "https://storage.ai-platform.example.com/exports/export_123/test.json.gz",\n          "expires_at": "2024-03-21T12:00:00Z"\n        }\n      ],\n      "statistics": {\n        "total_samples": 10000,\n        "train_samples": 8000,\n        "validation_samples": 1000,\n        "test_samples": 1000,\n        "exported_size": "120MB"\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:01:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(t.h2,{id:"list-exports",children:"List Exports"}),"\n",(0,r.jsx)(t.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{children:"GET /v1/data/datasets/{dataset_id}/exports\n"})}),"\n",(0,r.jsx)(t.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Parameter"}),(0,r.jsx)(t.th,{children:"Type"}),(0,r.jsx)(t.th,{children:"Description"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"page"}),(0,r.jsx)(t.td,{children:"integer"}),(0,r.jsx)(t.td,{children:"Page number (default: 1)"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"limit"}),(0,r.jsx)(t.td,{children:"integer"}),(0,r.jsx)(t.td,{children:"Items per page (default: 10)"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"status"}),(0,r.jsx)(t.td,{children:"string"}),(0,r.jsx)(t.td,{children:"Filter by status"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"format"}),(0,r.jsx)(t.td,{children:"string"}),(0,r.jsx)(t.td,{children:"Filter by format"})]})]})]}),"\n",(0,r.jsx)(t.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "exports": [\n      {\n        "id": "export_123",\n        "dataset_id": "dataset_123",\n        "format": "json",\n        "status": "completed",\n        "created_at": "2024-03-14T12:00:00Z",\n        "completed_at": "2024-03-14T12:01:00Z",\n        "total_size": "120MB"\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:01:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(t.h2,{id:"export-status",children:"Export Status"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Status"}),(0,r.jsx)(t.th,{children:"Description"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"processing"}),(0,r.jsx)(t.td,{children:"Export is running"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"completed"}),(0,r.jsx)(t.td,{children:"Export completed"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"failed"}),(0,r.jsx)(t.td,{children:"Export failed"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"cancelled"}),(0,r.jsx)(t.td,{children:"Export cancelled"})]})]})]}),"\n",(0,r.jsx)(t.h2,{id:"supported-formats",children:"Supported Formats"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Format"}),(0,r.jsx)(t.th,{children:"Description"}),(0,r.jsx)(t.th,{children:"Compression"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"json"}),(0,r.jsx)(t.td,{children:"JSON format"}),(0,r.jsx)(t.td,{children:"gzip, zip"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"csv"}),(0,r.jsx)(t.td,{children:"CSV format"}),(0,r.jsx)(t.td,{children:"gzip, zip"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"parquet"}),(0,r.jsx)(t.td,{children:"Parquet format"}),(0,r.jsx)(t.td,{children:"snappy, gzip"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"tfrecord"}),(0,r.jsx)(t.td,{children:"TensorFlow record format"}),(0,r.jsx)(t.td,{children:"gzip"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"hdf5"}),(0,r.jsx)(t.td,{children:"HDF5 format"}),(0,r.jsx)(t.td,{children:"gzip"})]})]})]}),"\n",(0,r.jsx)(t.h2,{id:"split-types",children:"Split Types"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Type"}),(0,r.jsx)(t.th,{children:"Description"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"train_test"}),(0,r.jsx)(t.td,{children:"Train/test split"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"train_val_test"}),(0,r.jsx)(t.td,{children:"Train/validation/test split"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"k_fold"}),(0,r.jsx)(t.td,{children:"K-fold cross-validation"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"custom"}),(0,r.jsx)(t.td,{children:"Custom split ratios"})]})]})]}),"\n",(0,r.jsx)(t.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,r.jsx)(t.h3,{id:"python",children:"Python"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Create export\nexport = client.data.create_export(\n    "dataset_123",\n    format="json",\n    options={\n        "include_metadata": True,\n        "compression": "gzip",\n        "split": {\n            "type": "train_test",\n            "train_ratio": 0.8\n        }\n    }\n)\n\n# Get export status\nstatus = client.data.get_export_status(\n    "dataset_123",\n    "export_123"\n)\n\n# List exports\nexports = client.data.list_exports(\n    "dataset_123",\n    page=1,\n    limit=10\n)\n'})}),"\n",(0,r.jsx)(t.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Create export\nconst export = await client.data.createExport('dataset_123', {\n  format: 'json',\n  options: {\n    includeMetadata: true,\n    compression: 'gzip',\n    split: {\n      type: 'train_test',\n      trainRatio: 0.8\n    }\n  }\n});\n\n// Get export status\nconst status = await client.data.getExportStatus(\n  'dataset_123',\n  'export_123'\n);\n\n// List exports\nconst exports = await client.data.listExports('dataset_123', {\n  page: 1,\n  limit: 10\n});\n"})}),"\n",(0,r.jsx)(t.h2,{id:"export-best-practices",children:"Export Best Practices"}),"\n",(0,r.jsxs)(t.ol,{children:["\n",(0,r.jsx)(t.li,{children:"Choose appropriate format"}),"\n",(0,r.jsx)(t.li,{children:"Use compression when needed"}),"\n",(0,r.jsx)(t.li,{children:"Include metadata"}),"\n",(0,r.jsx)(t.li,{children:"Implement data splitting"}),"\n",(0,r.jsx)(t.li,{children:"Set expiration dates"}),"\n",(0,r.jsx)(t.li,{children:"Monitor export progress"}),"\n",(0,r.jsx)(t.li,{children:"Handle large datasets"}),"\n",(0,r.jsx)(t.li,{children:"Validate exported data"}),"\n"]})]})}function p(e={}){const{wrapper:t}={...(0,i.R)(),...e.components};return t?(0,r.jsx)(t,{...e,children:(0,r.jsx)(c,{...e})}):c(e)}},8453:(e,t,n)=>{n.d(t,{R:()=>a,x:()=>d});var s=n(6540);const r={},i=s.createContext(r);function a(e){const t=s.useContext(i);return s.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function d(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:a(e.components),s.createElement(i.Provider,{value:t},e.children)}}}]);