"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[1366],{2869:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>l,contentTitle:()=>r,default:()=>h,frontMatter:()=>a,metadata:()=>t,toc:()=>c});const t=JSON.parse('{"id":"adrs/global/odrl","title":"4. ODRL as Policy and Entitlement Definition Language","description":"Date: 2025-03-17","source":"@site/docs/adrs/global/0004-odrl.md","sourceDirName":"adrs/global","slug":"/adrs/global/odrl","permalink":"/docs/adrs/global/odrl","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/global/0004-odrl.md","tags":[],"version":"current","sidebarPosition":4,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"3. Micro-frontends and BFF","permalink":"/docs/adrs/global/mfe-and-bff"},"next":{"title":"5. OPA as Policy Evaluation Engine","permalink":"/docs/adrs/global/opa"}}');var s=i(4848),o=i(8453);const a={},r="4. ODRL as Policy and Entitlement Definition Language",l={},c=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2}];function d(e){const n={h1:"h1",h2:"h2",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,o.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"4-odrl-as-policy-and-entitlement-definition-language",children:"4. ODRL as Policy and Entitlement Definition Language"})}),"\n",(0,s.jsx)(n.p,{children:"Date: 2025-03-17"}),"\n",(0,s.jsx)(n.h2,{id:"status",children:"Status"}),"\n",(0,s.jsx)(n.p,{children:"Proposed"}),"\n",(0,s.jsx)(n.h2,{id:"context",children:"Context"}),"\n",(0,s.jsx)(n.p,{children:"In 91Life's Healthcare platform, defining and enforcing access control policies is critical to ensuring compliance, privacy, and security. We evaluated several approaches for expressing policies and entitlements:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"XACML:"})," A mature, widely adopted standard that provides extensive capabilities but comes with significant complexity and verbosity, which may lead to increased maintenance overhead."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Self-Implemented Solutions:"})," These offer flexibility and customization but entail substantial development effort, potential for security vulnerabilities, and long-term maintenance risks."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Keycloak Policies:"})," We evaluated the built-in policies offered by Keycloak for authentication and authorization. However, we decided against a direct dependency on Keycloak\u2019s policy engine because it would limit our flexibility. Relying solely on Keycloak would also constrain our ability to define granular and tailored policies required by our healthcare environment."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"ODRL (Open Digital Rights Language):"})," A standardized, less complex alternative designed to express permissions, prohibitions, and obligations clearly and concisely."]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:"Additionally, there is a need for a tool that empowers non-technical users to define access control policies through a user-friendly UI. This interface should translate UI inputs into ODRL policies, allowing our clients to customize access control rules based on their specific requirements."}),"\n",(0,s.jsx)(n.h2,{id:"decision",children:"Decision"}),"\n",(0,s.jsxs)(n.p,{children:["We propose adopting ",(0,s.jsx)(n.strong,{children:"ODRL"})," as the standard for defining policies and entitlements within our Healthcare platform, with OPA (Open Policy Agent) serving as the policy evaluation engine. This decision is based on several key factors:"]}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Simplicity and Clarity:"})," ODRL offers a more concise and human-readable syntax compared to XACML, reducing the learning curve for developers and security personnel while simplifying policy management."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Granularity and Tailoring:"})," ODRL enables us to define more granular and tailored policies and entitlements compared to Keycloak\u2019s native options, ensuring that we can meet the specific and diverse needs of our healthcare clients."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Avoiding Vendor Lock-In:"})," By not relying on Keycloak\u2019s policy engine, we avoid creating a dependency that could limit our flexibility or force changes if Keycloak evolves in ways that do not align with our policy requirements."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Interoperability and Standardization:"})," As an established standard, ODRL facilitates easier integration with external systems and ensures that our policy definitions remain consistent and auditable across various platforms."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"User Empowerment through UI Integration:"})," A dedicated UI tool will enable non-technical users to define access control policies easily. The tool will convert UI inputs into standardized ODRL policies, ensuring flexibility to meet diverse client-specific requirements."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Seamless Integration with OPA:"})," OPA\u2019s flexible policy evaluation capabilities complement the use of ODRL, allowing us to efficiently enforce and audit access control policies in real-time without significant performance penalties."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Regulatory Compliance:"})," The clarity and auditability provided by ODRL align well with the compliance requirements typical in healthcare environments, supporting both internal and external audits."]}),"\n"]}),"\n",(0,s.jsx)(n.h2,{id:"consequences",children:"Consequences"}),"\n",(0,s.jsx)(n.p,{children:"Adopting ODRL as our policy definition standard, in conjunction with OPA as our evaluation engine and an intuitive UI tool for policy creation, leads to several benefits and trade-offs:"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Benefits:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Ease of Policy Definition:"})," Simplified syntax makes it easier for teams and non-technical users to define, understand, and maintain access control policies."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Improved Auditability:"})," Standardized policies enhance transparency and compliance reporting, crucial for regulatory reviews."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Enhanced Interoperability:"})," Leveraging a recognized standard facilitates integration with third-party systems and supports future scalability."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Client-Centric Customization:"})," The UI tool enables clients to easily define and adjust access control rules to match their unique needs, ensuring flexibility and personalization."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Greater Granularity:"})," ODRL allows for more detailed and tailored policy definitions compared to Keycloak\u2019s native options, meeting the stringent requirements of a healthcare environment."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Avoiding Dependency on Keycloak:"})," This approach reduces the risk of vendor lock-in and maintains our flexibility to evolve our policy management system independently of Keycloak\u2019s internal policies."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Operational Efficiency:"})," Reduced complexity minimizes the risk of misconfigurations and streamlines the policy enforcement process."]}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Trade-offs and Risks:"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Extension Requirements:"})," While ODRL covers a wide range of access control needs, certain healthcare-specific scenarios might require custom extensions or adaptations."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Community and Support:"})," Although ODRL is a standard, the community and tooling around it may not be as extensive as those for XACML, requiring investment in internal expertise."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Policy Translation Tool Development:"})," Developing and maintaining a robust tool, meant to be used by non-technical team, that accurately translates user inputs into ODRL policies will require additional resources and thorough testing to ensure accuracy and compliance."]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:"By choosing ODRL, supported by a user-friendly policy definition tool and OPA for evaluation, we aim to strike a balance between operational simplicity, client flexibility, and robust, compliant policy enforcement. This approach avoids unnecessary dependency on Keycloak\u2019s built-in policy system, granting us greater control and granularity to meet both current demands and future growth in our platform."})]})}function h(e={}){const{wrapper:n}={...(0,o.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(d,{...e})}):d(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>a,x:()=>r});var t=i(6540);const s={},o=t.createContext(s);function a(e){const n=t.useContext(o);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function r(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:a(e.components),t.createElement(o.Provider,{value:n},e.children)}}}]);