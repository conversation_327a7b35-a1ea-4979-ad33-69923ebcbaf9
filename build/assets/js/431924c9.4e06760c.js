"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6144],{591:(e,t,i)=>{i.r(t),i.d(t,{assets:()=>l,contentTitle:()=>s,default:()=>h,frontMatter:()=>a,metadata:()=>r,toc:()=>c});const r=JSON.parse('{"id":"ai-architecture/intro","title":"Introduction","description":"Welcome! This documentation is your go-to guide for understanding the architecture behind our R&D Platform. Here, you\'ll find clear explanations about how our artificial intelligence systems are designed, how the different parts fit together, and the strategies we use to keep everything running smoothly.","source":"@site/docs/ai-architecture/intro.md","sourceDirName":"ai-architecture","slug":"/ai-architecture/intro","permalink":"/docs/ai-architecture/intro","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/intro.md","tags":[],"version":"current","sidebarPosition":1,"frontMatter":{"sidebar_position":1},"sidebar":"tutorialSidebar","previous":{"title":"Feature Store Implementation","permalink":"/docs/ai-architecture/implementation/feature-store/minimalistic_feature_store"},"next":{"title":"Support","permalink":"/docs/ai-architecture/support/"}}');var n=i(4848),o=i(8453);const a={sidebar_position:1},s="Introduction",l={},c=[{value:"Understanding Our AI Architecture",id:"understanding-our-ai-architecture",level:2},{value:"Getting Started",id:"getting-started",level:2},{value:"Support",id:"support",level:2}];function u(e){const t={a:"a",h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",p:"p",ul:"ul",...(0,o.R)(),...e.components};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(t.header,{children:(0,n.jsx)(t.h1,{id:"introduction",children:"Introduction"})}),"\n",(0,n.jsx)(t.p,{children:"Welcome! This documentation is your go-to guide for understanding the architecture behind our R&D Platform. Here, you'll find clear explanations about how our artificial intelligence systems are designed, how the different parts fit together, and the strategies we use to keep everything running smoothly."}),"\n",(0,n.jsx)(t.h2,{id:"understanding-our-ai-architecture",children:"Understanding Our AI Architecture"}),"\n",(0,n.jsx)(t.p,{children:"When we talk about AI Architecture, we mean the thoughtful design and structure of our AI systems\u2014including all their moving parts, how they interact, and how they're deployed in the real world. Our platform is built with a strong focus on scalability, reliability, and ease of maintenance. This means you can count on it to grow with your needs, stay dependable, and be straightforward to manage."}),"\n",(0,n.jsx)(t.p,{children:"One thing that sets us apart is our choice to keep things simple when it comes to third-party MLOps frameworks. Instead of relying on complex, off-the-shelf solutions, we build most of our tools in-house and use only the essential cloud services. This approach gives us more control, makes it easier to adapt to new challenges, and helps us meet strict regulatory standards like FDA and HIPAA. Ultimately, it means we can fine-tune every part of the platform to fit your unique requirements."}),"\n",(0,n.jsx)(t.h2,{id:"getting-started",children:"Getting Started"}),"\n",(0,n.jsx)(t.p,{children:"To begin exploring our AI Platform's architecture and its capabilities, we recommend the following sequence:"}),"\n",(0,n.jsxs)(t.ol,{children:["\n",(0,n.jsxs)(t.li,{children:["Review the ",(0,n.jsx)(t.a,{href:"architecture-overview",children:"Architecture Overview"}),": Get a big-picture view of the platform's main components and the thinking behind our design choices."]}),"\n",(0,n.jsxs)(t.li,{children:["Check the ",(0,n.jsx)(t.a,{href:"system-requirements",children:"System Requirements"}),": Learn about the specific features and standards that guide how our platform is built and operated."]}),"\n",(0,n.jsxs)(t.li,{children:["Explore the ",(0,n.jsx)(t.a,{href:"components/ai-models",children:"Core Components"}),": Take a closer look at the essential building blocks that power our AI solutions."]}),"\n"]}),"\n",(0,n.jsx)(t.h2,{id:"support",children:"Support"}),"\n",(0,n.jsx)(t.p,{children:"We're here for you! If you have any questions or need assistance, just reach out:"}),"\n",(0,n.jsxs)(t.ul,{children:["\n",(0,n.jsx)(t.li,{children:(0,n.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),"\n",(0,n.jsx)(t.li,{children:(0,n.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),"\n",(0,n.jsx)(t.li,{children:(0,n.jsx)(t.a,{href:"mailto:<EMAIL>",children:"<EMAIL>"})}),"\n"]}),"\n",(0,n.jsx)(t.p,{children:"Let's build something amazing together!"})]})}function h(e={}){const{wrapper:t}={...(0,o.R)(),...e.components};return t?(0,n.jsx)(t,{...e,children:(0,n.jsx)(u,{...e})}):u(e)}},8453:(e,t,i)=>{i.d(t,{R:()=>a,x:()=>s});var r=i(6540);const n={},o=r.createContext(n);function a(e){const t=r.useContext(o);return r.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function s(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(n):e.components||n:a(e.components),r.createElement(o.Provider,{value:t},e.children)}}}]);