"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[5610],{3216:(e,n,a)=>{a.r(n),a.d(n,{assets:()=>r,contentTitle:()=>d,default:()=>h,frontMatter:()=>l,metadata:()=>t,toc:()=>o});const t=JSON.parse('{"id":"ai-architecture/api/data/validation","title":"Data Validation","description":"Validate and ensure the quality of your datasets.","source":"@site/docs/ai-architecture/api/data/validation.md","sourceDirName":"ai-architecture/api/data","slug":"/ai-architecture/api/data/validation","permalink":"/docs/ai-architecture/api/data/validation","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/data/validation.md","tags":[],"version":"current","sidebarPosition":4,"frontMatter":{"sidebar_position":4},"sidebar":"tutorialSidebar","previous":{"title":"Data Versioning","permalink":"/docs/ai-architecture/api/data/versioning"},"next":{"title":"Data Preprocessing","permalink":"/docs/ai-architecture/api/data/preprocessing"}}');var i=a(4848),s=a(8453);const l={sidebar_position:4},d="Data Validation",r={},o=[{value:"Run Validation",id:"run-validation",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Request Body",id:"request-body",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Validation Results",id:"get-validation-results",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"List Validations",id:"list-validations",level:2},{value:"Endpoint",id:"endpoint-2",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response-2",level:3},{value:"Validation Status",id:"validation-status",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Validation Best Practices",id:"validation-best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,s.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"data-validation",children:"Data Validation"})}),"\n",(0,i.jsx)(n.p,{children:"Validate and ensure the quality of your datasets."}),"\n",(0,i.jsx)(n.h2,{id:"run-validation",children:"Run Validation"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"POST /v1/data/datasets/{dataset_id}/validate\n"})}),"\n",(0,i.jsx)(n.h3,{id:"request-body",children:"Request Body"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "validation_rules": {\n    "data_quality": {\n      "min_samples": 1000,\n      "max_missing_values": 0.1,\n      "required_columns": ["text", "label"]\n    },\n    "format_validation": {\n      "text_length": {\n        "min": 10,\n        "max": 1000\n      },\n      "label_values": ["positive", "negative", "neutral"]\n    }\n  },\n  "options": {\n    "generate_report": true,\n    "notify_on_completion": true\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "validation": {\n      "id": "validation_123",\n      "dataset_id": "dataset_123",\n      "status": "processing",\n      "created_at": "2024-03-14T12:00:00Z",\n      "validation_rules": {\n        "data_quality": {\n          "min_samples": 1000,\n          "max_missing_values": 0.1,\n          "required_columns": ["text", "label"]\n        }\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"get-validation-results",children:"Get Validation Results"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/validations/{validation_id}\n"})}),"\n",(0,i.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "validation": {\n      "id": "validation_123",\n      "dataset_id": "dataset_123",\n      "status": "completed",\n      "created_at": "2024-03-14T12:00:00Z",\n      "completed_at": "2024-03-14T12:01:00Z",\n      "results": {\n        "overall_score": 0.95,\n        "checks": {\n          "data_quality": {\n            "score": 0.98,\n            "details": {\n              "sample_count": 1500,\n              "missing_values": 0.05,\n              "required_columns": true\n            }\n          },\n          "format_validation": {\n            "score": 0.92,\n            "details": {\n              "text_length": {\n                "valid": 0.95,\n                "invalid": 0.05\n              },\n              "label_values": {\n                "valid": 0.98,\n                "invalid": 0.02\n              }\n            }\n          }\n        },\n        "issues": [\n          {\n            "type": "format_validation",\n            "severity": "warning",\n            "message": "5% of texts exceed maximum length",\n            "affected_samples": 75\n          }\n        ]\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:01:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"list-validations",children:"List Validations"}),"\n",(0,i.jsx)(n.h3,{id:"endpoint-2",children:"Endpoint"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"GET /v1/data/datasets/{dataset_id}/validations\n"})}),"\n",(0,i.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Parameter"}),(0,i.jsx)(n.th,{children:"Type"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"page"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"limit"}),(0,i.jsx)(n.td,{children:"integer"}),(0,i.jsx)(n.td,{children:"Items per page (default: 10)"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"status"}),(0,i.jsx)(n.td,{children:"string"}),(0,i.jsx)(n.td,{children:"Filter by status"})]})]})]}),"\n",(0,i.jsx)(n.h3,{id:"example-response-2",children:"Example Response"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "validations": [\n      {\n        "id": "validation_123",\n        "dataset_id": "dataset_123",\n        "status": "completed",\n        "created_at": "2024-03-14T12:00:00Z",\n        "completed_at": "2024-03-14T12:01:00Z",\n        "overall_score": 0.95\n      }\n    ],\n    "pagination": {\n      "total": 1,\n      "page": 1,\n      "limit": 10,\n      "pages": 1\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:01:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,i.jsx)(n.h2,{id:"validation-status",children:"Validation Status"}),"\n",(0,i.jsxs)(n.table,{children:[(0,i.jsx)(n.thead,{children:(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.th,{children:"Status"}),(0,i.jsx)(n.th,{children:"Description"})]})}),(0,i.jsxs)(n.tbody,{children:[(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"processing"}),(0,i.jsx)(n.td,{children:"Validation is running"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"completed"}),(0,i.jsx)(n.td,{children:"Validation completed"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"failed"}),(0,i.jsx)(n.td,{children:"Validation failed"})]}),(0,i.jsxs)(n.tr,{children:[(0,i.jsx)(n.td,{children:"cancelled"}),(0,i.jsx)(n.td,{children:"Validation was cancelled"})]})]})]}),"\n",(0,i.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,i.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Run validation\nvalidation = client.data.validate_dataset(\n    "dataset_123",\n    validation_rules={\n        "data_quality": {\n            "min_samples": 1000,\n            "max_missing_values": 0.1\n        }\n    }\n)\n\n# Get validation results\nresults = client.data.get_validation(\n    "dataset_123",\n    "validation_123"\n)\n\n# List validations\nvalidations = client.data.list_validations(\n    "dataset_123",\n    page=1,\n    limit=10\n)\n'})}),"\n",(0,i.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Run validation\nconst validation = await client.data.validateDataset('dataset_123', {\n  validationRules: {\n    dataQuality: {\n      minSamples: 1000,\n      maxMissingValues: 0.1\n    }\n  }\n});\n\n// Get validation results\nconst results = await client.data.getValidation(\n  'dataset_123',\n  'validation_123'\n);\n\n// List validations\nconst validations = await client.data.listValidations('dataset_123', {\n  page: 1,\n  limit: 10\n});\n"})}),"\n",(0,i.jsx)(n.h2,{id:"validation-best-practices",children:"Validation Best Practices"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"Define clear validation rules"}),"\n",(0,i.jsx)(n.li,{children:"Set appropriate thresholds"}),"\n",(0,i.jsx)(n.li,{children:"Monitor validation results"}),"\n",(0,i.jsx)(n.li,{children:"Address issues promptly"}),"\n",(0,i.jsx)(n.li,{children:"Maintain validation history"}),"\n",(0,i.jsx)(n.li,{children:"Use automated validation"}),"\n",(0,i.jsx)(n.li,{children:"Implement custom rules"}),"\n",(0,i.jsx)(n.li,{children:"Regular validation schedule"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(c,{...e})}):c(e)}},8453:(e,n,a)=>{a.d(n,{R:()=>l,x:()=>d});var t=a(6540);const i={},s=t.createContext(i);function l(e){const n=t.useContext(s);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function d(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:l(e.components),t.createElement(s.Provider,{value:n},e.children)}}}]);