"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[9033],{4284:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>l,contentTitle:()=>c,default:()=>o,frontMatter:()=>d,metadata:()=>i,toc:()=>a});const i=JSON.parse('{"id":"ai-architecture/api/monitoring/metrics","title":"Monitoring Metrics","description":"Track and analyze system and model performance metrics.","source":"@site/docs/ai-architecture/api/monitoring/metrics.md","sourceDirName":"ai-architecture/api/monitoring","slug":"/ai-architecture/api/monitoring/metrics","permalink":"/docs/ai-architecture/api/monitoring/metrics","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/monitoring/metrics.md","tags":[],"version":"current","sidebarPosition":3,"frontMatter":{"sidebar_position":3},"sidebar":"tutorialSidebar","previous":{"title":"Monitoring Logs","permalink":"/docs/ai-architecture/api/monitoring/logs"},"next":{"title":"Monitoring Dashboards","permalink":"/docs/ai-architecture/api/monitoring/dashboards"}}');var r=n(4848),s=n(8453);const d={sidebar_position:3},c="Monitoring Metrics",l={},a=[{value:"Get Metrics",id:"get-metrics",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Metric Details",id:"get-metric-details",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Metric Types",id:"metric-types",level:2},{value:"Available Metrics",id:"available-metrics",level:2},{value:"System Metrics",id:"system-metrics",level:3},{value:"Model Metrics",id:"model-metrics",level:3},{value:"Data Metrics",id:"data-metrics",level:3},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Metrics Best Practices",id:"metrics-best-practices",level:2}];function h(e){const t={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,s.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(t.header,{children:(0,r.jsx)(t.h1,{id:"monitoring-metrics",children:"Monitoring Metrics"})}),"\n",(0,r.jsx)(t.p,{children:"Track and analyze system and model performance metrics."}),"\n",(0,r.jsx)(t.h2,{id:"get-metrics",children:"Get Metrics"}),"\n",(0,r.jsx)(t.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{children:"GET /v1/monitoring/metrics\n"})}),"\n",(0,r.jsx)(t.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Parameter"}),(0,r.jsx)(t.th,{children:"Type"}),(0,r.jsx)(t.th,{children:"Description"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"start_time"}),(0,r.jsx)(t.td,{children:"string"}),(0,r.jsx)(t.td,{children:"Start time (ISO format)"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"end_time"}),(0,r.jsx)(t.td,{children:"string"}),(0,r.jsx)(t.td,{children:"End time (ISO format)"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"metric"}),(0,r.jsx)(t.td,{children:"string"}),(0,r.jsx)(t.td,{children:"Metric name"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"interval"}),(0,r.jsx)(t.td,{children:"string"}),(0,r.jsx)(t.td,{children:"Time interval (1m, 5m, 1h)"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"aggregation"}),(0,r.jsx)(t.td,{children:"string"}),(0,r.jsx)(t.td,{children:"Aggregation method (avg, max)"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"page"}),(0,r.jsx)(t.td,{children:"integer"}),(0,r.jsx)(t.td,{children:"Page number (default: 1)"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"limit"}),(0,r.jsx)(t.td,{children:"integer"}),(0,r.jsx)(t.td,{children:"Items per page (default: 100)"})]})]})]}),"\n",(0,r.jsx)(t.h3,{id:"example-response",children:"Example Response"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "metrics": [\n      {\n        "id": "metric_123",\n        "name": "model_latency",\n        "value": 150.5,\n        "timestamp": "2024-03-14T12:00:00Z",\n        "labels": {\n          "model_id": "model_123",\n          "environment": "production"\n        },\n        "metadata": {\n          "unit": "ms",\n          "type": "gauge"\n        }\n      }\n    ],\n    "pagination": {\n      "total": 1000,\n      "page": 1,\n      "limit": 100,\n      "pages": 10\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(t.h2,{id:"get-metric-details",children:"Get Metric Details"}),"\n",(0,r.jsx)(t.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{children:"GET /v1/monitoring/metrics/{metric_id}\n"})}),"\n",(0,r.jsx)(t.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-json",children:'{\n  "data": {\n    "metric": {\n      "id": "metric_123",\n      "name": "model_latency",\n      "description": "Model prediction latency",\n      "type": "gauge",\n      "unit": "ms",\n      "values": [\n        {\n          "value": 150.5,\n          "timestamp": "2024-03-14T12:00:00Z",\n          "labels": {\n            "model_id": "model_123",\n            "environment": "production"\n          }\n        }\n      ],\n      "statistics": {\n        "min": 100.0,\n        "max": 200.0,\n        "avg": 150.5,\n        "p95": 180.0,\n        "p99": 190.0\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(t.h2,{id:"metric-types",children:"Metric Types"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Type"}),(0,r.jsx)(t.th,{children:"Description"}),(0,r.jsx)(t.th,{children:"Example"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"Current value"}),(0,r.jsx)(t.td,{children:"CPU usage, memory usage"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"counter"}),(0,r.jsx)(t.td,{children:"Increasing value"}),(0,r.jsx)(t.td,{children:"Request count, error count"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"histogram"}),(0,r.jsx)(t.td,{children:"Value distribution"}),(0,r.jsx)(t.td,{children:"Response time distribution"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"summary"}),(0,r.jsx)(t.td,{children:"Statistical summary"}),(0,r.jsx)(t.td,{children:"Model accuracy summary"})]})]})]}),"\n",(0,r.jsx)(t.h2,{id:"available-metrics",children:"Available Metrics"}),"\n",(0,r.jsx)(t.h3,{id:"system-metrics",children:"System Metrics"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Metric"}),(0,r.jsx)(t.th,{children:"Type"}),(0,r.jsx)(t.th,{children:"Unit"}),(0,r.jsx)(t.th,{children:"Description"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"cpu_usage"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"%"}),(0,r.jsx)(t.td,{children:"CPU utilization"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"memory_usage"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"%"}),(0,r.jsx)(t.td,{children:"Memory utilization"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"disk_usage"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"%"}),(0,r.jsx)(t.td,{children:"Disk space utilization"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"network_traffic"}),(0,r.jsx)(t.td,{children:"counter"}),(0,r.jsx)(t.td,{children:"bytes"}),(0,r.jsx)(t.td,{children:"Network traffic"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"request_count"}),(0,r.jsx)(t.td,{children:"counter"}),(0,r.jsx)(t.td,{children:"count"}),(0,r.jsx)(t.td,{children:"HTTP request count"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"error_count"}),(0,r.jsx)(t.td,{children:"counter"}),(0,r.jsx)(t.td,{children:"count"}),(0,r.jsx)(t.td,{children:"Error count"})]})]})]}),"\n",(0,r.jsx)(t.h3,{id:"model-metrics",children:"Model Metrics"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Metric"}),(0,r.jsx)(t.th,{children:"Type"}),(0,r.jsx)(t.th,{children:"Unit"}),(0,r.jsx)(t.th,{children:"Description"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"model_latency"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"ms"}),(0,r.jsx)(t.td,{children:"Prediction latency"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"model_throughput"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"req/s"}),(0,r.jsx)(t.td,{children:"Requests per second"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"model_accuracy"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"%"}),(0,r.jsx)(t.td,{children:"Model accuracy"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"model_errors"}),(0,r.jsx)(t.td,{children:"counter"}),(0,r.jsx)(t.td,{children:"count"}),(0,r.jsx)(t.td,{children:"Model error count"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"model_confidence"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"%"}),(0,r.jsx)(t.td,{children:"Prediction confidence"})]})]})]}),"\n",(0,r.jsx)(t.h3,{id:"data-metrics",children:"Data Metrics"}),"\n",(0,r.jsxs)(t.table,{children:[(0,r.jsx)(t.thead,{children:(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.th,{children:"Metric"}),(0,r.jsx)(t.th,{children:"Type"}),(0,r.jsx)(t.th,{children:"Unit"}),(0,r.jsx)(t.th,{children:"Description"})]})}),(0,r.jsxs)(t.tbody,{children:[(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"data_volume"}),(0,r.jsx)(t.td,{children:"counter"}),(0,r.jsx)(t.td,{children:"bytes"}),(0,r.jsx)(t.td,{children:"Processed data volume"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"data_quality"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"%"}),(0,r.jsx)(t.td,{children:"Data quality score"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"data_freshness"}),(0,r.jsx)(t.td,{children:"gauge"}),(0,r.jsx)(t.td,{children:"min"}),(0,r.jsx)(t.td,{children:"Data freshness"})]}),(0,r.jsxs)(t.tr,{children:[(0,r.jsx)(t.td,{children:"data_errors"}),(0,r.jsx)(t.td,{children:"counter"}),(0,r.jsx)(t.td,{children:"count"}),(0,r.jsx)(t.td,{children:"Data processing errors"})]})]})]}),"\n",(0,r.jsx)(t.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,r.jsx)(t.h3,{id:"python",children:"Python"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Get metrics\nmetrics = client.monitoring.get_metrics(\n    start_time="2024-03-14T00:00:00Z",\n    end_time="2024-03-14T12:00:00Z",\n    metric="model_latency",\n    interval="5m",\n    aggregation="avg",\n    page=1,\n    limit=100\n)\n\n# Get metric details\nmetric_details = client.monitoring.get_metric("metric_123")\n'})}),"\n",(0,r.jsx)(t.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,r.jsx)(t.pre,{children:(0,r.jsx)(t.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Get metrics\nconst metrics = await client.monitoring.getMetrics({\n  startTime: '2024-03-14T00:00:00Z',\n  endTime: '2024-03-14T12:00:00Z',\n  metric: 'model_latency',\n  interval: '5m',\n  aggregation: 'avg',\n  page: 1,\n  limit: 100\n});\n\n// Get metric details\nconst metricDetails = await client.monitoring.getMetric('metric_123');\n"})}),"\n",(0,r.jsx)(t.h2,{id:"metrics-best-practices",children:"Metrics Best Practices"}),"\n",(0,r.jsxs)(t.ol,{children:["\n",(0,r.jsx)(t.li,{children:"Use consistent naming"}),"\n",(0,r.jsx)(t.li,{children:"Include units"}),"\n",(0,r.jsx)(t.li,{children:"Add labels"}),"\n",(0,r.jsx)(t.li,{children:"Set appropriate intervals"}),"\n",(0,r.jsx)(t.li,{children:"Monitor trends"}),"\n",(0,r.jsx)(t.li,{children:"Set thresholds"}),"\n",(0,r.jsx)(t.li,{children:"Use aggregations"}),"\n",(0,r.jsx)(t.li,{children:"Document metrics"}),"\n"]})]})}function o(e={}){const{wrapper:t}={...(0,s.R)(),...e.components};return t?(0,r.jsx)(t,{...e,children:(0,r.jsx)(h,{...e})}):h(e)}},8453:(e,t,n)=>{n.d(t,{R:()=>d,x:()=>c});var i=n(6540);const r={},s=i.createContext(r);function d(e){const t=i.useContext(s);return i.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function c(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:d(e.components),i.createElement(s.Provider,{value:t},e.children)}}}]);