"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[671],{5307:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>a,contentTitle:()=>d,default:()=>h,frontMatter:()=>t,metadata:()=>s,toc:()=>o});const s=JSON.parse('{"id":"ai-architecture/tools/old/api/orchestration/kserve-api","title":"KServe API Documentation","description":"KServe provides a standardized API for model serving and inference in Kubernetes. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/orchestration/kserve-api.md","sourceDirName":"ai-architecture/tools/old/api/orchestration","slug":"/ai-architecture/tools/old/api/orchestration/kserve-api","permalink":"/docs/ai-architecture/tools/old/api/orchestration/kserve-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/orchestration/kserve-api.md","tags":[],"version":"current","frontMatter":{"id":"kserve-api","title":"KServe API Documentation","sidebar_label":"KServe API"},"sidebar":"tutorialSidebar","previous":{"title":"Orchestration","permalink":"/docs/ai-architecture/tools/old/api/orchestration/"},"next":{"title":"Kubeflow API","permalink":"/docs/ai-architecture/tools/old/api/orchestration/kubeflow-api"}}');var r=i(4848),l=i(8453);const t={id:"kserve-api",title:"KServe API Documentation",sidebar_label:"KServe API"},d="KServe API Documentation",a={},o=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Model Serving",id:"model-serving",level:3},{value:"Deploy Model",id:"deploy-model",level:4},{value:"List Models",id:"list-models",level:4},{value:"Inference",id:"inference",level:3},{value:"Model Inference",id:"model-inference",level:4},{value:"Model Management",id:"model-management",level:3},{value:"Update Model",id:"update-model",level:4},{value:"Delete Model",id:"delete-model",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function c(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,l.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"kserve-api-documentation",children:"KServe API Documentation"})}),"\n",(0,r.jsx)(n.p,{children:"KServe provides a standardized API for model serving and inference in Kubernetes. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,r.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"https://kserve.91.life/api/v1\n"})}),"\n",(0,r.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,r.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,r.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,r.jsx)(n.h3,{id:"model-serving",children:"Model Serving"}),"\n",(0,r.jsx)(n.h4,{id:"deploy-model",children:"Deploy Model"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"POST /models\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "name": "my-model",\n    "version": "v1",\n    "model_format": "pytorch",\n    "storage_uri": "s3://my-bucket/models/model.pt",\n    "resources": {\n        "cpu": "2",\n        "memory": "4Gi",\n        "gpu": "1"\n    },\n    "scaling": {\n        "min_replicas": 1,\n        "max_replicas": 3,\n        "target_concurrency": 10\n    },\n    "config": {\n        "batch_size": 32,\n        "max_batch_delay": "100ms"\n    }\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "model_id": "my-model-v1",\n    "name": "my-model",\n    "version": "v1",\n    "status": "DEPLOYING",\n    "endpoint": "https://kserve.91.life/models/model-001",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"list-models",children:"List Models"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /models\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"status"})," (optional): Filter by status"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "models": [\n        {\n            "model_id": "my-model-v1",\n            "name": "my-model",\n            "version": "v1",\n            "status": "RUNNING",\n            "endpoint": "https://kserve.91.life/models/model-001",\n            "created_at": "2024-03-20T10:00:00Z"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"inference",children:"Inference"}),"\n",(0,r.jsx)(n.h4,{id:"model-inference",children:"Model Inference"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"POST /models/{model_id}/infer\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "inputs": [\n        {\n            "name": "input-0",\n            "shape": [1, 3, 224, 224],\n            "datatype": "FP32",\n            "data": [0.1, 0.2, 0.3, ...]\n        }\n    ],\n    "parameters": {\n        "batch_size": 1\n    }\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "id": "inference-001",\n    "model_name": "my-model",\n    "model_version": "v1",\n    "outputs": [\n        {\n            "name": "output-0",\n            "shape": [1, 1000],\n            "datatype": "FP32",\n            "data": [0.1, 0.2, 0.3, ...]\n        }\n    ],\n    "parameters": {\n        "batch_size": 1\n    }\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"model-management",children:"Model Management"}),"\n",(0,r.jsx)(n.h4,{id:"update-model",children:"Update Model"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"PATCH /models/{model_id}\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "scaling": {\n        "min_replicas": 2,\n        "max_replicas": 5\n    },\n    "config": {\n        "batch_size": 64\n    }\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "model_id": "my-model-v1",\n    "name": "my-model",\n    "version": "v1",\n    "status": "UPDATING",\n    "endpoint": "https://kserve.91.life/models/model-001",\n    "updated_at": "2024-03-20T11:00:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"delete-model",children:"Delete Model"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"DELETE /models/{model_id}\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "model_id": "my-model-v1",\n    "status": "DELETING",\n    "deleted_at": "2024-03-20T12:00:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,r.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'from kserve import Client\nimport numpy as np\n\n# Initialize client\nclient = Client(host="https://kserve.91.life")\n\n# Deploy model\nmodel = {\n    "name": "my-model",\n    "version": "v1",\n    "model_format": "pytorch",\n    "storage_uri": "s3://my-bucket/models/model.pt",\n    "resources": {\n        "cpu": "2",\n        "memory": "4Gi",\n        "gpu": "1"\n    }\n}\nmodel_id = client.deploy_model(model)\n\n# Make inference\ninput_data = np.random.rand(1, 3, 224, 224).astype(np.float32)\nresponse = client.infer(\n    model_id=model_id,\n    inputs=[{\n        "name": "input-0",\n        "shape": input_data.shape,\n        "datatype": "FP32",\n        "data": input_data.tolist()\n    }]\n)\nprint(f"Prediction: {response.outputs[0].data}")\n'})}),"\n",(0,r.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:'# Deploy model\ncurl -X POST https://kserve.91.life/api/v1/models \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "my-model",\n    "version": "v1",\n    "model_format": "pytorch",\n    "storage_uri": "s3://my-bucket/models/model.pt",\n    "resources": {\n      "cpu": "2",\n      "memory": "4Gi",\n      "gpu": "1"\n    }\n  }\'\n\n# Make inference\ncurl -X POST https://kserve.91.life/api/v1/models/model-001/predict \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "inputs": [\n      {\n        "name": "input-0",\n        "shape": [1, 3, 224, 224],\n        "datatype": "FP32",\n        "data": [0.1, 0.2, 0.3, ...]\n      }\n    ]\n  }\'\n'})}),"\n",(0,r.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,r.jsxs)(n.table,{children:[(0,r.jsx)(n.thead,{children:(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.th,{children:"Code"}),(0,r.jsx)(n.th,{children:"Description"})]})}),(0,r.jsxs)(n.tbody,{children:[(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"400"}),(0,r.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"401"}),(0,r.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"403"}),(0,r.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"404"}),(0,r.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"409"}),(0,r.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"429"}),(0,r.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"500"}),(0,r.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,r.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,r.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,r.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Model Deployment"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Version your models appropriately"}),"\n",(0,r.jsx)(n.li,{children:"Set appropriate resource limits"}),"\n",(0,r.jsx)(n.li,{children:"Configure proper scaling parameters"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Inference"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Use appropriate batch sizes"}),"\n",(0,r.jsx)(n.li,{children:"Implement proper error handling"}),"\n",(0,r.jsx)(n.li,{children:"Monitor inference latency"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Performance"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Optimize model serving configuration"}),"\n",(0,r.jsx)(n.li,{children:"Use appropriate hardware resources"}),"\n",(0,r.jsx)(n.li,{children:"Implement caching when possible"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Monitoring"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Monitor model performance"}),"\n",(0,r.jsx)(n.li,{children:"Track resource usage"}),"\n",(0,r.jsx)(n.li,{children:"Set up alerts for failures"}),"\n"]}),"\n"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(c,{...e})}):c(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>t,x:()=>d});var s=i(6540);const r={},l=s.createContext(r);function t(e){const n=s.useContext(l);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function d(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:t(e.components),s.createElement(l.Provider,{value:n},e.children)}}}]);