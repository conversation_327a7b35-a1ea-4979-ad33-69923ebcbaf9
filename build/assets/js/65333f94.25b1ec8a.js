"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3494],{206:(e,n,o)=>{o.r(n),o.d(n,{assets:()=>c,contentTitle:()=>a,default:()=>p,frontMatter:()=>t,metadata:()=>r,toc:()=>l});const r=JSON.parse('{"id":"adrs/global/monorepo","title":"2. Monorepo per Project","description":"Date: 2025-02-10","source":"@site/docs/adrs/global/0002-monorepo.md","sourceDirName":"adrs/global","slug":"/adrs/global/monorepo","permalink":"/docs/adrs/global/monorepo","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/global/0002-monorepo.md","tags":[],"version":"current","sidebarPosition":2,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"1. Record architecture decisions","permalink":"/docs/adrs/global/record-architecture-decisions"},"next":{"title":"3. Micro-frontends and BFF","permalink":"/docs/adrs/global/mfe-and-bff"}}');var s=o(4848),i=o(8453);const t={},a="2. Monorepo per Project",c={},l=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2},{value:"Benefits",id:"benefits",level:3},{value:"Risks &amp; Mitigation",id:"risks--mitigation",level:3}];function d(e){const n={h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,i.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"2-monorepo-per-project",children:"2. Monorepo per Project"})}),"\n",(0,s.jsx)(n.p,{children:"Date: 2025-02-10"}),"\n",(0,s.jsx)(n.h2,{id:"status",children:"Status"}),"\n",(0,s.jsx)(n.p,{children:"Proposed"}),"\n",(0,s.jsx)(n.h2,{id:"context",children:"Context"}),"\n",(0,s.jsx)(n.p,{children:"To streamline project management, ensure consistency, and improve cross-team collaboration, we have decided to\nadopt a monorepo approach for managing all related projects. This includes projects like Healthcare Platform,\nPlatform, Heart+, and any future initiatives. The goal is to consolidate code, improve workflows, and make scaling\nmore efficient."}),"\n",(0,s.jsx)(n.h2,{id:"decision",children:"Decision"}),"\n",(0,s.jsxs)(n.p,{children:["We will ",(0,s.jsx)(n.strong,{children:"implement a monorepo for each major project within our organization"}),". This will include all applications,\nservices, and shared libraries for a given project."]}),"\n",(0,s.jsx)(n.h2,{id:"consequences",children:"Consequences"}),"\n",(0,s.jsx)(n.h3,{id:"benefits",children:"Benefits"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Improved Code Sharing"}),": Easier management of shared components and libraries across multiple projects."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Unified Development Workflow"}),": Standardized best practices, coding conventions, and CI/CD processes across projects."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Simplified Dependency Management"}),": Reduces version mismatches by keeping all related components in sync."]}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"risks--mitigation",children:"Risks & Mitigation"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Large Repository Size"}),": As the repository grows, IDEs may struggle with performance on your local device."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Access Control"}),": Teams may require different access levels to the monorepo."]}),"\n"]})]})}function p(e={}){const{wrapper:n}={...(0,i.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(d,{...e})}):d(e)}},8453:(e,n,o)=>{o.d(n,{R:()=>t,x:()=>a});var r=o(6540);const s={},i=r.createContext(s);function t(e){const n=r.useContext(i);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:t(e.components),r.createElement(i.Provider,{value:n},e.children)}}}]);