"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6143],{8453:(e,s,n)=>{n.d(s,{R:()=>o,x:()=>a});var i=n(6540);const r={},t=i.createContext(r);function o(e){const s=i.useContext(t);return i.useMemo((function(){return"function"==typeof e?e(s):{...s,...e}}),[s,e])}function a(e){let s;return s=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:o(e.components),i.createElement(t.Provider,{value:s},e.children)}},9365:(e,s,n)=>{n.r(s),n.d(s,{assets:()=>l,contentTitle:()=>a,default:()=>h,frontMatter:()=>o,metadata:()=>i,toc:()=>d});const i=JSON.parse('{"id":"research/data-flow/hl7-ingestion-parsing","title":"HL7 Transmission Ingestion and Parsing Flow","description":"This document details the flow for ingesting and parsing HL7 transmissions and subsequently outputting those HL7 messages to be consumed by other applications. It also includes the addition of a consumer application designed to save the messages to a database for reprocessing or auditing purposes.","source":"@site/docs/research/data-flow/hl7-ingestion-parsing.md","sourceDirName":"research/data-flow","slug":"/research/data-flow/hl7-ingestion-parsing","permalink":"/docs/research/data-flow/hl7-ingestion-parsing","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/research/data-flow/hl7-ingestion-parsing.md","tags":[],"version":"current","frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"Data Flow","permalink":"/docs/category/data-flow"},"next":{"title":"FHIR","permalink":"/docs/category/fhir"}}');var r=n(4848),t=n(8453);const o={},a="HL7 Transmission Ingestion and Parsing Flow",l={},d=[{value:"Overview of the Flow",id:"overview-of-the-flow",level:2},{value:"Step-by-Step Flow",id:"step-by-step-flow",level:2},{value:"1. HL7 Transmission Ingestion",id:"1-hl7-transmission-ingestion",level:3},{value:"2. HL7 Parsing",id:"2-hl7-parsing",level:3},{value:"3. Message Consumption",id:"3-message-consumption",level:3},{value:"4. Adding a New Consumer for Storage",id:"4-adding-a-new-consumer-for-storage",level:3},{value:"Diagram of the Flow",id:"diagram-of-the-flow",level:2}];function c(e){const s={h1:"h1",h2:"h2",h3:"h3",header:"header",hr:"hr",img:"img",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.header,{children:(0,r.jsx)(s.h1,{id:"hl7-transmission-ingestion-and-parsing-flow",children:"HL7 Transmission Ingestion and Parsing Flow"})}),"\n",(0,r.jsx)(s.p,{children:"This document details the flow for ingesting and parsing HL7 transmissions and subsequently outputting those HL7 messages to be consumed by other applications. It also includes the addition of a consumer application designed to save the messages to a database for reprocessing or auditing purposes."}),"\n",(0,r.jsx)(s.h2,{id:"overview-of-the-flow",children:"Overview of the Flow"}),"\n",(0,r.jsxs)(s.ol,{children:["\n",(0,r.jsxs)(s.li,{children:["\n",(0,r.jsxs)(s.p,{children:[(0,r.jsx)(s.strong,{children:"Ingestion"}),":"]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"HL7 transmissions are sent by multiple couriers (e.g., Courier 1, Courier 2, Courier n)."}),"\n",(0,r.jsxs)(s.li,{children:["These transmissions are received by ",(0,r.jsx)(s.strong,{children:"Mirth"}),", which acts as the entry point for handling incoming HL7 data."]}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(s.li,{children:["\n",(0,r.jsxs)(s.p,{children:[(0,r.jsx)(s.strong,{children:"Parsing"}),":"]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Mirth forwards the HL7 transmission via a REST endpoint."}),"\n",(0,r.jsx)(s.li,{children:"The HL7 Parser processes the transmission and converts it into a Protobuf representation of the HL7 transmission."}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(s.li,{children:["\n",(0,r.jsxs)(s.p,{children:[(0,r.jsx)(s.strong,{children:"Output"}),":"]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"The parsed HL7 representation is made available for multiple consumer applications."}),"\n",(0,r.jsx)(s.li,{children:"Each consumer application handles the message based on its specific functionality."}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(s.li,{children:["\n",(0,r.jsxs)(s.p,{children:[(0,r.jsx)(s.strong,{children:"Storage and Reprocessing"}),":"]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"A dedicated consumer application stores parsed HL7 messages into a database for later reprocessing or auditing."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(s.hr,{}),"\n",(0,r.jsx)(s.h2,{id:"step-by-step-flow",children:"Step-by-Step Flow"}),"\n",(0,r.jsx)(s.h3,{id:"1-hl7-transmission-ingestion",children:"1. HL7 Transmission Ingestion"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Sources"}),": HL7 messages are provided by multiple couriers (e.g., Courier 1, Courier 2, etc.), which save the HL7 transmissions as files in a folder locally on the server."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Intermediate Handler"}),":","\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Mirth gets the HL7 files and sends them to the HL7 Parser via its REST API endpoint, serving as a middleware platform to standardize and preprocess incoming HL7 transmissions."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(s.h3,{id:"2-hl7-parsing",children:"2. HL7 Parsing"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"The HL7 Parser receives the raw HL7 data via REST."}),"\n",(0,r.jsx)(s.li,{children:"It processes and converts the data into a standardized HL7-compatible representation."}),"\n",(0,r.jsx)(s.li,{children:"The parser ensures compatibility with downstream consumer applications."}),"\n"]}),"\n",(0,r.jsx)(s.h3,{id:"3-message-consumption",children:"3. Message Consumption"}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["The parsed Protobuf representation of the HL7 transmission is broadcast to multiple consumers:","\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Consumer 1"})," and ",(0,r.jsx)(s.strong,{children:"Consumer 2"}),": Handle specific business logic or processing requirements."]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Heart+ Consumer"}),":","\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Integrates with the Heart+ platform."}),"\n",(0,r.jsxs)(s.li,{children:["Stores relevant data into the ",(0,r.jsx)(s.strong,{children:"Heart+ Database"}),"."]}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(s.li,{children:[(0,r.jsx)(s.strong,{children:"Transmission Storage Service"}),":","\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Saves the raw HL7 messages and parsed Protobuf representations into ",(0,r.jsx)(s.strong,{children:"MongoDB"})," as the Raw Transmissions Database for reprocessing and auditing purposes."]}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(s.h3,{id:"4-adding-a-new-consumer-for-storage",children:"4. Adding a New Consumer for Storage"}),"\n",(0,r.jsx)(s.p,{children:"To enhance the system, a new consumer application can be added to specifically store parsed HL7 messages for compliance, auditing, or later reprocessing."}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["\n",(0,r.jsxs)(s.p,{children:[(0,r.jsx)(s.strong,{children:"Purpose"}),":"]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsx)(s.li,{children:"Ensure all HL7 transmissions are saved to a dedicated database."}),"\n",(0,r.jsx)(s.li,{children:"Enable reprocessing of transmissions if downstream systems experience failures."}),"\n",(0,r.jsx)(s.li,{children:"Provide a trail for auditing and regulatory compliance."}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(s.li,{children:["\n",(0,r.jsxs)(s.p,{children:[(0,r.jsx)(s.strong,{children:"Implementation"}),":"]}),"\n",(0,r.jsxs)(s.ul,{children:["\n",(0,r.jsxs)(s.li,{children:["Develop a new ",(0,r.jsx)(s.strong,{children:"Consumer App"}),"."]}),"\n",(0,r.jsx)(s.li,{children:"Configure it to subscribe to the HL7 output stream from the HL7 Parser."}),"\n",(0,r.jsxs)(s.li,{children:["Save incoming HL7 messages into a ",(0,r.jsx)(s.strong,{children:"Transmission Storage Database"}),"."]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(s.h2,{id:"diagram-of-the-flow",children:"Diagram of the Flow"}),"\n",(0,r.jsx)(s.p,{children:"Below is a visual representation of the described HL7 ingestion and processing flow:"}),"\n",(0,r.jsx)(s.img,{src:"https://www.plantuml.com/plantuml/png/bLHDQzj04BtlhnYYYqrHg9L38OUIM6vme0PZ-rPwM6l7ykBLh9bTnPJG_zwLLjZoHmgHG6ZUc_Tsc8VqvBnaNrT6k9sclMHP0MFX9PK6uLq0Z6MnAzdMf79hB4EooPgxOImhv0vzQA-0lbZo7VQrlSHV8KwMTxcjMIF3w0waWtnqYyiYbztYA79qPZfxNQocBJ7JxBUG65rk_ThKsE-Q_lm6SyaEkMsDhv3iDUqkHYHNLwSPktBKsVv1O-pXfY0x4sGV2gOOjlxbsDYgMcnkQdTqY-gI4Uc6U2AzN4k7v-B9698hnOebkKexfot1qbkM9S8I-KqNKTvX7GH9gAWysztCKcc3-M0X3z3tSyT1XgrLu9iP69KG-GZIz7lCHEHPlw1U8M9eeUuYUOJ5Z-KA1Y6meH0TcFvIqcrH_GvzfqWUOSxMstMzWGNk6HsIbpxCzJzPzZdPTJAVabyizsEjk3uexHy_6OiBhuPk5ow4mH99mTfwRoimkF7XOvo42XKyCzk3Wq6SQGYQb2waHy2tS7ZQXAUWi5LLKuCtgTwBu1OznLDuX5_4Fm00"}),"\n",(0,r.jsx)(s.hr,{}),"\n",(0,r.jsx)(s.p,{children:"By following this flow and adding the proposed consumer app, the system will be able to securely and reliably store all HL7 transmissions, ensuring compliance and enabling reprocessing or auditing as needed."})]})}function h(e={}){const{wrapper:s}={...(0,t.R)(),...e.components};return s?(0,r.jsx)(s,{...e,children:(0,r.jsx)(c,{...e})}):c(e)}}}]);