"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[2585],{8453:(e,n,i)=>{i.d(n,{R:()=>r,x:()=>o});var s=i(6540);const t={},l=s.createContext(t);function r(e){const n=s.useContext(l);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function o(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:r(e.components),s.createElement(l.Provider,{value:n},e.children)}},9569:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>a,contentTitle:()=>o,default:()=>h,frontMatter:()=>r,metadata:()=>s,toc:()=>c});const s=JSON.parse('{"id":"adrs/platform/feature-flags","title":"6. Feature Flags and A/B Testing Platform","description":"Date: 2024-03-19","source":"@site/docs/adrs/platform/0010-feature-flags.md","sourceDirName":"adrs/platform","slug":"/adrs/platform/feature-flags","permalink":"/docs/adrs/platform/feature-flags","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/platform/0010-feature-flags.md","tags":[],"version":"current","sidebarPosition":10,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"5. Database Communication in Go","permalink":"/docs/adrs/platform/go-database-communication"},"next":{"title":"Research","permalink":"/docs/category/research"}}');var t=i(4848),l=i(8453);const r={},o="6. Feature Flags and A/B Testing Platform",a={},c=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Key Components",id:"key-components",level:3},{value:"Consequences",id:"consequences",level:2},{value:"Benefits",id:"benefits",level:3},{value:"Risks &amp; Mitigation",id:"risks--mitigation",level:3},{value:"Implementation Notes",id:"implementation-notes",level:2}];function d(e){const n={h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,l.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"6-feature-flags-and-ab-testing-platform",children:"6. Feature Flags and A/B Testing Platform"})}),"\n",(0,t.jsx)(n.p,{children:"Date: 2024-03-19"}),"\n",(0,t.jsx)(n.h2,{id:"status",children:"Status"}),"\n",(0,t.jsx)(n.p,{children:"Proposed"}),"\n",(0,t.jsx)(n.h2,{id:"context",children:"Context"}),"\n",(0,t.jsx)(n.p,{children:"We need a feature flagging and A/B testing solution that is:"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Self-hosted"}),"\n",(0,t.jsx)(n.li,{children:"Open-source"}),"\n",(0,t.jsx)(n.li,{children:"Scalable"}),"\n",(0,t.jsx)(n.li,{children:"Cost-effective"}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"decision",children:"Decision"}),"\n",(0,t.jsxs)(n.p,{children:["We will use ",(0,t.jsx)(n.strong,{children:"GrowthBook"})," (self-hosted open-source version) for:"]}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Feature flag management"}),"\n",(0,t.jsx)(n.li,{children:"A/B testing"}),"\n",(0,t.jsx)(n.li,{children:"Experiment tracking"}),"\n",(0,t.jsx)(n.li,{children:"User segmentation"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"key-components",children:"Key Components"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Core Platform"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Self-hosted deployment"}),"\n",(0,t.jsx)(n.li,{children:"MIT license"}),"\n",(0,t.jsx)(n.li,{children:"Unlimited usage"}),"\n"]}),"\n"]}),"\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Features"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Feature flags"}),"\n",(0,t.jsx)(n.li,{children:"A/B testing"}),"\n",(0,t.jsx)(n.li,{children:"Metric tracking"}),"\n",(0,t.jsx)(n.li,{children:"Slack integration"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"consequences",children:"Consequences"}),"\n",(0,t.jsx)(n.h3,{id:"benefits",children:"Benefits"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Free self-hosted version"}),"\n",(0,t.jsx)(n.li,{children:"Full code control"}),"\n",(0,t.jsx)(n.li,{children:"No vendor lock-in"}),"\n",(0,t.jsx)(n.li,{children:"Well-documented SDKs"}),"\n"]}),"\n",(0,t.jsx)(n.h3,{id:"risks--mitigation",children:"Risks & Mitigation"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Missing advanced features: Not critical for current needs"}),"\n",(0,t.jsx)(n.li,{children:"Self-hosting: Proper documentation and procedures"}),"\n",(0,t.jsx)(n.li,{children:"Customization: Leverage open-source nature"}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"implementation-notes",children:"Implementation Notes"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Self-hosted deployment"}),"\n",(0,t.jsx)(n.li,{children:"SDK integration in applications"}),"\n",(0,t.jsx)(n.li,{children:"Standardized naming conventions"}),"\n",(0,t.jsx)(n.li,{children:"Regular monitoring and backups"}),"\n",(0,t.jsx)(n.li,{children:"Security audits"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(d,{...e})}):d(e)}}}]);