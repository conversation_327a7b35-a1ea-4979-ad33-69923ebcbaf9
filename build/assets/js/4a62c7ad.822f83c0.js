"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[5303],{6207:(e,n,a)=>{a.r(n),a.d(n,{assets:()=>l,contentTitle:()=>o,default:()=>u,frontMatter:()=>i,metadata:()=>r,toc:()=>d});const r=JSON.parse('{"id":"adrs/platform/dapr-pub-sub","title":"2. Dapr Pub/Sub: Apache Kafka","description":"Date: 2025-02-05","source":"@site/docs/adrs/platform/0002-dapr-pub-sub.md","sourceDirName":"adrs/platform","slug":"/adrs/platform/dapr-pub-sub","permalink":"/docs/adrs/platform/dapr-pub-sub","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/platform/0002-dapr-pub-sub.md","tags":[],"version":"current","sidebarPosition":2,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"1. Dapr","permalink":"/docs/adrs/platform/dapr"},"next":{"title":"3. Dapr Secret Store: Kubernetes Secrets","permalink":"/docs/adrs/platform/dapr-secret-store"}}');var s=a(4848),t=a(8453);const i={},o="2. Dapr Pub/Sub: Apache Kafka",l={},d=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2}];function c(e){const n={h1:"h1",h2:"h2",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"2-dapr-pubsub-apache-kafka",children:"2. Dapr Pub/Sub: Apache Kafka"})}),"\n",(0,s.jsx)(n.p,{children:"Date: 2025-02-05"}),"\n",(0,s.jsx)(n.h2,{id:"status",children:"Status"}),"\n",(0,s.jsx)(n.p,{children:"Proposed"}),"\n",(0,s.jsx)(n.h2,{id:"context",children:"Context"}),"\n",(0,s.jsxs)(n.p,{children:["Dapr provides a set of building blocks for microservices, including a pub/sub mechanism. We needed to choose a reliable, scalable, and widely adopted underlying message broker to support Dapr\u2019s Pub/Sub functionality. Our primary requirements included high throughput, fault tolerance, and support for a wide range of use cases. Evaluating multiple messaging systems led us to consider ",(0,s.jsx)(n.strong,{children:"Apache Kafka"})," as a strong candidate."]}),"\n",(0,s.jsx)(n.h2,{id:"decision",children:"Decision"}),"\n",(0,s.jsxs)(n.p,{children:["We will use ",(0,s.jsx)(n.strong,{children:"Apache Kafka"})," as the underlying technology for the Dapr Pub/Sub Building Block. This choice is driven by Kafka\u2019s proven track record of handling high-volume data streams in a distributed environment, along with a vibrant ecosystem of tooling and community support. Compared to other options like RabbitMQ, Apache Pulsar, or Redis Pub/Sub, Kafka\u2019s partition-based messaging architecture provides stronger guarantees around data durability, ordering, and fault tolerance. RabbitMQ remains more queue-oriented and may require additional components for achieving similar throughput and partitioning, while Apache Pulsar, although also distributed, has a smaller ecosystem. Redis Pub/Sub is lighter-weight but lacks built-in persistence and advanced streaming features. Kafka\u2019s mature ecosystem and robust stream processing capabilities (e.g., Kafka Streams) make it well-suited for powering event-driven applications running under the Dapr Pub/Sub Building Block. This level of scalability and extensibility makes Kafka a superior choice for powering the event-driven applications running under the Dapr Pub/Sub Building Block."]}),"\n",(0,s.jsx)(n.h2,{id:"consequences",children:"Consequences"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Positive"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"High scalability and fault tolerance for pub/sub scenarios."}),"\n",(0,s.jsx)(n.li,{children:"Broad developer familiarity and extensive ecosystem."}),"\n",(0,s.jsx)(n.li,{children:"Strong tooling support and integrations for observability and security."}),"\n"]}),"\n"]}),"\n",(0,s.jsxs)(n.li,{children:["\n",(0,s.jsx)(n.p,{children:(0,s.jsx)(n.strong,{children:"Negative"})}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsx)(n.li,{children:"Operational complexity, as Kafka requires setup and management of brokers, zookeepers (if using older versions), and configuration."}),"\n",(0,s.jsx)(n.li,{children:"Possibly steep learning curve for new adopters."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,s.jsx)(n.p,{children:"By adopting Kafka, we can leverage its robust features for Dapr\u2019s messaging needs, ensuring reliable event streaming for Dapr-based applications. While this does introduce additional overhead in terms of setup and maintenance, Kafka\u2019s versatility and integrations with a broad range of technologies can also be leveraged for other advanced data streaming and processing use cases, making it a valuable investment beyond Dapr\u2019s pub/sub requirements."})]})}function u(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(c,{...e})}):c(e)}},8453:(e,n,a)=>{a.d(n,{R:()=>i,x:()=>o});var r=a(6540);const s={},t=r.createContext(s);function i(e){const n=r.useContext(t);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function o(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:i(e.components),r.createElement(t.Provider,{value:n},e.children)}}}]);