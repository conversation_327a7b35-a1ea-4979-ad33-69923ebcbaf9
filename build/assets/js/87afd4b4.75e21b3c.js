"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[139],{8453:(e,n,t)=>{t.d(n,{R:()=>l,x:()=>d});var i=t(6540);const r={},s=i.createContext(r);function l(e){const n=i.useContext(s);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function d(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:l(e.components),i.createElement(s.Provider,{value:n},e.children)}},9855:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>o,contentTitle:()=>d,default:()=>h,frontMatter:()=>l,metadata:()=>i,toc:()=>c});const i=JSON.parse('{"id":"ai-architecture/api/monitoring/logs","title":"Monitoring Logs","description":"Access and analyze system logs for monitoring and debugging.","source":"@site/docs/ai-architecture/api/monitoring/logs.md","sourceDirName":"ai-architecture/api/monitoring","slug":"/ai-architecture/api/monitoring/logs","permalink":"/docs/ai-architecture/api/monitoring/logs","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/api/monitoring/logs.md","tags":[],"version":"current","sidebarPosition":2,"frontMatter":{"sidebar_position":2},"sidebar":"tutorialSidebar","previous":{"title":"Monitoring Alerts","permalink":"/docs/ai-architecture/api/monitoring/alerts"},"next":{"title":"Monitoring Metrics","permalink":"/docs/ai-architecture/api/monitoring/metrics"}}');var r=t(4848),s=t(8453);const l={sidebar_position:2},d="Monitoring Logs",o={},c=[{value:"Get Logs",id:"get-logs",level:2},{value:"Endpoint",id:"endpoint",level:3},{value:"Query Parameters",id:"query-parameters",level:3},{value:"Example Response",id:"example-response",level:3},{value:"Get Log Details",id:"get-log-details",level:2},{value:"Endpoint",id:"endpoint-1",level:3},{value:"Example Response",id:"example-response-1",level:3},{value:"Log Levels",id:"log-levels",level:2},{value:"Log Sources",id:"log-sources",level:2},{value:"Log Retention",id:"log-retention",level:2},{value:"SDK Examples",id:"sdk-examples",level:2},{value:"Python",id:"python",level:3},{value:"JavaScript",id:"javascript",level:3},{value:"Logging Best Practices",id:"logging-best-practices",level:2}];function a(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",...(0,s.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"monitoring-logs",children:"Monitoring Logs"})}),"\n",(0,r.jsx)(n.p,{children:"Access and analyze system logs for monitoring and debugging."}),"\n",(0,r.jsx)(n.h2,{id:"get-logs",children:"Get Logs"}),"\n",(0,r.jsx)(n.h3,{id:"endpoint",children:"Endpoint"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /v1/monitoring/logs\n"})}),"\n",(0,r.jsx)(n.h3,{id:"query-parameters",children:"Query Parameters"}),"\n",(0,r.jsxs)(n.table,{children:[(0,r.jsx)(n.thead,{children:(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.th,{children:"Parameter"}),(0,r.jsx)(n.th,{children:"Type"}),(0,r.jsx)(n.th,{children:"Description"})]})}),(0,r.jsxs)(n.tbody,{children:[(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"start_time"}),(0,r.jsx)(n.td,{children:"string"}),(0,r.jsx)(n.td,{children:"Start time (ISO format)"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"end_time"}),(0,r.jsx)(n.td,{children:"string"}),(0,r.jsx)(n.td,{children:"End time (ISO format)"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"level"}),(0,r.jsx)(n.td,{children:"string"}),(0,r.jsx)(n.td,{children:"Log level (info, warn, error)"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"source"}),(0,r.jsx)(n.td,{children:"string"}),(0,r.jsx)(n.td,{children:"Log source (model, data, etc.)"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"search"}),(0,r.jsx)(n.td,{children:"string"}),(0,r.jsx)(n.td,{children:"Search query"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"page"}),(0,r.jsx)(n.td,{children:"integer"}),(0,r.jsx)(n.td,{children:"Page number (default: 1)"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"limit"}),(0,r.jsx)(n.td,{children:"integer"}),(0,r.jsx)(n.td,{children:"Items per page (default: 100)"})]})]})]}),"\n",(0,r.jsx)(n.h3,{id:"example-response",children:"Example Response"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "logs": [\n      {\n        "id": "log_123",\n        "timestamp": "2024-03-14T12:00:00Z",\n        "level": "error",\n        "source": "model",\n        "message": "Model prediction failed",\n        "context": {\n          "model_id": "model_123",\n          "request_id": "req_123456",\n          "error": "Invalid input format"\n        },\n        "metadata": {\n          "environment": "production",\n          "version": "1.0.0"\n        }\n      }\n    ],\n    "pagination": {\n      "total": 1000,\n      "page": 1,\n      "limit": 100,\n      "pages": 10\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(n.h2,{id:"get-log-details",children:"Get Log Details"}),"\n",(0,r.jsx)(n.h3,{id:"endpoint-1",children:"Endpoint"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"GET /v1/monitoring/logs/{log_id}\n"})}),"\n",(0,r.jsx)(n.h3,{id:"example-response-1",children:"Example Response"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n  "data": {\n    "log": {\n      "id": "log_123",\n      "timestamp": "2024-03-14T12:00:00Z",\n      "level": "error",\n      "source": "model",\n      "message": "Model prediction failed",\n      "context": {\n        "model_id": "model_123",\n        "request_id": "req_123456",\n        "error": "Invalid input format",\n        "stack_trace": "...",\n        "input_data": {\n          "text": "example input"\n        }\n      },\n      "metadata": {\n        "environment": "production",\n        "version": "1.0.0",\n        "host": "server-1",\n        "region": "us-west"\n      }\n    }\n  },\n  "meta": {\n    "timestamp": "2024-03-14T12:00:00Z",\n    "request_id": "req_123456"\n  }\n}\n'})}),"\n",(0,r.jsx)(n.h2,{id:"log-levels",children:"Log Levels"}),"\n",(0,r.jsxs)(n.table,{children:[(0,r.jsx)(n.thead,{children:(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.th,{children:"Level"}),(0,r.jsx)(n.th,{children:"Description"}),(0,r.jsx)(n.th,{children:"Use Case"})]})}),(0,r.jsxs)(n.tbody,{children:[(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"debug"}),(0,r.jsx)(n.td,{children:"Detailed debugging information"}),(0,r.jsx)(n.td,{children:"Development and debugging"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"info"}),(0,r.jsx)(n.td,{children:"General information"}),(0,r.jsx)(n.td,{children:"Normal operation events"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"warn"}),(0,r.jsx)(n.td,{children:"Warning messages"}),(0,r.jsx)(n.td,{children:"Potential issues"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"error"}),(0,r.jsx)(n.td,{children:"Error messages"}),(0,r.jsx)(n.td,{children:"Operation failures"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"critical"}),(0,r.jsx)(n.td,{children:"Critical errors"}),(0,r.jsx)(n.td,{children:"System failures"})]})]})]}),"\n",(0,r.jsx)(n.h2,{id:"log-sources",children:"Log Sources"}),"\n",(0,r.jsxs)(n.table,{children:[(0,r.jsx)(n.thead,{children:(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.th,{children:"Source"}),(0,r.jsx)(n.th,{children:"Description"}),(0,r.jsx)(n.th,{children:"Log Types"})]})}),(0,r.jsxs)(n.tbody,{children:[(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"model"}),(0,r.jsx)(n.td,{children:"Model-related logs"}),(0,r.jsx)(n.td,{children:"Predictions, training, errors"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"data"}),(0,r.jsx)(n.td,{children:"Data-related logs"}),(0,r.jsx)(n.td,{children:"Processing, validation"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"system"}),(0,r.jsx)(n.td,{children:"System-related logs"}),(0,r.jsx)(n.td,{children:"Performance, health"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"security"}),(0,r.jsx)(n.td,{children:"Security-related logs"}),(0,r.jsx)(n.td,{children:"Authentication, access"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"custom"}),(0,r.jsx)(n.td,{children:"Custom application logs"}),(0,r.jsx)(n.td,{children:"Business logic, events"})]})]})]}),"\n",(0,r.jsx)(n.h2,{id:"log-retention",children:"Log Retention"}),"\n",(0,r.jsxs)(n.table,{children:[(0,r.jsx)(n.thead,{children:(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.th,{children:"Plan"}),(0,r.jsx)(n.th,{children:"Retention Period"}),(0,r.jsx)(n.th,{children:"Storage Limit"})]})}),(0,r.jsxs)(n.tbody,{children:[(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"Standard"}),(0,r.jsx)(n.td,{children:"7 days"}),(0,r.jsx)(n.td,{children:"1 GB"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"Pro"}),(0,r.jsx)(n.td,{children:"30 days"}),(0,r.jsx)(n.td,{children:"10 GB"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"Enterprise"}),(0,r.jsx)(n.td,{children:"Custom"}),(0,r.jsx)(n.td,{children:"Custom"})]})]})]}),"\n",(0,r.jsx)(n.h2,{id:"sdk-examples",children:"SDK Examples"}),"\n",(0,r.jsx)(n.h3,{id:"python",children:"Python"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'from ai_platform import Client\n\nclient = Client(api_key="YOUR_API_KEY")\n\n# Get logs\nlogs = client.monitoring.get_logs(\n    start_time="2024-03-14T00:00:00Z",\n    end_time="2024-03-14T12:00:00Z",\n    level="error",\n    source="model",\n    page=1,\n    limit=100\n)\n\n# Get log details\nlog_details = client.monitoring.get_log("log_123")\n'})}),"\n",(0,r.jsx)(n.h3,{id:"javascript",children:"JavaScript"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-javascript",children:"import { Client } from '@ai-platform/sdk';\n\nconst client = new Client({\n  apiKey: 'YOUR_API_KEY'\n});\n\n// Get logs\nconst logs = await client.monitoring.getLogs({\n  startTime: '2024-03-14T00:00:00Z',\n  endTime: '2024-03-14T12:00:00Z',\n  level: 'error',\n  source: 'model',\n  page: 1,\n  limit: 100\n});\n\n// Get log details\nconst logDetails = await client.monitoring.getLog('log_123');\n"})}),"\n",(0,r.jsx)(n.h2,{id:"logging-best-practices",children:"Logging Best Practices"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsx)(n.li,{children:"Use appropriate log levels"}),"\n",(0,r.jsx)(n.li,{children:"Include relevant context"}),"\n",(0,r.jsx)(n.li,{children:"Structure log messages"}),"\n",(0,r.jsx)(n.li,{children:"Add request IDs"}),"\n",(0,r.jsx)(n.li,{children:"Include timestamps"}),"\n",(0,r.jsx)(n.li,{children:"Add metadata"}),"\n",(0,r.jsx)(n.li,{children:"Implement log rotation"}),"\n",(0,r.jsx)(n.li,{children:"Monitor log volume"}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(a,{...e})}):a(e)}}}]);