"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[606],{954:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>l,contentTitle:()=>a,default:()=>u,frontMatter:()=>o,metadata:()=>i,toc:()=>d});const i=JSON.parse('{"id":"adrs/platform/nx","title":"6. Nx as Monorepo Manager and Build Tool","description":"Date: 2025-03-10","source":"@site/docs/adrs/platform/0006-nx.md","sourceDirName":"adrs/platform","slug":"/adrs/platform/nx","permalink":"/docs/adrs/platform/nx","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/adrs/platform/0006-nx.md","tags":[],"version":"current","sidebarPosition":6,"frontMatter":{},"sidebar":"tutorialSidebar","previous":{"title":"5. Web Applications with React","permalink":"/docs/adrs/platform/react"},"next":{"title":"7. Vite as the Build Tool for React applications","permalink":"/docs/adrs/platform/vite"}}');var s=t(4848),r=t(8453);const o={},a="6. Nx as Monorepo Manager and Build Tool",l={},d=[{value:"Status",id:"status",level:2},{value:"Context",id:"context",level:2},{value:"Decision",id:"decision",level:2},{value:"Consequences",id:"consequences",level:2},{value:"Benefits",id:"benefits",level:3},{value:"Risks &amp; Mitigation",id:"risks--mitigation",level:3}];function c(e){const n={a:"a",h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",p:"p",strong:"strong",ul:"ul",...(0,r.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"6-nx-as-monorepo-manager-and-build-tool",children:"6. Nx as Monorepo Manager and Build Tool"})}),"\n",(0,s.jsx)(n.p,{children:"Date: 2025-03-10"}),"\n",(0,s.jsx)(n.h2,{id:"status",children:"Status"}),"\n",(0,s.jsx)(n.p,{children:"Proposed"}),"\n",(0,s.jsx)(n.h2,{id:"context",children:"Context"}),"\n",(0,s.jsx)(n.p,{children:"To efficiently manage the monorepo, we need a robust tool that can handle dependency management, optimize builds,\nand support CI/CD pipelines."}),"\n",(0,s.jsx)(n.h2,{id:"decision",children:"Decision"}),"\n",(0,s.jsxs)(n.p,{children:["We will ",(0,s.jsxs)(n.strong,{children:["use ",(0,s.jsx)(n.a,{href:"https://nx.dev/",children:"Nx"})," to manage the monorepo"]})," for all projects. Nx will handle the structure, dependency management,\nand build optimizations."]}),"\n",(0,s.jsx)(n.h2,{id:"consequences",children:"Consequences"}),"\n",(0,s.jsx)(n.h3,{id:"benefits",children:"Benefits"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Maturity"}),": Nx is a mature tool with a large community and extensive documentation."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Performant builds"}),": Nx\u2019s caching and incremental build capabilities significantly reduce build times. Benchmarks\nshow it is faster then other tools."]}),"\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Multiple programming languages"}),": Nx supports multiple programming languages and frameworks (including Go which"]}),"\n",(0,s.jsx)(n.li,{children:"is our predominant backend programming language)."}),"\n"]}),"\n",(0,s.jsx)(n.h3,{id:"risks--mitigation",children:"Risks & Mitigation"}),"\n",(0,s.jsxs)(n.ul,{children:["\n",(0,s.jsxs)(n.li,{children:[(0,s.jsx)(n.strong,{children:"Learning Curve"}),": Nx may introduce additional complexity for developers unfamiliar with it."]}),"\n"]})]})}function u(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(c,{...e})}):c(e)}},8453:(e,n,t)=>{t.d(n,{R:()=>o,x:()=>a});var i=t(6540);const s={},r=i.createContext(s);function o(e){const n=i.useContext(r);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:o(e.components),i.createElement(r.Provider,{value:n},e.children)}}}]);