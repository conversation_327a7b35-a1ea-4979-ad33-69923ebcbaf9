"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[6719],{1159:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>c,contentTitle:()=>a,default:()=>h,frontMatter:()=>s,metadata:()=>t,toc:()=>o});const t=JSON.parse('{"id":"ai-architecture/tools/old/api/monitoring/alerting-api","title":"Alerting API Documentation","description":"The Alerting API provides endpoints for managing alerts, notifications, and incident response. This documentation covers all available endpoints, authentication, and usage examples.","source":"@site/docs/ai-architecture/tools/old/api/monitoring/alerting-api.md","sourceDirName":"ai-architecture/tools/old/api/monitoring","slug":"/ai-architecture/tools/old/api/monitoring/alerting-api","permalink":"/docs/ai-architecture/tools/old/api/monitoring/alerting-api","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/tools/old/api/monitoring/alerting-api.md","tags":[],"version":"current","frontMatter":{"id":"alerting-api","title":"Alerting API Documentation","sidebar_label":"Alerting API"},"sidebar":"tutorialSidebar","previous":{"title":"Monitoring","permalink":"/docs/ai-architecture/tools/old/api/monitoring/"},"next":{"title":"Monitoring API","permalink":"/docs/ai-architecture/tools/old/api/monitoring/monitoring-api"}}');var r=i(4848),l=i(8453);const s={id:"alerting-api",title:"Alerting API Documentation",sidebar_label:"Alerting API"},a="Alerting API Documentation",c={},o=[{value:"Base URL",id:"base-url",level:2},{value:"Authentication",id:"authentication",level:2},{value:"API Endpoints",id:"api-endpoints",level:2},{value:"Alert Rules",id:"alert-rules",level:3},{value:"Create Alert Rule",id:"create-alert-rule",level:4},{value:"List Alert Rules",id:"list-alert-rules",level:4},{value:"Alerts",id:"alerts",level:3},{value:"List Active Alerts",id:"list-active-alerts",level:4},{value:"Update Alert Status",id:"update-alert-status",level:4},{value:"Notification Channels",id:"notification-channels",level:3},{value:"Create Notification Channel",id:"create-notification-channel",level:4},{value:"List Notification Channels",id:"list-notification-channels",level:4},{value:"Usage Examples",id:"usage-examples",level:2},{value:"Python Example",id:"python-example",level:3},{value:"cURL Example",id:"curl-example",level:3},{value:"Error Codes",id:"error-codes",level:2},{value:"Rate Limiting",id:"rate-limiting",level:2},{value:"Best Practices",id:"best-practices",level:2}];function d(e){const n={code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",table:"table",tbody:"tbody",td:"td",th:"th",thead:"thead",tr:"tr",ul:"ul",...(0,l.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"alerting-api-documentation",children:"Alerting API Documentation"})}),"\n",(0,r.jsx)(n.p,{children:"The Alerting API provides endpoints for managing alerts, notifications, and incident response. This documentation covers all available endpoints, authentication, and usage examples."}),"\n",(0,r.jsx)(n.h2,{id:"base-url",children:"Base URL"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"https://alerting.91.life/api/v1\n"})}),"\n",(0,r.jsx)(n.h2,{id:"authentication",children:"Authentication"}),"\n",(0,r.jsx)(n.p,{children:"All API requests require authentication using OAuth2/OIDC. Include the following header:"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"Authorization: Bearer <your-token>\n"})}),"\n",(0,r.jsx)(n.h2,{id:"api-endpoints",children:"API Endpoints"}),"\n",(0,r.jsx)(n.h3,{id:"alert-rules",children:"Alert Rules"}),"\n",(0,r.jsx)(n.h4,{id:"create-alert-rule",children:"Create Alert Rule"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"POST /rules\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "name": "high_error_rate",\n    "description": "Alert when error rate exceeds threshold",\n    "condition": {\n        "metric": "error_rate",\n        "operator": ">",\n        "threshold": 0.05,\n        "duration": "5m"\n    },\n    "severity": "critical",\n    "labels": {\n        "team": "ml-platform",\n        "component": "model-serving"\n    },\n    "annotations": {\n        "summary": "High error rate detected",\n        "description": "Error rate has exceeded 5% for 5 minutes"\n    },\n    "notifications": {\n        "channels": ["slack", "email"],\n        "recipients": ["<EMAIL>"],\n        "cooldown": "15m"\n    }\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "rule_id": "rule_001",\n    "name": "high_error_rate",\n    "status": "active",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"list-alert-rules",children:"List Alert Rules"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /rules\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"severity"})," (optional): Filter by severity"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"status"})," (optional): Filter by status"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"label"})," (optional): Filter by label"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "rules": [\n        {\n            "rule_id": "rule_001",\n            "name": "high_error_rate",\n            "description": "Alert when error rate exceeds threshold",\n            "condition": {\n                "metric": "error_rate",\n                "operator": ">",\n                "threshold": 0.05,\n                "duration": "5m"\n            },\n            "severity": "critical",\n            "status": "active",\n            "created_at": "2024-03-20T10:00:00Z"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"alerts",children:"Alerts"}),"\n",(0,r.jsx)(n.h4,{id:"list-active-alerts",children:"List Active Alerts"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /alerts\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"severity"})," (optional): Filter by severity"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"rule_id"})," (optional): Filter by rule"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"status"})," (optional): Filter by status"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"start_time"})," (optional): Start time for alerts"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"end_time"})," (optional): End time for alerts"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "alerts": [\n        {\n            "alert_id": "alert_001",\n            "rule_id": "rule_001",\n            "name": "high_error_rate",\n            "severity": "critical",\n            "status": "firing",\n            "started_at": "2024-03-20T10:00:00Z",\n            "last_updated": "2024-03-20T10:05:00Z",\n            "value": 0.08,\n            "threshold": 0.05,\n            "labels": {\n                "team": "ml-platform",\n                "component": "model-serving"\n            }\n        }\n    ]\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"update-alert-status",children:"Update Alert Status"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"PATCH /alerts/{alert_id}\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "status": "acknowledged",\n    "comment": "Investigating the issue",\n    "assigned_to": "<EMAIL>"\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "alert_id": "alert_001",\n    "status": "acknowledged",\n    "updated_at": "2024-03-20T10:10:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h3,{id:"notification-channels",children:"Notification Channels"}),"\n",(0,r.jsx)(n.h4,{id:"create-notification-channel",children:"Create Notification Channel"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"POST /channels\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Request Body:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "name": "ml-team-slack",\n    "type": "slack",\n    "config": {\n        "webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz",\n        "channel": "#ml-alerts",\n        "username": "ML Platform Bot"\n    },\n    "severity_levels": ["critical", "warning"],\n    "cooldown": "15m"\n}\n'})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "channel_id": "channel_001",\n    "name": "ml-team-slack",\n    "type": "slack",\n    "status": "active",\n    "created_at": "2024-03-20T10:00:00Z"\n}\n'})}),"\n",(0,r.jsx)(n.h4,{id:"list-notification-channels",children:"List Notification Channels"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-http",children:"GET /channels\n"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Query Parameters:"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"type"})," (optional): Filter by channel type"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"status"})," (optional): Filter by status"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_size"})," (optional): Number of results per page"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"page_token"})," (optional): Token for pagination"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Response:"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-json",children:'{\n    "channels": [\n        {\n            "channel_id": "channel_001",\n            "name": "ml-team-slack",\n            "type": "slack",\n            "status": "active",\n            "created_at": "2024-03-20T10:00:00Z"\n        }\n    ],\n    "next_page_token": "abc123..."\n}\n'})}),"\n",(0,r.jsx)(n.h2,{id:"usage-examples",children:"Usage Examples"}),"\n",(0,r.jsx)(n.h3,{id:"python-example",children:"Python Example"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-python",children:'from alerting import Client\n\n# Initialize client\nclient = Client(\n    host="https://alerting.91.life",\n    auth_token="your-token"\n)\n\n# Create alert rule\nrule = {\n    "name": "high_error_rate",\n    "description": "Alert when error rate exceeds threshold",\n    "condition": {\n        "metric": "error_rate",\n        "operator": ">",\n        "threshold": 0.05,\n        "duration": "5m"\n    },\n    "severity": "critical",\n    "notifications": {\n        "channels": ["slack"],\n        "recipients": ["<EMAIL>"]\n    }\n}\nrule_id = client.create_rule(rule)\n\n# List active alerts\nalerts = client.list_alerts(\n    severity="critical",\n    status="firing"\n)\nprint(alerts)\n'})}),"\n",(0,r.jsx)(n.h3,{id:"curl-example",children:"cURL Example"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:'# Create alert rule\ncurl -X POST https://alerting.91.life/api/v1/rules \\\n  -H "Authorization: Bearer ${TOKEN}" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "name": "high_error_rate",\n    "description": "Alert when error rate exceeds threshold",\n    "condition": {\n      "metric": "error_rate",\n      "operator": ">",\n      "threshold": 0.05,\n      "duration": "5m"\n    },\n    "severity": "critical",\n    "notifications": {\n      "channels": ["slack"],\n      "recipients": ["<EMAIL>"]\n    }\n  }\'\n\n# List active alerts\ncurl -X GET "https://alerting.91.life/api/v1/alerts?severity=critical&status=firing" \\\n  -H "Authorization: Bearer ${TOKEN}"\n'})}),"\n",(0,r.jsx)(n.h2,{id:"error-codes",children:"Error Codes"}),"\n",(0,r.jsxs)(n.table,{children:[(0,r.jsx)(n.thead,{children:(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.th,{children:"Code"}),(0,r.jsx)(n.th,{children:"Description"})]})}),(0,r.jsxs)(n.tbody,{children:[(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"400"}),(0,r.jsx)(n.td,{children:"Bad Request - Invalid input parameters"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"401"}),(0,r.jsx)(n.td,{children:"Unauthorized - Invalid or missing authentication"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"403"}),(0,r.jsx)(n.td,{children:"Forbidden - Insufficient permissions"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"404"}),(0,r.jsx)(n.td,{children:"Not Found - Resource doesn't exist"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"409"}),(0,r.jsx)(n.td,{children:"Conflict - Resource already exists"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"429"}),(0,r.jsx)(n.td,{children:"Too Many Requests - Rate limit exceeded"})]}),(0,r.jsxs)(n.tr,{children:[(0,r.jsx)(n.td,{children:"500"}),(0,r.jsx)(n.td,{children:"Internal Server Error - Server-side error"})]})]})]}),"\n",(0,r.jsx)(n.h2,{id:"rate-limiting",children:"Rate Limiting"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Standard rate limit: 100 requests per minute"}),"\n",(0,r.jsx)(n.li,{children:"Burst rate limit: 200 requests per minute"}),"\n",(0,r.jsxs)(n.li,{children:["Rate limit headers:","\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Limit"}),": Maximum requests per window"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Remaining"}),": Remaining requests in current window"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.code,{children:"X-RateLimit-Reset"}),": Time until rate limit resets"]}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Alert Rules"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Set appropriate thresholds"}),"\n",(0,r.jsx)(n.li,{children:"Use meaningful names and descriptions"}),"\n",(0,r.jsx)(n.li,{children:"Configure proper severity levels"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Notifications"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Use multiple notification channels"}),"\n",(0,r.jsx)(n.li,{children:"Configure cooldown periods"}),"\n",(0,r.jsx)(n.li,{children:"Set up escalation policies"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Alert Management"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Acknowledge alerts promptly"}),"\n",(0,r.jsx)(n.li,{children:"Document investigation steps"}),"\n",(0,r.jsx)(n.li,{children:"Update alert status regularly"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Monitoring"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Monitor alert effectiveness"}),"\n",(0,r.jsx)(n.li,{children:"Review and tune thresholds"}),"\n",(0,r.jsx)(n.li,{children:"Track alert response times"}),"\n"]}),"\n"]}),"\n"]})]})}function h(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(d,{...e})}):d(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>s,x:()=>a});var t=i(6540);const r={},l=t.createContext(r);function s(e){const n=t.useContext(l);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:s(e.components),t.createElement(l.Provider,{value:n},e.children)}}}]);