"use strict";(self.webpackChunkAI_Architecture=self.webpackChunkAI_Architecture||[]).push([[3146],{8453:(n,e,i)=>{i.d(e,{R:()=>c,x:()=>d});var l=i(6540);const r={},s=l.createContext(r);function c(n){const e=l.useContext(s);return l.useMemo((function(){return"function"==typeof n?n(e):{...e,...n}}),[e,n])}function d(n){let e;return e=n.disableParentContext?"function"==typeof n.components?n.components(r):n.components||r:c(n.components),l.createElement(s.Provider,{value:e},n.children)}},9687:(n,e,i)=>{i.r(e),i.d(e,{assets:()=>t,contentTitle:()=>d,default:()=>x,frontMatter:()=>c,metadata:()=>l,toc:()=>h});const l=JSON.parse('{"id":"ai-architecture/deployment/cloud-providers/index","title":"Cloud Providers","description":"This guide covers deploying the AI Platform on major cloud providers, including AWS, GCP, and Azure.","source":"@site/docs/ai-architecture/deployment/cloud-providers/index.md","sourceDirName":"ai-architecture/deployment/cloud-providers","slug":"/ai-architecture/deployment/cloud-providers/","permalink":"/docs/ai-architecture/deployment/cloud-providers/","draft":false,"unlisted":false,"editUrl":"https://bitbucket.org/arhm/architecture/docs/ai-architecture/deployment/cloud-providers/index.md","tags":[],"version":"current","frontMatter":{"title":"Cloud Providers"},"sidebar":"tutorialSidebar","previous":{"title":"Monitoring","permalink":"/docs/ai-architecture/components/monitoring/"},"next":{"title":"Kubernetes Deployment","permalink":"/docs/ai-architecture/deployment/kubernetes/"}}');var r=i(4848),s=i(8453);const c={title:"Cloud Providers"},d="Cloud Providers",t={},h=[{value:"GCP Deployment",id:"gcp-deployment",level:2},{value:"Services",id:"services",level:3},{value:"GCP Deployment",id:"gcp-deployment-1",level:2},{value:"Services",id:"services-1",level:3},{value:"Azure Deployment",id:"azure-deployment",level:2},{value:"Services",id:"services-2",level:3},{value:"Best Practices",id:"best-practices",level:2},{value:"Infrastructure",id:"infrastructure",level:3},{value:"Deployment",id:"deployment",level:3}];function o(n){const e={h1:"h1",h2:"h2",h3:"h3",header:"header",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,s.R)(),...n.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(e.header,{children:(0,r.jsx)(e.h1,{id:"cloud-providers",children:"Cloud Providers"})}),"\n",(0,r.jsx)(e.p,{children:"This guide covers deploying the AI Platform on major cloud providers, including AWS, GCP, and Azure."}),"\n",(0,r.jsx)(e.h2,{id:"gcp-deployment",children:"GCP Deployment"}),"\n",(0,r.jsx)(e.h3,{id:"services",children:"Services"}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Compute"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"EC2"}),"\n",(0,r.jsx)(e.li,{children:"EKS"}),"\n",(0,r.jsx)(e.li,{children:"Lambda"}),"\n",(0,r.jsx)(e.li,{children:"Batch"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Storage"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"S3"}),"\n",(0,r.jsx)(e.li,{children:"EBS"}),"\n",(0,r.jsx)(e.li,{children:"EFS"}),"\n",(0,r.jsx)(e.li,{children:"RDS"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Networking"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"VPC"}),"\n",(0,r.jsx)(e.li,{children:"Route 53"}),"\n",(0,r.jsx)(e.li,{children:"CloudFront"}),"\n",(0,r.jsx)(e.li,{children:"API Gateway"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Security"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"IAM"}),"\n",(0,r.jsx)(e.li,{children:"KMS"}),"\n",(0,r.jsx)(e.li,{children:"Secrets Manager"}),"\n",(0,r.jsx)(e.li,{children:"WAF"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(e.h2,{id:"gcp-deployment-1",children:"GCP Deployment"}),"\n",(0,r.jsx)(e.h3,{id:"services-1",children:"Services"}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Compute"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"GKE"}),"\n",(0,r.jsx)(e.li,{children:"Compute Engine"}),"\n",(0,r.jsx)(e.li,{children:"Cloud Run"}),"\n",(0,r.jsx)(e.li,{children:"Cloud Functions"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Storage"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Cloud Storage"}),"\n",(0,r.jsx)(e.li,{children:"Cloud SQL"}),"\n",(0,r.jsx)(e.li,{children:"Filestore"}),"\n",(0,r.jsx)(e.li,{children:"BigQuery"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Networking"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"VPC"}),"\n",(0,r.jsx)(e.li,{children:"Cloud DNS"}),"\n",(0,r.jsx)(e.li,{children:"Cloud CDN"}),"\n",(0,r.jsx)(e.li,{children:"Cloud Load Balancing"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Security"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"IAM"}),"\n",(0,r.jsx)(e.li,{children:"Cloud KMS"}),"\n",(0,r.jsx)(e.li,{children:"Secret Manager"}),"\n",(0,r.jsx)(e.li,{children:"Cloud Armor"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(e.h2,{id:"azure-deployment",children:"Azure Deployment"}),"\n",(0,r.jsx)(e.h3,{id:"services-2",children:"Services"}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Compute"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"AKS"}),"\n",(0,r.jsx)(e.li,{children:"Virtual Machines"}),"\n",(0,r.jsx)(e.li,{children:"App Service"}),"\n",(0,r.jsx)(e.li,{children:"Functions"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Storage"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Blob Storage"}),"\n",(0,r.jsx)(e.li,{children:"Azure SQL"}),"\n",(0,r.jsx)(e.li,{children:"Files"}),"\n",(0,r.jsx)(e.li,{children:"Cosmos DB"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Networking"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Virtual Network"}),"\n",(0,r.jsx)(e.li,{children:"Azure DNS"}),"\n",(0,r.jsx)(e.li,{children:"CDN"}),"\n",(0,r.jsx)(e.li,{children:"Application Gateway"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Security"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Azure AD"}),"\n",(0,r.jsx)(e.li,{children:"Key Vault"}),"\n",(0,r.jsx)(e.li,{children:"Security Center"}),"\n",(0,r.jsx)(e.li,{children:"WAF"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(e.h2,{id:"best-practices",children:"Best Practices"}),"\n",(0,r.jsx)(e.h3,{id:"infrastructure",children:"Infrastructure"}),"\n",(0,r.jsxs)(e.ol,{children:["\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Resource Management"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Use IaC"}),"\n",(0,r.jsx)(e.li,{children:"Tag resources"}),"\n",(0,r.jsx)(e.li,{children:"Monitor usage"}),"\n",(0,r.jsx)(e.li,{children:"Optimize costs"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Networking"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Secure VPC"}),"\n",(0,r.jsx)(e.li,{children:"Use CDN"}),"\n",(0,r.jsx)(e.li,{children:"Load balance"}),"\n",(0,r.jsx)(e.li,{children:"Monitor traffic"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Storage"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Choose right type"}),"\n",(0,r.jsx)(e.li,{children:"Backup data"}),"\n",(0,r.jsx)(e.li,{children:"Monitor usage"}),"\n",(0,r.jsx)(e.li,{children:"Secure access"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Security"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Use IAM"}),"\n",(0,r.jsx)(e.li,{children:"Encrypt data"}),"\n",(0,r.jsx)(e.li,{children:"Monitor threats"}),"\n",(0,r.jsx)(e.li,{children:"Follow compliance"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(e.h3,{id:"deployment",children:"Deployment"}),"\n",(0,r.jsxs)(e.ol,{children:["\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Automation"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Use CI/CD"}),"\n",(0,r.jsx)(e.li,{children:"Automate testing"}),"\n",(0,r.jsx)(e.li,{children:"Monitor deployment"}),"\n",(0,r.jsx)(e.li,{children:"Handle rollbacks"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Scaling"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Auto-scale"}),"\n",(0,r.jsx)(e.li,{children:"Load balance"}),"\n",(0,r.jsx)(e.li,{children:"Monitor capacity"}),"\n",(0,r.jsx)(e.li,{children:"Optimize resources"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Monitoring"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Track metrics"}),"\n",(0,r.jsx)(e.li,{children:"Set alerts"}),"\n",(0,r.jsx)(e.li,{children:"Log events"}),"\n",(0,r.jsx)(e.li,{children:"Analyze performance"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(e.li,{children:["\n",(0,r.jsx)(e.p,{children:(0,r.jsx)(e.strong,{children:"Maintenance"})}),"\n",(0,r.jsxs)(e.ul,{children:["\n",(0,r.jsx)(e.li,{children:"Regular updates"}),"\n",(0,r.jsx)(e.li,{children:"Backup data"}),"\n",(0,r.jsx)(e.li,{children:"Monitor health"}),"\n",(0,r.jsx)(e.li,{children:"Handle incidents"}),"\n"]}),"\n"]}),"\n"]})]})}function x(n={}){const{wrapper:e}={...(0,s.R)(),...n.components};return e?(0,r.jsx)(e,{...n,children:(0,r.jsx)(o,{...n})}):o(n)}}}]);