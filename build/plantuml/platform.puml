@startuml
!pragma layout smetana

' Custom styles
skinparam packageBackgroundColor transparent
skinparam packageBorderColor #666666
skinparam componentBorderColor #666666
skinparam componentBackgroundColor white
skinparam ArrowColor #666666
skinparam padding 4
skinparam roundCorner 20
skinparam NoteBackgroundColor #FFFFF0
skinparam NoteBorderColor #666666

title 
    91 Platform Architecture showing the relationship between 91 Life, Heart+, and the Matrics platform  
  This diagram shows the complete platform stack, from business applications down to core services.
  All components follow a vertical dependency model where higher-level services depend on lower-level ones.
end title


package "91 Life" #LightBlue {
    note bottom of "91 Life"
      Business domain containing healthcare
      applications and platforms
    end note

    package "Heart+ Application" #LightGrey {
        component [Billing Application] as billing
        component [Device Acquisition\nServices] as deviceAcq
        component [PDF Processing] as pdf
        component [Device Connectivity\nServices] as deviceConn
        component [Reporting &\nAnalytics] as reporting
        component [PRiority Routing\nServices] as priority

        note left of "Heart+ Application"   
          Handles integration with medical devices
          from various manufacturers (Abbott,
          Medtronic, Boston Scientific)
        end note
    }

    package "Healthcare Platform" #LightGrey {
        component [FHIR \nData Services] as fhir
        component [HL7 Parser] as hl7
        component [EMR Integration] as emr
        component [Smart\nFHIR] as smartFhir
        component [Data Mapping\nServices] as dataMap
        component [Data Anonymization\nServices] as dataAnon

        note left of "Healthcare Platform"  
          FHIR-compliant services for
          healthcare data interoperability
        end note
    }
}

package "Matrics" #LightGreen { 

    package "Internal Apps Platform" #LightGrey {
        package "Huly Ecosystem" #RosyBrown { 
            component [Collaboration] as collab
            component [Documents] as docs
            component [Project\nManagement] as projMgmt
            component [Email &\nCalendars] as email           
            component [Internal Drive] as drive
            component [Ticketing &\nProject Management] as ticketing
            component [Priorities\nManagement] as priorities
            component [HR Management] as hr           

            note left of "Huly Ecosystem"
              Internal collaboration tools
              for team communication and
              document management
            end note
        }

        package "ML Ops & R&D" #RosyBrown { 
            component [AI Research\nPlatform] as aiResearch
            component [Model\nManagement] as modelMgmt
            component [Model Training] as modelTrain
            component [Data Pipelines] as dataPipes           
            component [Data Privacy\nManagement] as dataPrivacy
            component [Jupyter Notebooks] as jupyter
            component [Data Annotation] as dataAnnotation
            component [Model Drift\nManagement] as modelDrift           

            note left of "ML Ops & R&D"
              ML model lifecycle management
              including versioning, deployment,
              and monitoring
            end note
        }

        package "KPIs" #RosyBrown { 
            component [Teams KPIs \n& Analytics] as teamKpi
            component [Testing KPIs \n& Analytics] as testKpi
            component [Infrastructure\nDashboards\n& KPIs] as infraKpi
            component [Project\nScorecards] as projScore           
        }
    }

    package "Platform : Core Services & ML Ops" #LightGrey {
        note left of "Platform : Core Services & ML Ops" 
          Foundation layer providing core
          infrastructure and development services
        end note

        package "Dapr: Local & K8S" #RosyBrown { 
            ' Core Infrastructure
            component [PubSub] as pubsub
            component [Authentication &\nEntitlement] as auth
            component [Event Sourcing] as events
            component [Configuration] as config
            component [State Management] as state
            component [Secrets Management] as secrets
            
            ' API & Development
            component [Languages Support\n\nGo/Java/Python/TypeScript] as lang
            component [gRPC & REST APIs] as api
            
            ' Operations
            component [Observibilityt] as observe
            component [Canary Deployments] as canary

            note left of "Dapr: Local & K8S"
              Dapr-based microservices platform
              providing core infrastructure capabilities
            end note
        }

        package "SDKs" #RosyBrown { 
            component [Data Lineage] as lineage
            component [CQRS &\n Event Sourcing] as cqrs
            component [BiTemporality] as biTemp
            component [ODRL] as odrl

            note left of "SDKs"
              Development frameworks and
              patterns for building applications
            end note
        }

        package "Data Services" #RosyBrown { 
            component [Blockchain & DLT\n Services] as blockchain            
            component [Database\n Services] as db            
        }

        package "ML Ops" #RosyBrown { 
            component [Model Deployement] as modelDeploy
            component [Model Monitoring] as modelMonitor
        }

        package "UI Services" #RosyBrown { 
            component [Application Shell] as appShell
            component [Dashboarding\n\nAnalytics\nReporting] as dashboard
        }

        package "Ops Services" #RosyBrown { 
            component [Metrics, Tracing &\nLogs Services] as metrics
            component [Data Vectorization &\nLLM Services] as llm
        }
    }
}

' Vertical Relationships
[91 Life] -[#4B0082,thickness=2]down-> [Matrics]
[Heart+ Application] -[#4B0082,thickness=2]down-> [Healthcare Platform]
[Healthcare Platform] -[#4B0082,thickness=2]down-> [Platform : Core Services & ML Ops]
[Healthcare Platform] -[#4B0082,thickness=2]down-> [Internal Apps Platform]
[Internal Apps Platform] -[#4B0082,thickness=2]down-> [Platform : Core Services & ML Ops]

' Add legend
legend bottom
  |= Component |= Description |
  |<#LightBlue>   | 91 Life Domain - Healthcare business applications |
  |<#LightGreen>  | Matrics Platform - Core platform services |
  |<#RosyBrown>   | Core Services - Infrastructure and development services |
  |<#LightGrey>   | Application Layer - Business logic and UI |
endlegend

@enduml
