---
marp: true
theme: default
paginate: true
header: "91 Platform: Scalable, Agile, Secure"
footer: "Confidential - 91.life"
style: |
  section {
    background-color: #fff;
    color: #333;
    font-family: 'Arial', sans-serif;
    padding: 30px 50px;
  }
  h1 {
    color: #0066cc;
    font-size: 2em;
    margin-bottom: 0.5em;
  }
  h2 {
    color: #004d99;
  }
  strong {
    color: #0066cc;
  }
  ul {
    margin-left: 1em;
    margin-right: 1em;
  }
  .grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2em;
    align-items: center;
  }
  .triple {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 1em;
  }
  .center-image {
    text-align: center;
    margin: 0 auto;
  }
---

<!-- Slide 1 -->

# Executive Summary

<div>

**Transforming Healthcare Through Adaptive Technology**

- Cloud-agnostic, AI-powered medical technology platform
- Seamlessly integrates with existing hospital infrastructure
- Built for compliance, scalability, and security from day one
- Full FHIR compliance for healthcare interoperability
- Natively built with and for the use of AI 
- Natively built for DLT and Secure Computing 


</div>

---

<!-- Slide 2 -->
# Technical Architecture Overview

<div>

**Versatile Deployment with Maximum Flexibility**

- Cloud-agnostic: AWS, Azure, GCP, or on-premise
- Hybrid deployment model for sensitive healthcare environments
- Dapr (Distributed Application Runtime) foundation enables portable microservices
- Stateless design allows elastic scaling during peak demand periods
- Zero vendor lock-in advantages for global healthcare systems

</div>

---

<!-- Slide 3 -->
# Infrastructure as Code

<div>

**Automated Deployment and Scaling**

- Complete infrastructure defined and managed with Terraform
- Repeatable, auditable deployments across environments
- 95% reduction in configuration errors versus manual deployment
- Disaster recovery in minutes rather than days
- Automated compliance-ready infrastructure with built-in audit trails

</div>

---

<!-- Slide 4 -->
# Technology Stack

<div>

**Engineered for Performance and Reliability**

- Backend services built with Go for maximum efficiency
- 70% lower resource utilization compared to legacy systems
- 99.99% uptime across all deployments
- Micro-Frontend React architecture for modular, customizable UI
- Dynamic deployment allows feature rollouts without downtime

</div>

---

<!-- Slide 5 -->
# FHIR-Based Data Architecture

<div>

**Standards-Driven Healthcare Interoperability**

- Complete implementation of FHIR standards for data and communications
- Bitemporal data schemas capturing both "as-of date" and "date of knowledge"
- OpenLineage integration for complete data lineage tracking
- Enables temporal auditing, retrospective analysis, and regulatory compliance
- Supports both R4 and R5 FHIR resources with backward compatibility

</div>

---

<!-- Slide 6 -->
# Access Control & Governance

<div>

**Enterprise-Grade Security & Compliance**

- Granular permission management using ODRL (Open Digital Rights Language)
- Role-based, attribute-based, and context-aware access controls
- Automated policy enforcement across all system components
- Real-time compliance monitoring and violation alerts
- Simplified audit processes for regulatory requirements
- Zero-trust architecture with cryptographic verification

</div>

---

<!-- Slide 7 -->
# AI-Native Platform

<div>

**Intelligence Built Into Every Layer**

- Continual learning system that improves with usage
- NLP exploration for all system logs and system traces
- NLP exploration for all application data
- ML Platform built from the ground up for internal and external researchers  
- Native Data Anonymisation and Secure Computing (FHE/SGX)

</div>

---

<!-- Slide 8 -->
# Data Lineage & Traceability

<div>

**Complete Visibility Across the Data Lifecycle**

- OpenLineage implementation for end-to-end data traceability
- Complete change history with bitemporal data modeling
- Immutable audit trail of all data transformations
- Point-in-time recovery capabilities for any historical state
- Critical for clinical research, regulatory compliance, and legal discovery
- Enables "time travel" through patient data history

</div>
