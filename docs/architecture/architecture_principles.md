# Architecture Principles

This document outlines the core architectural principles that must guide all development and design of our SaaS platform. These principles are fundamental to maintaining a robust, maintainable, and scalable system.

## 1. Development Process and Tooling

### 1.1 JIRA-Driven Development
Every development task must be tracked through JIRA tickets. This ensures:
- Clear traceability of changes
- Proper documentation of requirements and decisions
- Consistent project management
- Ability to track progress and dependencies

### 1.2 You Professional Software Engineers ... not Hackers
We are software engineers, not hackers:
- Write production-grade, maintainable code
- Follow established software engineering practices
- Document design decisions and code
- Write comprehensive tests
- Consider security, performance, and scalability
- Use proper version control and branching strategies
- Follow code review processes
- Consider long-term maintenance and support

### 1.3 Open Source and License Compliance
All frameworks and tools used within our software must be Open Source with liberal licenses (EPL, APL, MIT, BSD, AGPL, GPL). This ensures:
- Freedom to modify and customize
- No vendor lock-in
- Community support and transparency


### 1.4 Framework Selection and Customization
We must maintain a balance between using existing solutions and custom development:
- Critical frameworks (<PERSON><PERSON>, Dapr, <PERSON>mill, Marquez) must be debuggable, maintainable, and customizable
- We can reuse parts of software but not entire platforms
- Every framework/tool requires an Architecture Decision Record (ADR) and approval from the architecture group

```plantuml
@startuml
skinparam componentStyle rectangle

[Development Request] --> [JIRA Ticket]
[JIRA Ticket] --> [ADR Creation]
[ADR Creation] --> [Architecture Review]
[Architecture Review] --> [Approval]
[Approval] --> [Implementation]
[Implementation] --> [Documentation]
[Documentation] --> [Testing]

note right of [Architecture Review]
  Must be approved by
  Architecture Group
  (Alban & Jean)
end note
@enduml
```

## 2. Design Principles

### 2.1 KISS (Keep It Simple, Stupid)
Complexity should be avoided whenever possible:
- Prefer simple, straightforward solutions over complex ones
- Focus on maintainability and readability
- Document the reasoning behind any necessary complexity

### 2.2 Separation of Concerns
Apply separation of concerns everywhere:
- Use clear boundaries between components
- Keep domain logic isolated from technical implementation details for cleaner architecture
- Isolate different responsibilities into distinct modules
- Keep configuration separate from code
- Separate deployment concerns from application logic
- Maintain clear distinction between public and internal APIs

### 2.3 The Best is the Enemy of the Good 
Striving for perfection can prevent progress:
- Focus on delivering working solutions that meet requirements
- Avoid over-engineering and excessive refinement
- Prioritize practical solutions over theoretical perfection
- Embrace iterative improvement over perfect first attempts
- Remember that "good enough" is often better than "perfect but late"

### 2.4 Early Optimization is the Root of All Evil
Premature optimization can lead to problems:
- Focus on correctness and clarity first
- Optimize only after identifying actual bottlenecks
- Measure before optimizing
- Avoid complex optimizations that make code harder to maintain
- Let performance requirements drive optimization decisions

### 2.5 Minimize External Dependencies
Minimize dependency on external services and tools:
- Maintain control over critical functionality
- External libraries often include more functionality than needed, introducing unnecessary bloat 
- Reduce the risk of being forced into disruptive upgrades

### 2.6 Problem-First Approach
Before implementing any solution:
- Analyze the problem thoroughly
- Identify gaps in current stack
- Evaluate build vs. buy options
- Consider hybrid approaches
- Document the decision-making process (ADR)

### 2.7 Pay Now or Pay More
Not investing early in good design will increase technical debt, reduce agility, and cost significantly more to undo later. This principle emphasizes the importance of proper upfront investment in architecture and design decisions.

- Conduct thorough design reviews before implementation
- Document architectural decisions and their rationale
- Consider long-term implications of design choices
- Balance immediate needs with future scalability
- Invest in automated testing and quality assurance
- Regular architecture reviews to identify potential 

### 2.8 Automation First
What is not automated is a huge cost to the company. Strive for 100% automation of all tasks, especially those pertaining to platform runtime maintenance, deployment, upgrade, and onboarding.

- Automate all deployment and release processes
- Implement self-healing and auto-recovery mechanisms
- Automate platform monitoring and alerting
- Create automated onboarding and provisioning workflows
- Implement automated testing at all levels
- Automate documentation generation and updates
- Establish automated compliance and security checks
- Create automated backup and disaster recovery procedures

### 2.9 Clear Naming
Badly naming things only adds misery to the world. Clear, consistent, and meaningful names are essential for maintainable and understandable code.

- Use descriptive and self-explanatory names for all components
- Follow consistent naming conventions across the platform
- Avoid abbreviations unless universally understood
- Use domain-specific terminology appropriately
- Maintain naming consistency across related components
- Document naming conventions and patterns
- Review and refactor unclear names during code reviews

## 3. Service Architecture

### 3.1 Treat Everything as Data
All platform elements must be treated as data:
- Code as data
- Models as data
- Documents as data
- Configuration as data
- Metadata for everything

### 3.2 Service Design Principles
All services must follow these principles:

1. **Mandatory Dapr Integration**
   - All services must be "Daprized"
   - Leverage Dapr building blocks
   - Ensure consistent service-to-service communication

2. **Systematic Interface-Driven Development**
   - Design services around clear interfaces
   - Hide implementation details
   - Focus on API contracts
   - Make API's acceptance a milestone before progressing on implementation

3. **Systematic Command-Based Service Design**
   - Design services around commands they execute
   - Commands will uniquely define the API that the service offers be it REST, gRPC, MCP, GraphQl etc...
   - Clear separation of concerns
   - Event-driven architecture justify when not appropriate

4. **Keep Transverse Concerns ... Transverse**
   Focus on your business logic and implement those concerns through injection by the platform mechanisms through its SDKs:
   - I/O operations
   - Configuration management
   - Transaction management
   - Security
   - Entitlement
   - DataLineage
   - Monitoring
   - Session Context

### 3.3 Component Replaceability
Switching components, frameworks, or tools should be a trivial task:
- Design services with clear boundaries and interfaces
- Use dependency injection and inversion of control
- Implement adapter patterns for external dependencies
- Keep framework-specific code isolated
- Document integration points and dependencies
- Use abstraction layers to decouple from specific implementations

```plantuml
@startuml
skinparam componentStyle rectangle

package "Service Layer" {
  [Service Interface] as SI
  [Command Handler] as CH
  [Transverse Concerns] as TC
}

package "Transverse Concerns" {
  [I/O] as IO
  [Configuration] as Config
  [Transactions] as Tx
  [Security] as Sec
  [Entitlement] as Ent
  [DataLineage] as DL
  [Monitoring] as Mon
  [Session Context] as Sc
}

SI --> CH
CH --> TC
TC --> IO
TC --> Config
TC --> Tx
TC --> Sec
TC --> Ent
TC --> DL
TC --> Mon
TC --> Sc

note right of TC
  All services must implement
  these concerns consistently
end note
@enduml
```

## 4. Development Standards

### 4.1 Only use Supported Languages
Development is restricted to:
- Go
- Python
- TypeScript/JavaScript
- Java

### 4.2 Documentation and Testing for Everything
- All activities (from requirements down to deployment) must be documented
- Comprehensive test coverage
- Documentation must also be versioned in Git

### 4.3 Everything as Code
Following Rex's EaaC principle:
- Version control for all artifacts
- Use Markdown for documentation
- Use PlantUML for diagrams
- Use Terraform for infrastructure
- LLM Rules as Code

### 4.4 Data Lineage Everywhere
- Track data flow through the system using the SDk - not doing it must be justified and approved by architecture
- Document data transformations
- Maintain audit trails
- Enable data quality monitoring

### 4.5 MCP Everywhere
All services must be compatible with the Model Context Protocol (MCP):
- Enable service activation and interaction through LLMs
- Provide standardized context and metadata for LLM understanding
- Maintain proper context preservation across LLM interactions

### 4.6 use Staged Event Driven Architecture
The platform must implement Staged Event-Driven Architecture (SEDA):
- Break complex processes into stages
- Enable better resource utilization
- Improve system scalability
- Support asynchronous processing

### 4.7 Command Replay and State Reconstruction
The system must support complete state reconstruction through command replay:
- All commands entering the system must be persisted
- Commands must be immutable and versioned
- State must be deterministically reconstructible
- Support point-in-time state reconstruction
- Enable system recovery through command replay
- Maintain command ordering and causality
- Enable system testing through command replay
- Allow for system migration and upgrades through replay
- Support disaster recovery scenarios

### 4.9 Infrastructure Agnostic Execution
The platform must be developed in an infrastructure-agnostic manner:
- Design all mechanisms to work across different deployment scenarios:
  - On-premise data centers
  - Public cloud providers (Google Cloud, AWS, Azure)
  - Local development environments (laptops)
- Ensure consistent behavior across all deployment environments
- Design for portability from the start
- Use infrastructure as code for consistent deployment
- Maintain development parity between all environments
- Test across all deployment scenarios
- Use containerization for consistent runtime environments
- Design for offline capabilities where required

### 4.10 Guarantee Version Coexistence
The platform must support multiple versions of components running simultaneously:
- Allow different versions of backend and frontend components to coexist
- Support version-aware routing and service discovery
- Maintain backward compatibility between versions
- Support gradual migration and canary deployments
- Enable client-side version selection
- Maintain version-specific documentation and SDKs
- Support version deprecation policies

## 5. API Design

### 5.1 Standard API Façades
- Start any design top down from API to Implementation
- Use standard APIs as façades where possible
- Hide implementation details
- Provide consistent interfaces through commands (not doing it requires justification and approval)

### 5.2 SDK Design Principles
When designing SDKs:
- Focus on user experience
- Hide implementation complexity
- Maintain backward compatibility
- Follow language-specific best practices

```plantuml
@startuml
skinparam componentStyle rectangle

package "SDK Layer" {
  [SDK API] as API
  [Implementation] as IMP
  [Complexity] as COMP
}

package "User Application" {
  [SDK User] as USER
}

USER --> API
API --> IMP
IMP --> COMP

note right of API
  SDK API should be
  clean and simple
end note

note right of COMP
  Implementation complexity
  hidden from user
end note
@enduml
``` 