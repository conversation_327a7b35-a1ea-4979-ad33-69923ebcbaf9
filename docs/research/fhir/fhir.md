# FHIR Database Strategy

## 1. Introduction

This document summarizes the research and decisions regarding the database
strategy for our scalable FHIR server backend. The core challenge is balancing 
the need to efficiently store and query complex FHIR resources, support standard
FHIR search capabilities (including relationships and normalization), while
ensuring the system can scale horizontally to handle potentially massive 
healthcare data volumes and high throughput.

## 2. Core Database Requirements for FHIR

Based on the FHIR specification and anticipated usage patterns, the database solution
must effectively support:

* **Resource Storage:** Efficiently store FHIR resources, which are complex, semi-structured documents (often 
  represented as JSON).
* **FHIR Search:** Execute standard FHIR search parameters efficiently, including searches on nested fields within 
  resources.
* **Search Normalization:** Support common search expectations like case-insensitivity and diacritic-insensitivity
  for string parameters, and potentially normalization for other types (dates, tokens, quantities).
* **Relationship Handling:** Efficiently retrieve related resources as specified by `_include` and `_revinclude` 
  parameters.
* **High Availability (HA):** Ensure data durability and service uptime through redundancy/failover mechanisms.
* **Horizontal Scalability:** Ability to scale out by adding more servers to handle growing data volumes and user 
  load (sharding).
* **Write Performance:** Maintain acceptable insert and update performance even with the necessary indexes for 
  efficient searching.
* **Data Integrity:** Ensure consistency appropriate for healthcare data (level of transactional guarantee needed).

## 3. Database Options Explored

We considered several database categories:

### 3.1. PostgreSQL (Traditional SQL + Extensions)

* **Models:** HAPI FHIR (normalized index tables) or Aidbox (JSONB + Functional Indexes).
* **Pros:**
    * Mature ACID transactions and relational integrity features.
    * Powerful SQL query language, including native `JOIN` operations ideal for efficiently handling 
      `_include`/`_revinclude` (often lower latency).
    * Strong JSONB support allows storing FHIR documents effectively.
    * Functional Indexes (Aidbox style) allow indexing expressions on JSONB data, enabling search within documents.
* **Cons:**
    * **Horizontal scaling (sharding) is traditionally complex.** Cross-shard JOINs, transactions, and referential 
      integrity are difficult to manage efficiently without specialized extensions (e.g., CitusDB) or architectures.
      Often requires significant operational expertise or vertical scaling limits.

### 3.2. MongoDB (Document Database)

* **Models:** Nested Map or Binary Proto + Extracted Search Params.
* **Pros:**
    * **Excellent horizontal scaling** via native, mature sharding capabilities. Easier to scale out for large
      datasets/throughput compared to traditional SQL sharding.
    * Document model aligns naturally with FHIR resource structure.
    * Flexible schema evolution.
* **Cons:**
    * **No native JOINs across collections.** Handling `_include`/`_revinclude` requires multiple application-level
      queries (increasing latency) or complex `$lookup` aggregations.
    * Multi-document/multi-shard ACID transactions are more complex than traditional SQL.
    * Relational integrity is not enforced by the database; requires application logic.

### 3.3. Distributed SQL (Spanner, CockroachDB)

* **Models:** Relational tables, potentially with JSON/JSONB columns.
* **Pros:**
    * Designed for **both horizontal scalability AND strong consistency** (often serializable ACID).
    * Support distributed SQL **JOINs**, potentially offering low latency for `_include`/`_revinclude` similar
      to traditional SQL but in a scalable architecture.
    * Often provide SQL interfaces (PostgreSQL compatible for CockroachDB).
    * Support JSON types and functional index equivalents (Expression Indexes in CockroachDB, Generated Columns
      in Spanner).
* **Cons:**
    * Can be complex to operate, tune, and understand (distributed system nuances).
    * Potentially higher cost (especially managed services like Spanner).
    * Newer ecosystem compared to traditional SQL or MongoDB.

## 4. Suggested Approach: CockroachDB (Distributed SQL)

Based on the requirements, particularly the need for both **efficient relationship handling (JOINs for
`_include`/`_revinclude`) and native horizontal scalability**, we suggest to proceed with **CockroachDB**.

**Rationale:** CockroachDB provides a unique combination that addresses the core challenges. It allows
us to leverage the power and familiarity of SQL for complex queries and JOINs while offering the seamless
horizontal scalability required for potentially massive FHIR datasets. Its support for JSONB and PostgreSQL-compatible
Expression Indexes directly addresses the need to store and efficiently query FHIR resource documents.

### 4.1. Proposed Data Model (Table per Resource Type)

We will use a relational schema in CockroachDB, likely with a table per FHIR resource type:

```sql
-- Example table for Patients (simplified)
CREATE TABLE patients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- Internal primary key
    logical_id STRING NOT NULL UNIQUE,           -- FHIR logical ID (e.g., 'pat123')
    version_id STRING NOT NULL,                  -- FHIR version ID
    last_updated TIMESTAMPTZ NOT NULL,           -- FHIR meta.lastUpdated
    is_deleted BOOL DEFAULT false,              -- For soft deletes
    resource_data JSONB NOT NULL,                -- Stores the full FHIR resource as JSONB
    -- Potentially add other top-level indexed metadata if frequently queried without JSONB access
    INDEX (last_updated),
    INDEX (is_deleted) WHERE is_deleted=true -- Index only deleted resources if needed often
    -- CockroachDB automatically interleaves tables for locality if desired (e.g., observations interleaved with patients)
);

-- Example table for Observations (simplified)
CREATE TABLE observations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    logical_id STRING NOT NULL UNIQUE,
    version_id STRING NOT NULL,
    last_updated TIMESTAMPTZ NOT NULL,
    is_deleted BOOL DEFAULT false,
    resource_data JSONB NOT NULL,
    -- Index top-level metadata
    INDEX (last_updated)
);
```

### 4.2. Indexing Strategy
We will leverage CockroachDB's indexing capabilities:

- Primary/Standard Indexes: On primary keys (id) and frequently queried top-level metadata (logical_id, last_updated).
- Expression Indexes (Functional Indexes): These are crucial for searching within the resource_data JSONB column.
  We will create indexes on expressions that extract and potentially normalize FHIR search parameter values.
  ```sql
  -- Example: Index for case-insensitive search on first family name
  CREATE INDEX idx_patient_family_normalized ON patients ((lower(resource_data->'name'->0->>'family')));
  
  -- Example: Index for Observation code (first coding system/code)
  CREATE INDEX idx_observation_code ON observations ((resource_data->'code'->'coding'->0->>'system'), (resource_data->'code'->'coding'->0->>'code'));
  
  -- Example: Index for Observation date
  CREATE INDEX idx_observation_date ON observations (((resource_data->>'effectiveDateTime')::TIMESTAMPTZ)); -- Cast JSON text to timestamp
  ```
- GIN Indexes (Optional): For more complex JSONB searching (e.g., checking for the existence of keys or matching
  multiple values within arrays using operators like @>), GIN indexes on the resource_data column could be used,
  though targeted expression indexes are generally preferred for specific search parameters.

### 4.3. Normalization Strategy
Normalization (case-insensitivity, diacritics, UTC dates) will primarily be handled within the Expression Index
definitions:

- Strings: Use functions like `lower()` within the index definition. Accent removal might require custom functions
  or pre-processing if not built-in. Search terms must be similarly normalized at query time.
- Dates: Cast JSON string dates to `TIMESTAMPTZ` within the index expression, which normalizes them to UTC for
  reliable comparison.
  Other Types: Extract relevant components (e.g., token system/code, reference parts) using JSONB operators
  within the index expressions.

### 4.4. Handling Relationships (`_include`/`_revinclude`)
Leverage standard SQL JOINs:

1. The application translates the FHIR search (with `_include` or `_revinclude`) into a SQL query.
2. This query JOINs the resource table(s) with appropriate index expressions or other tables based on the
  relationships defined by the FHIR parameters.
  ```sql
  -- Conceptual Example: GET /Patient?_id=pat123&_revinclude=Observation:subject
  SELECT
      p.resource_data AS patient_resource,
      o.resource_data AS observation_resource
  FROM
      patients p
  LEFT JOIN -- Find observations referencing this patient
      observations o ON (o.resource_data->'subject'->>'reference') = ('Patient/' || p.logical_id) -- Join based on reference path in JSONB
  WHERE
      p.logical_id = 'pat123'; -- Find the specific patient
  -- Note: Performance depends on having an appropriate expression index on the reference path in the 'observations' table.
  ```
3. CockroachDB's distributed SQL engine optimizes and executes the JOIN across the necessary nodes in the cluster.
4. Results are returned to the application for Bundle construction.

## 4.5. Performance & Scalability
- Reads: Performance relies on well-designed Expression Indexes for specific searches and efficient distributed
  JOIN execution for includes/revincludes.
- Writes: Indexes add overhead. CockroachDB manages Raft replication for consistency, which impacts write latency.
  Tuning batching and transaction sizes is important.
- Scalability: Add more CockroachDB nodes. The database automatically rebalances data ranges (shards) across nodes.
  Performance scales near-linearly for many workloads if schema/queries are designed well to avoid hotspots.