# HL7 Transmission Ingestion and Parsing Flow

This document details the flow for ingesting and parsing HL7 transmissions and subsequently outputting those HL7 messages to be consumed by other applications. It also includes the addition of a consumer application designed to save the messages to a database for reprocessing or auditing purposes.

## Overview of the Flow

1. **Ingestion**:

   - HL7 transmissions are sent by multiple couriers (e.g., Courier 1, Courier 2, Courier n).
   - These transmissions are received by **Mirth**, which acts as the entry point for handling incoming HL7 data.

2. **Parsing**:

   - <PERSON><PERSON> forwards the HL7 transmission via a REST endpoint.
   - The HL7 Parser processes the transmission and converts it into a Protobuf representation of the HL7 transmission.

3. **Output**:

   - The parsed HL7 representation is made available for multiple consumer applications.
   - Each consumer application handles the message based on its specific functionality.

4. **Storage and Reprocessing**:

   - A dedicated consumer application stores parsed HL7 messages into a database for later reprocessing or auditing.

---

## Step-by-Step Flow

### 1. HL7 Transmission Ingestion

- **Sources**: HL7 messages are provided by multiple couriers (e.g., Courier 1, Courier 2, etc.), which save the HL7 transmissions as files in a folder locally on the server.
- **Intermediate Handler**:
  - <PERSON><PERSON> gets the HL7 files and sends them to the HL7 Parser via its REST API endpoint, serving as a middleware platform to standardize and preprocess incoming HL7 transmissions.

### 2. HL7 Parsing

- The HL7 Parser receives the raw HL7 data via REST.
- It processes and converts the data into a standardized HL7-compatible representation.
- The parser ensures compatibility with downstream consumer applications.

### 3. Message Consumption

- The parsed Protobuf representation of the HL7 transmission is broadcast to multiple consumers:
  - **Consumer 1** and **Consumer 2**: Handle specific business logic or processing requirements.
  - **Heart+ Consumer**:
    - Integrates with the Heart+ platform.
    - Stores relevant data into the **Heart+ Database**.
  - **Transmission Storage Service**:
    - Saves the raw HL7 messages and parsed Protobuf representations into **MongoDB** as the Raw Transmissions Database for reprocessing and auditing purposes.

### 4. Adding a New Consumer for Storage

To enhance the system, a new consumer application can be added to specifically store parsed HL7 messages for compliance, auditing, or later reprocessing.

- **Purpose**:

  - Ensure all HL7 transmissions are saved to a dedicated database.
  - Enable reprocessing of transmissions if downstream systems experience failures.
  - Provide a trail for auditing and regulatory compliance.

- **Implementation**:

  - Develop a new **Consumer App**.
  - Configure it to subscribe to the HL7 output stream from the HL7 Parser.
  - Save incoming HL7 messages into a **Transmission Storage Database**.

## Diagram of the Flow

Below is a visual representation of the described HL7 ingestion and processing flow:

```plantuml
@startuml
skinparam rectangle {
  BackgroundColor #f2f2f2
  BorderColor #333333
  FontColor #000000
}

rectangle "Courier 1" as C1
rectangle "Courier 2" as C2
rectangle "Courier n" as Cn

rectangle "MIRTH" as Mirth #lightblue
rectangle "HL7 Parser" as Parser #pink

rectangle "Consumer 1" as Consumer1 #lightyellow
rectangle "Consumer 2" as Consumer2 #lightyellow
rectangle "Heart+ Consumer" as HeartPlusConsumer #lightgreen
rectangle "Heart+ Database" as HeartPlusDB #green
rectangle "Transmission Storage Service" as StorageService #orange
rectangle "MongoDB (Raw Transmissions Database)" as RawDB #red

C1 --> Mirth
C2 --> Mirth
Cn --> Mirth

Mirth -> Parser : REST (HL7)

Parser -[dashed]-> Consumer1 : Protobuf Representation
Parser -[dashed]-> Consumer2 : Protobuf Representation
Parser -[dashed]-> HeartPlusConsumer : Protobuf Representation
Parser -[dashed]-> StorageService : Protobuf Representation

HeartPlusConsumer --> HeartPlusDB
StorageService --> RawDB

legend bottom left
  Dotted Arrows (dashed) indicate event-based communications.
endlegend
@enduml
```

---

By following this flow and adding the proposed consumer app, the system will be able to securely and reliably store all HL7 transmissions, ensuring compliance and enabling reprocessing or auditing as needed.