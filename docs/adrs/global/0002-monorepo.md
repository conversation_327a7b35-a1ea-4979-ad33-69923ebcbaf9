# 2. Monorepo per Project

Date: 2025-02-10

## Status

Proposed

## Context

To streamline project management, ensure consistency, and improve cross-team collaboration, we have decided to 
adopt a monorepo approach for managing all related projects. This includes projects like Healthcare Platform, 
Platform, Heart+, and any future initiatives. The goal is to consolidate code, improve workflows, and make scaling 
more efficient.

## Decision

We will **implement a monorepo for each major project within our organization**. This will include all applications,
services, and shared libraries for a given project.

## Consequences

### Benefits

- **Improved Code Sharing**: Easier management of shared components and libraries across multiple projects.
- **Unified Development Workflow**: Standardized best practices, coding conventions, and CI/CD processes across projects.
- **Simplified Dependency Management**: Reduces version mismatches by keeping all related components in sync.

### Risks & Mitigation

- **Large Repository Size**: As the repository grows, IDEs may struggle with performance on your local device.
- **Access Control**: Teams may require different access levels to the monorepo.

