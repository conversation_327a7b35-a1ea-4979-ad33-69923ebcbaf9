# 4. ODRL as Policy and Entitlement Definition Language

Date: 2025-03-17

## Status

Proposed

## Context

In 91Life's Healthcare platform, defining and enforcing access control policies is critical to ensuring compliance, privacy, and security. We evaluated several approaches for expressing policies and entitlements:

- **XACML:** A mature, widely adopted standard that provides extensive capabilities but comes with significant complexity and verbosity, which may lead to increased maintenance overhead.
- **Self-Implemented Solutions:** These offer flexibility and customization but entail substantial development effort, potential for security vulnerabilities, and long-term maintenance risks.
- **Keycloak Policies:** We evaluated the built-in policies offered by Keycloak for authentication and authorization. However, we decided against a direct dependency on Keycloak’s policy engine because it would limit our flexibility. Relying solely on Keycloak would also constrain our ability to define granular and tailored policies required by our healthcare environment.
- **ODRL (Open Digital Rights Language):** A standardized, less complex alternative designed to express permissions, prohibitions, and obligations clearly and concisely.

Additionally, there is a need for a tool that empowers non-technical users to define access control policies through a user-friendly UI. This interface should translate UI inputs into ODRL policies, allowing our clients to customize access control rules based on their specific requirements.

## Decision

We propose adopting **ODRL** as the standard for defining policies and entitlements within our Healthcare platform, with OPA (Open Policy Agent) serving as the policy evaluation engine. This decision is based on several key factors:

- **Simplicity and Clarity:** ODRL offers a more concise and human-readable syntax compared to XACML, reducing the learning curve for developers and security personnel while simplifying policy management.
- **Granularity and Tailoring:** ODRL enables us to define more granular and tailored policies and entitlements compared to Keycloak’s native options, ensuring that we can meet the specific and diverse needs of our healthcare clients.
- **Avoiding Vendor Lock-In:** By not relying on Keycloak’s policy engine, we avoid creating a dependency that could limit our flexibility or force changes if Keycloak evolves in ways that do not align with our policy requirements.
- **Interoperability and Standardization:** As an established standard, ODRL facilitates easier integration with external systems and ensures that our policy definitions remain consistent and auditable across various platforms.
- **User Empowerment through UI Integration:** A dedicated UI tool will enable non-technical users to define access control policies easily. The tool will convert UI inputs into standardized ODRL policies, ensuring flexibility to meet diverse client-specific requirements.
- **Seamless Integration with OPA:** OPA’s flexible policy evaluation capabilities complement the use of ODRL, allowing us to efficiently enforce and audit access control policies in real-time without significant performance penalties.
- **Regulatory Compliance:** The clarity and auditability provided by ODRL align well with the compliance requirements typical in healthcare environments, supporting both internal and external audits.

## Consequences

Adopting ODRL as our policy definition standard, in conjunction with OPA as our evaluation engine and an intuitive UI tool for policy creation, leads to several benefits and trade-offs:

- **Benefits:**
  - **Ease of Policy Definition:** Simplified syntax makes it easier for teams and non-technical users to define, understand, and maintain access control policies.
  - **Improved Auditability:** Standardized policies enhance transparency and compliance reporting, crucial for regulatory reviews.
  - **Enhanced Interoperability:** Leveraging a recognized standard facilitates integration with third-party systems and supports future scalability.
  - **Client-Centric Customization:** The UI tool enables clients to easily define and adjust access control rules to match their unique needs, ensuring flexibility and personalization.
  - **Greater Granularity:** ODRL allows for more detailed and tailored policy definitions compared to Keycloak’s native options, meeting the stringent requirements of a healthcare environment.
  - **Avoiding Dependency on Keycloak:** This approach reduces the risk of vendor lock-in and maintains our flexibility to evolve our policy management system independently of Keycloak’s internal policies.
  - **Operational Efficiency:** Reduced complexity minimizes the risk of misconfigurations and streamlines the policy enforcement process.
  
- **Trade-offs and Risks:**
  - **Extension Requirements:** While ODRL covers a wide range of access control needs, certain healthcare-specific scenarios might require custom extensions or adaptations.
  - **Community and Support:** Although ODRL is a standard, the community and tooling around it may not be as extensive as those for XACML, requiring investment in internal expertise.
  - **Policy Translation Tool Development:** Developing and maintaining a robust tool, meant to be used by non-technical team, that accurately translates user inputs into ODRL policies will require additional resources and thorough testing to ensure accuracy and compliance.

By choosing ODRL, supported by a user-friendly policy definition tool and OPA for evaluation, we aim to strike a balance between operational simplicity, client flexibility, and robust, compliant policy enforcement. This approach avoids unnecessary dependency on Keycloak’s built-in policy system, granting us greater control and granularity to meet both current demands and future growth in our platform.
