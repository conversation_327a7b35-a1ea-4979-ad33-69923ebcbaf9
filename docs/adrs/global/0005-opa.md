# 5. OPA as Policy Evaluation Engine

Date: 2025-03-17

## Status

Proposed

## Context

Efficient and low-latency authorization is critical to ensuring secure access control while meeting strict compliance and regulatory requirements. As our platform scales and adapts to evolving access control needs, it is essential to select a policy engine that offers both performance and flexibility.

We evaluated several policy engines and decision frameworks to determine the best fit for our environment:

- **Balana + XACML:** Although this combination is mature and feature-rich, its reliance on standalone services and complex processing pipelines introduces higher latency, which could negatively impact system responsiveness. Additionally, XACML policies are notoriously complex and not human-readable, which complicates policy management and increases the likelihood of misconfigurations.
- **Keycloak's Decision Engine:** Keycloak provides built-in policies for authentication and authorization. However, using its decision engine would create an unnecessary dependency, potentially limiting our flexibility and hindering our ability to implement granular, tailored policies required by our healthcare clients.
- **OPA (Open Policy Agent):** OPA offers robust, decoupled policy evaluation capabilities that integrate seamlessly with external data sources. Its support for data loading makes it a natural fit for working with ODRL policies. Moreover, the availability of OPA SDKs allows us to build libraries that can be embedded directly within our applications or integrated into Dapr middleware. This approach minimizes latency by eliminating the need for remote service calls during policy evaluation.

Given these factors, OPA not only meets our performance and flexibility criteria but also aligns with our long-term strategic goals of maintaining an independent, scalable authorization framework.

## Decision

We propose to adopt **OPA** as our policy engine, leveraging its SDKs to build libraries that can be embedded directly within our applications or integrated with Dapr middleware. This approach offers several advantages:

- **Reduced Latency:** By embedding OPA via SDKs within the app or middleware, we eliminate the overhead associated with calling out to a separate service.
- **Seamless Integration with ODRL:** OPA’s ability to load and work with external data allows for a smooth integration with our chosen ODRL policy standard.
- **Flexibility and Modularity:** Using OPA SDKs allows us to tailor policy evaluation logic to our specific needs without being tied to a centralized decision engine.
- **Avoiding Vendor Dependencies:** This approach mitigates the risks of dependency on external policy services such as those offered by Keycloak or standalone OPA deployments.
- **Enhanced Developer Experience:** Embedding policy evaluation directly within the application or middleware streamlines development and maintenance, providing a more cohesive system architecture.
- **Dapr OPA Middleware Updates:** We will update the Dapr OPA middleware to support data loading for ODRL policies, ensuring that policy evaluation has access to all necessary external data for comprehensive authorization decisions.

## Consequences

Adopting OPA as our integrated policy engine through its SDKs brings several benefits along with some considerations:

- **Benefits:**
  - **Performance Improvement:** Direct in-app or middleware policy evaluation reduces network overhead and latency.
  - **Tighter Integration with ODRL:** Enhanced support for ODRL policies through data loading capabilities ensures consistency across our authorization framework.
  - **Modularity and Flexibility:** Allows for dynamic policy updates and more granular control over the evaluation process.
  - **Simplified Architecture:** Reduces the need to manage and scale a separate policy evaluation service.
  
- **Trade-offs and Risks:**
  - **Development Overhead:** Building and maintaining custom libraries using OPA SDKs requires initial development effort and ongoing maintenance.
  - **Complexity in Integration:** Embedding policy evaluation within various parts of the system (applications or Dapr middleware) may increase the complexity of integration and testing.
  - **Dependency on OPA SDK Updates:** We must monitor and adapt to changes in the OPA SDKs to ensure continuous compatibility and performance.
  - **Middleware Update Requirements:** Updating the Dapr OPA middleware to support data loading for ODRL policies introduces additional work and testing to ensure smooth integration and operation.

By choosing to integrate OPA directly through its SDKs and updating our Dapr OPA middleware, we aim to achieve a balance between performance, flexibility, and robust policy evaluation, ensuring that our Healthcare platform remains secure and responsive to evolving authorization requirements.
