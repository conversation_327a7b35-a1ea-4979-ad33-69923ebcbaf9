# 2. Dapr Pub/Sub: Apache Kafka

Date: 2025-02-05

## Status
Proposed

## Context
Dapr provides a set of building blocks for microservices, including a pub/sub mechanism. We needed to choose a reliable, scalable, and widely adopted underlying message broker to support Dapr’s Pub/Sub functionality. Our primary requirements included high throughput, fault tolerance, and support for a wide range of use cases. Evaluating multiple messaging systems led us to consider **Apache Kafka** as a strong candidate.

## Decision
We will use **Apache Kafka** as the underlying technology for the Dapr Pub/Sub Building Block. This choice is driven by Kafka’s proven track record of handling high-volume data streams in a distributed environment, along with a vibrant ecosystem of tooling and community support. Compared to other options like RabbitMQ, Apache Pulsar, or Redis Pub/Sub, Kafka’s partition-based messaging architecture provides stronger guarantees around data durability, ordering, and fault tolerance. RabbitMQ remains more queue-oriented and may require additional components for achieving similar throughput and partitioning, while Apache Pulsar, although also distributed, has a smaller ecosystem. Redis Pub/Sub is lighter-weight but lacks built-in persistence and advanced streaming features. Kafka’s mature ecosystem and robust stream processing capabilities (e.g., Kafka Streams) make it well-suited for powering event-driven applications running under the Dapr Pub/Sub Building Block. This level of scalability and extensibility makes Kafka a superior choice for powering the event-driven applications running under the Dapr Pub/Sub Building Block.

## Consequences

- **Positive**
  - High scalability and fault tolerance for pub/sub scenarios.
  - Broad developer familiarity and extensive ecosystem.
  - Strong tooling support and integrations for observability and security.

- **Negative**
  - Operational complexity, as Kafka requires setup and management of brokers, zookeepers (if using older versions), and configuration.
  - Possibly steep learning curve for new adopters.

By adopting Kafka, we can leverage its robust features for Dapr’s messaging needs, ensuring reliable event streaming for Dapr-based applications. While this does introduce additional overhead in terms of setup and maintenance, Kafka’s versatility and integrations with a broad range of technologies can also be leveraged for other advanced data streaming and processing use cases, making it a valuable investment beyond Dapr’s pub/sub requirements.
