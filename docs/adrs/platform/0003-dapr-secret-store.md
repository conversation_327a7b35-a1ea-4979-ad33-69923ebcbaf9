# 3. Dapr Secret Store: Kubernetes Secrets

Date: 2025-02-05

## Status
Proposed

## Context

Dapr provides a secrets management building block to securely store and retrieve sensitive configuration values. Various secret stores are supported, including cloud-based options such as AWS Secrets Manager, Azure Key Vault, and Google Secret Manager, as well as self-managed solutions like HashiCorp Vault and Kubernetes Secrets. For our use case, we aim to select a secret store that is cloud-agnostic, natively integrates with Kubernetes, and provides secure storage while minimizing operational complexity.

**Kubernetes Secrets** offer a built-in solution for managing sensitive information in a Kubernetes cluster. They allow storing secrets such as API keys, passwords, and certificates in an encrypted format and can be accessed by applications through environment variables, volume mounts, or direct API calls. When used with Dapr, Kubernetes Secrets provide a seamless way to manage application secrets while leveraging Kubernetes’ native RBAC and access controls for security.

## Decision

We will use **Kubernetes Secrets** as the secret store for Dapr. This decision is driven by the following factors:

- **Native Kubernetes Integration**: Kubernetes Secrets are a first-class citizen in Kubernetes and integrate seamlessly with existing cluster configurations and workloads.
- **Cloud-Agnostic**: Unlike cloud-specific secret stores, Kubernetes Secrets work across any Kubernetes deployment, avoiding vendor lock-in.
- **RBAC and Security Policies**: Kubernetes Secrets can be secured using Kubernetes Role-Based Access Control (RBAC) and network policies to restrict unauthorized access.
- **Encryption at Rest**: Secrets are stored encrypted by default in etcd, and additional encryption options are available for enhanced security.
- **Dapr Compatibility**: The Kubernetes Secrets store is fully supported by Dapr, allowing applications to retrieve secrets dynamically at runtime with minimal configuration.

## Consequences

### Positive Aspects

- **Seamless Kubernetes Integration**: Applications running in Kubernetes can easily access secrets with minimal changes.
- **No Additional Infrastructure**: No need to set up or manage an external secret store, reducing operational complexity.
- **RBAC and Fine-Grained Access Control**: Kubernetes RBAC ensures secure access to secrets based on user and service identities.
- **Encryption and Security**: Secrets are encrypted in etcd, and additional encryption policies can be applied.
- **Dapr Support**: Kubernetes Secrets are fully compatible with Dapr, enabling seamless integration with Dapr-managed applications.

### Negative Aspects

- **Limited Secret Rotation Mechanisms**: Kubernetes Secrets do not natively provide automatic secret rotation mechanisms like some cloud-based solutions.
- **etcd Security Concerns**: If not properly secured, etcd can be a potential attack vector for secrets leakage.
- **Potential Namespace Scoping Issues**: Secrets are namespace-scoped, requiring careful planning for cross-namespace access.

## Conclusion

By adopting Kubernetes Secrets as the secret store in Dapr, we ensure a secure, cloud-agnostic, and well-integrated solution for managing sensitive application data. This choice simplifies secret management within Kubernetes environments while leveraging Kubernetes' native security and access control features.
