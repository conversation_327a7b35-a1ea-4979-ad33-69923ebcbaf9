# 5. Web Applications with React

Date: 2025-03-05

## Status

Proposed

## Context

Web applications need a frontend framework that ensures high performance, scalability, and long-term maintainability.
Frameworks/libraries like React, Vue.js and Angular have been evaluated.

## Decision

We will **use [React](https://react.dev/) as a frontend library** for the 91 web applications.

## Consequences

### Benefits
- **Ecosystem & Community Support**: React has the largest community, ensuring robust libraries and tools.
- **Developer Experience**: React’s developer tools streamline debugging and speed up development.
- **Industry Adoption**: Widely used by major companies, ensuring long-term support.
- **AI Code Generation**: React’s popularity makes it the library with which LLMs are mostly trained, thus
  enabling better AI code generation.


### Risks & Mitigation
- Learning Curve: Frameworks like Vue.js provide better performance but the difference is negligible.