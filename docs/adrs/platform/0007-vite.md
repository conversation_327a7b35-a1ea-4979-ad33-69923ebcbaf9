# 7. Vite as the Build Tool for React applications

Date: 2025-03-10

## Status

Proposed

## Context

As we adopt a Microfrontend (MFE) architecture, we need a fast and efficient build tool to support independent 
module development and integration. While Next.js provides a powerful framework, it lacks robust support for 
Micro-frontends and Module Federation. To enable faster development cycles, improve build performance, and ensure a 
smooth integration process for MFEs, we need a tool that is optimized for this architecture.

## Decision

We will **use Vite as the build tool for React applications**.

## Consequences

### Benefits
- **Fast Development Cycles**: Vite’s optimized build times and HMR result in quicker iteration and testing for MFEs.
- **Great MFE Integration**: Native support for Module Federation ensures seamless integration of MFEs.
- **Improved Developer Experienc**e: Vite’s modern tooling provides an enhanced development experience with 
  faster feedback loops.

### Risks & Mitigation
- **Plugin Compatibility**: Vite’s ecosystem is not as mature as Webpack’s.
- **Integration with Nx**: Module Federation with Vite does not have an official plugin to integrate it with Nx.