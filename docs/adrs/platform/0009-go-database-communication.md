# 5. Database Communication in Go

Date: 2024-03-19

## Status

Proposed

## Context

Our Go services need efficient database communication with options for both high performance and developer productivity.

## Decision

We will use:

1. **Migrations**: `golang-migrate/migrate`
   - Version-controlled schema changes
   - Multi-database support

2. **High Performance**: `jackc/pgx`
   - Native PostgreSQL driver
   - Connection pooling
   - Used for:
     - High-performance requirements
     - Complex queries
     - PostgreSQL-specific features

3. **ORM**: `gorm.io/gorm`
   - Developer-friendly API
   - Used for:
     - Rapid development
     - Complex relationships
     - CRUD-heavy applications

## Consequences

### Benefits

- Flexible approach for different needs
- High performance when required
- Developer productivity where needed
- Consistent migration strategy

### Risks & Mitigation

- Library selection: Clear usage guidelines
- Performance: Define requirements upfront
- Migrations: Standardize workflow

## Implementation Notes

1. Use pgx for high-performance needs
2. Use GORM for rapid development
3. Always use migrations for schema changes
4. Document performance requirements
5. Regular performance monitoring
