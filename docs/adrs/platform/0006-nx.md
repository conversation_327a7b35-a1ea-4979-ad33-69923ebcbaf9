# 6. <PERSON><PERSON> as Monorepo Manager and Build Tool

Date: 2025-03-10

## Status

Proposed

## Context

To efficiently manage the monorepo, we need a robust tool that can handle dependency management, optimize builds,
and support CI/CD pipelines.

## Decision

We will **use [Nx](https://nx.dev/) to manage the monorepo** for all projects. Nx will handle the structure, dependency management, 
and build optimizations.


## Consequences

### Benefits
- **Maturity**: Nx is a mature tool with a large community and extensive documentation.
- **Performant builds**: Nx’s caching and incremental build capabilities significantly reduce build times. Benchmarks
  show it is faster then other tools.
- **Multiple programming languages**: Nx supports multiple programming languages and frameworks (including Go which 
- is our predominant backend programming language).

### Risks & Mitigation
- **Learning Curve**: Nx may introduce additional complexity for developers unfamiliar with it.
