# 4. Dapr State Store: PostgreSQL v2

Date: 2025-02-05

## Status
Proposed

## Context

Dapr provides a state management building block that enables microservices to persist and retrieve state efficiently. Various state store options exist, including Redis, etcd, Consul, Apache Cassandra, and PostgreSQL. Our goal is to choose a solution that offers strong consistency, high availability, and ease of integration with existing enterprise databases. Given our requirements for transactional guarantees, SQL-based querying, and scalability, PostgreSQL v2 emerges as a compelling choice.

The **PostgreSQL v2** state store component in Dapr introduces significant performance and reliability improvements over v1. Notably, it stores state values using the `BYTEA` data type, which enhances query speed and, in some cases, improves space efficiency compared to the previously used `JSONB` column. However, due to this change, the v2 component does not support the Dapr state store query APIs. Additionally, ETags are now random UUIDs, ensuring better compatibility with other PostgreSQL-compatible databases, such as CockroachDB. Importantly, v1 and v2 components are not compatible, and data cannot be migrated between the two versions.

## Decision

We will use **PostgreSQL v2** as the state management backend for Dapr. PostgreSQL offers ACID-compliant transactions, ensuring data integrity across state operations. Compared to other options:

- **Redis**: Provides fast in-memory storage but lacks strong consistency guarantees and durability unless configured with persistence mechanisms.
- **etcd**: A strongly consistent key-value store optimized for distributed systems. However, it lacks the advanced querying capabilities, relational integrity, and built-in support for transactions that PostgreSQL v2 provides. Additionally, etcd is primarily designed for configuration management and service discovery rather than high-volume state persistence, which can impact its efficiency in some state management use cases.
- **Consul**: Offers key-value storage with strong consistency guarantees and service discovery capabilities. However, it lacks relational integrity, SQL-based querying, and transactional support, making it less suitable for structured state management compared to PostgreSQL v2.
- **Apache Cassandra**: A highly scalable, distributed NoSQL database that supports multi-region replication. However, it lacks ACID-compliant transactions, making it less suitable for workloads requiring strong consistency. Additionally, while Cassandra is highly available and fault-tolerant, it does not provide native SQL-based querying and requires additional tooling for analytics and complex queries compared to PostgreSQL v2.
- **PostgreSQL v1**: While viable, the v2 component offers performance enhancements, better indexing, and improved `BYTEA` support for flexible data structures. However, it does not support the Dapr state store query APIs, which should be considered if query capabilities are required.

PostgreSQL v2’s combination of relational integrity, scalability improvements, and open-source nature makes it a preferred choice for state management in Dapr.

## Consequences

### Positive Aspects

- ACID transactions provide strong consistency and data integrity.
- SQL-based querying enables complex state retrieval and analytics.
- Open-source and widely adopted, reducing vendor lock-in.
- Already part of the existing technology stack, minimizing additional adoption efforts.
- Enhanced performance and reliability in v2 improve state management efficiency.

### Negative Aspects

- The v2 component does not support Dapr state store query APIs, which may limit querying capabilities.
- Requires careful schema design to optimize performance for high-scale applications.
- May introduce additional operational overhead for backups, scaling, and tuning.

## Conclusion

By adopting PostgreSQL v2, we ensure a robust, scalable, and future-proof solution for state management within Dapr. This choice enhances consistency, reliability, and integration within microservices architectures while aligning with modern database best practices.
