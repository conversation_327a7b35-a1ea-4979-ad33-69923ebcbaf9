# ADR‑0001. Vector Database Adoption: Milvus & ChromaDB

Date: 2025-06-23

## Status

Proposed

---

## Context

Our current vector‑search experiments run in **Google Vertex AI** and are limited to <5 M text embeddings with no production‑grade ingestion, indexing, or query SLOs. Regulatory requirements (HIPAA) and rising egress bills mandate bringing dense‑vector storage **on‑prem**. We need a solution that:

- Scales from tens of millions to billions of vectors with GPU acceleration.
- Serves Retrieval‑Augmented Generation (RAG) pipelines for internal LLMs without exposing PHI to third‑party clouds.
- Offers SDKs in Python, Go, and TypeScript and deploys cleanly to our Kubernetes 1.3x clusters.

**Stakeholders:** application engineers, data engineers, DevOps/platform, security & compliance, and product leadership.

## Decision

Adopt **Milvus** as the production vector database and **ChromaDB** for lightweight prototyping.

1. **Performance & Scalability** – Milvu<PERSON>’s distributed, GPU‑accelerated FAISS/HNSW indexes deliver P95 < 200 ms at billion‑vector scale; ChromaDB handles ≤10 M vectors with minimal overhead.
2. **Developer Velocity** – Both offer straightforward Python/Go/TypeScript SDKs; ChromaDB’s single‑binary runtime accelerates notebook workflows.
3. **Security & Governance** – TLS 1.3, KMS‑backed AES‑256 encryption, and Milvus’s RBAC preview integrate with our OIDC stack, enabling HIPAA compliance.
4. **Operational Alignment** – Kubernetes Operators, Prometheus exporters, and S3/Snapshot backup paths align with existing observability and DR practices.

**Assumptions & Constraints**
- Engineering teams receive training on ANN concepts and Milvus/Chroma APIs.
- Existing GPU nodes (4×A100) cover initial capacity; CPU‑only indexes serve as fallback.
- Rollout proceeds in phases to avoid service disruption.

## Consequences

### Benefits
- **Interactive Retrieval Quality** – GPU‑backed ANN improves LLM context relevance while hitting sub‑second latency.
- **Elastic Scalability** – Horizontal sharding in Milvus; quick local DB reset for experiments.
- **Cost & Ownership** – Eliminates SaaS egress fees and vendor lock‑in; capitalizes on already‑purchased GPUs.
- **Unified Pipeline** – Single ingestion path (Kafka → VectorSink) cuts ETL duplication.

### Risks & Mitigation
- **Learning Curve**  
  *Risk*: Teams unfamiliar with FAISS/HNSW tuning.  
  *Mitigation*: Hands‑on workshops, internal wiki, mentorship.

- **Operator Complexity**  
  *Risk*: Milvus upgrades could cause downtime.  
  *Mitigation*: Blue‑green deployments; rehearsed staging upgrades.

- **Community Support Gaps**  
  *Risk*: Bug fixes may lag OSS releases.  
  *Mitigation*: Sponsor project, maintain internal fork, SWAT rotation.

- **GPU Supply Constraints**  
  *Risk*: Capacity add blocked by hardware lead time.  
  *Mitigation*: Maintain 20 % headroom; enable CPU‑only fallback indexes.

## Follow‑up Actions & Future Considerations
- **Pilot Deployment** – Stand up staging Milvus cluster;
- **Full Rollout** – Migrate first production workload by Q3 2025; extend to image embeddings in H1 2026.
- **Training & Documentation** – Publish code samples; record hands‑on labs; update ADR wiki.
- **Roadmap** – Kafka streaming ingestion, Feast feature‑store connector, and cross‑region replication slated for Q4 2025.

## References & Supporting Documents
- Milvus Operator v2.5 Documentation
- ChromaDB 0.5.x Guide
- HIPAA Security Rule §164.312
- ANN‑Benchmarks (2023) Paper
- Internal Benchmark Report DOC‑2025‑06‑25‑ANN‑perf

