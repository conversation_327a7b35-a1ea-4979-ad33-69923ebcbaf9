# ADR‑0002. AI Workflow Orchestration: Adoption of LangChain / LangGraph

**Date:** 2025-06-23

## Status

Proposed

---

## Context

Our organisation is embarking on its **first** AI/LLM initiative. Currently there are **no production-grade AI workflows**, and knowledge extraction from documents is largely manual. We must:
- **Accelerate time-to-insight** for business stakeholders through rapid document Q&A and tool-integrated agents.
- **Standardise orchestration** across Python, TypeScript, and Go teams while remaining Kubernetes-native.
- **Avoid vendor lock-in** by favouring active, permissive open-source ecosystems.

LangChain (chains / agents) plus LangGraph (graph execution) offers a mature, language-agnostic abstraction with a vibrant community and built-in integrations (vector DBs, external APIs). Adopting this stack will unify AI development, reduce boilerplate, and maintain parity with our DevOps practices.

## Decision

We will adopt **LangChain** and **LangGraph** as the primary framework for building and orchestrating AI/LLM workflows.

1. **Modular, Reusable Components** – Chains, tools, and graph nodes promote clean separation of concerns and composability.
2. **Rapid Prototyping & Time-to-Market** – Declarative APIs, rich prompt tooling, and extensive examples shorten concept-to-prototype to **≤ 2 days**.
3. **Kubernetes & Observability Alignment** – Stateless execution, container-friendly; native OpenTelemetry hooks plug into Grafana/Loki stack.
4. **Open-Source & Multi-Language** – Apache-2 licence; first-class Python SDK with TypeScript parity for client apps.
5. **Scalability** – Graph-based execution supports horizontal scaling; integration with vector DBs and streaming systems.

**Assumptions & Constraints**
- Teams will complete structured training to mitigate the learning curve.
- Existing Kubernetes cluster can host containerised chain services and GPU node groups.

## Consequences

### Benefits
- **Faster Insight Delivery** – Interactive Q&A over large document sets with low latency.
- **Developer Productivity** – Reusable patterns cut boilerplate and foster shared best practices.
- **Operational Consistency** – Observability, RBAC, and deployment align with existing GitOps workflows.
- **Community Momentum** – Frequent updates, plug-ins, and knowledge sharing from an active OSS ecosystem.

### Risks & Mitigation
| Risk | Impact | Mitigation |
|------|--------|-----------|
| **Steep Learning Curve** | Medium | Internal workshops, pair programming, curated code templates |
| **Rapid Upstream Changes** | Medium | Version pinning, nightly integration tests, selective LTS adoption |
| **Python-centric Graph API** | Low | Keep orchestration in Python; expose REST/GRPC endpoints to TS/Go services |
| **Operational Overhead for GPU Nodes** | Low | Autoscaling policies; cost dashboards; fallback to hosted endpoints where viable |

## Follow-up Actions & Future Considerations

1. **Pilot Deployment** – Build a document QA chain and a Jira tool-integration agent in a non-prod namespace; validate latency, cost, and developer UX.
2. **Gradual Rollout** – Migrate production document ingestion and querying use-cases; expand to additional chains based on demand.
3. **Training & Documentation** – Publish internal LangChain style guide; run a two-day bootcamp; record demo sessions.
4. **Future Roadmap** – Explore multimodal (audio/image) support, implement streaming outputs via Kafka, and evaluate TypeScript LangGraph parity as it matures.

## References & Supporting Documents
- ADR-001 (original extended version with full analysis)
- LangChain OSS Repository: https://github.com/langchain-ai/langchain
- LangGraph OSS Repository: https://github.com/langchain-ai/langgraph
- Agentic AI - Initial Documentation : https://docs.google.com/document/d/1juYiuphAb6MxW-gJyD5AhU6pWmdoPgdlOJocap7yrS8/edit?tab=t.0#heading=h.la0xm2ipemzs
- Enterprise Agentic Platform Architecture Proposal : https://docs.google.com/document/d/1PJj0KX-oWSEVPS97qdTOJLmv0gutGUDUFTkND30ftQQ/edit?tab=t.0
