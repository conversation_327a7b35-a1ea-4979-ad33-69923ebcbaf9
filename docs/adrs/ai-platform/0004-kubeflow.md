# ADR-0004 - MLOps Platform: Adoption of Kubeflow

Date: 2025-06-23

## Status

Proposed

## Context

Our machine‑learning workflows are currently fragmented and manual:

- **Data Processing:** Local pandas scripts and notebooks run ad‑hoc on developers’ laptops, previously done on Vertex AI notebooks
- **Experiment Tracking:** None
- **Training & Packaging:** Models trained locally, saved as `.pkl` artefacts, and manually containerised.
- **Deployment:** Hand‑crafted Kubernetes manifests applied with `kubectl`; rollback is manual; only inference workloads autoscale.

As model count, data volume, and compliance requirements grow, this approach is no longer scalable. We need:

- End‑to‑end, reproducible pipelines.
- Integrated experiment tracking and metadata lineage.
- A model registry that supports versioning and promotion.
- Distributed training, hyper‑parameter tuning, and autoscaling.

Kubeflow is a Kubernetes‑native MLOps stack that addresses these gaps while aligning with our existing GKE and ArgoCD investment.

## Decision

We will adopt **Kubeflow** as our unified MLOps platform. This decision provides:

1. **Automated Pipeline Orchestration:** Kubeflow Pipelines (KFP) offers DAG‑based workflows with native Kubernetes CRDs.
2. **Experiment Tracking & Metadata:** ML Metadata (MLMD) records lineage, artefacts, and metrics by default.
3. **Integrated Model Registry & Serving:** KServe enables canary releases, scale‑to‑zero, and GPU inference out of the box.
4. **Hyperparameter Tuning & Distributed Training:** Katib plus TFJob/PyTorchJob operators leverage cluster GPUs and autoscaling.
5. **Operational Alignment:** Runs entirely within our HIPAA‑compliant GKE cluster and meshes with ArgoCD for GitOps delivery.

**Assumptions & Constraints:**

- Teams will receive structured training to manage the learning curve.
- Current GKE cluster will be extended with GPU node‑pools and namespace quotas.

## Consequences

### Benefits

- **Holistic MLOps:** One platform for data prep, training, tuning, registry, and serving—reducing glue code.
- **Improved Reproducibility:** 95 %+ of experiments tracked; lineage and metrics stored centrally.
- **Faster Delivery:** Targeting <1 day from commit to production.
- **Scalability:** Horizontal autoscaling for CPU/GPU workloads and scale‑to‑zero for idle services.
- **Ecosystem Fit:** Leverages existing Kubernetes skills and tooling (Grafana, Datadog, OpenSearch).

### Risks & Mitigation

- **Learning Curve:**\
  *Risk:* R&D team unfamiliar with Kubeflow concepts.\
  *Mitigation:* Offer hands‑on workshops, provide pipeline templates, and schedule office hours.

- **Operational Overhead:**\
  *Risk:* Additional maintenance for multi‑component platform.\
  *Mitigation:* Use ArgoCD “app‑of‑apps” pattern, automate upgrades in blue‑green fashion, and create runbooks.

- **Resource Contention:**\
  *Risk:* Training jobs starve inference services.\
  *Mitigation:* Separate CPU/GPU node‑pools, apply Kubernetes ResourceQuotas and priority classes.

## Follow‑up Actions & Future Considerations

- **Pilot Deployment:** Install Kubeflow in a dedicated `kf-platform` namespace
- **Full Rollout:** Gradually move all model lifecycles to Kubeflow pipelines; decommission manual workflows.
- **Training & Documentation:** Publish quick‑start guides, record demo sessions, and embed Kubeflow best practices into onboarding.
- **Feature Roadmap:** Integrate Feast Feature Store (Q1 2026), OpenMetadata lineage sync, and MinIO S3 data lake connector.

## References & Supporting Documents

TDB