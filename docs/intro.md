---
sidebar_position: 0
---

# Introduction

## Documentation Structure

This is the documentation for the 91 architecture. It is divided into several sections:
- **[Architecture](/docs/category/architecture)**: This section contains the finalized architecture documents of the 
91 project.
- **[Decision Records](/docs/category/decision-records)**: This section contains the Architecture Decision Records (ADRs) of the 91 project. 
It is divided into:
  - **[Global](/docs/category/global)**: This section contains the global decisions that affect all teams.
  - **[Platform](/docs/category/platform/)**: This section contains the decisions related to the platform engineering team.
  - **[Healthcare Platform](/docs/category/healthcare-platform/)**: This section contains the decisions related to the Healthcare
  - **[Heart+](/docs/category/heartplus/)**: This section contains the decisions related to the Heart+ application.
- **[Research](/docs/category/research)**: This section contains the research documents that were conducted for the ADRs.

## Adr Tools

For Architecture Decision Records, we use [adr tools](https://github.com/npryce/adr-tools) to easily add new ADRS to 
our documentation.

If you want to add an ADR to the documentation, you should use the following steps:

1. Go to the directory inside `adrs` where you want to add the ADR. For example, to add an ADR to the global decisions:

```sh
cd documentation/docs/adrs/global
```

2. Use the following command to create a new ADR:

```sh
adr new "My ADR Title"
```

To create a new ADR that supercedes a previous one (ADR 9, for example), use the -s option.
```sh
adr new -s 9 "My ADR Title"
```

## PlantUML
All diagrams (with exceptions, of course) should be done using PlantUML.

Example:

```plantuml
@startuml
!pragma layout smetana

package "Heart+ Application" {
    [Device Acquisition]
    [PDF Processing]
    [Billing]
    [Device Connectivity]
    [Reporting & Dashboarding]
}

package "Healthcare Platform" {
    [FHIR Infrastructure]
    [HL7 Parser]
    [Epic Integration]
}

package "AI Platform" {
    [Data Catalog]
    [Data Transformation\n\n]
    [Model Training]
    [Labeling Services]
}

package "Internal Apps Platform" {
    [KPI Dashboards]
    [Project Score Card]
    [Sherloq]
}
package "Technical Platform" {
  [Event Sourcing\n\nDapr or Temporal]
  [Data Lineage\n\nOpen Lineage/Marquez]
  [Core Services]
  [PubSub\n\nKafka]
  [Secrets\n\n Hashicorp Vault] 
  [State Management\n\nRedis]
  [Configuration]
  [Functions\n\nKNative/Cloud Run/Lambda]
  [Dev Languages\n\nGo/Java/Python/TypeScript]
  [Database Services\n\nPostgres/MongoDB]
  [Dashboarding\n\nStreamlit/Grafana]
  [ML Runtime]
  [Blockchain Services]
}

[Healthcare Platform] -down-> [Technical Platform]
[Internal Apps Platform] -down-> [Technical Platform]
[AI Platform] -down-> [Technical Platform]
[Heart+ Application] -down-> [Healthcare Platform]

@enduml
```