---
sidebar_position: 2
---

# Model Details

Retrieve detailed information about a specific AI model.

## Endpoint

```
GET /v1/models/{model_id}
```

## Path Parameters

| Parameter | Type   | Required | Description        |
|-----------|--------|----------|--------------------|
| model_id  | string | Yes      | ID of the model    |

## Example Request

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     "https://api.ai-platform.example.com/v1/models/model_123"
```

## Example Response

```json
{
  "data": {
    "model": {
      "id": "model_123",
      "name": "GPT-4",
      "type": "language",
      "status": "active",
      "version": "1.0.0",
      "description": "Advanced language model for text generation",
      "capabilities": [
        "text-generation",
        "text-completion",
        "code-generation"
      ],
      "parameters": {
        "max_tokens": 4096,
        "temperature": 0.7,
        "top_p": 1.0
      },
      "created_at": "2024-03-14T12:00:00Z",
      "updated_at": "2024-03-14T12:00:00Z",
      "metrics": {
        "accuracy": 0.95,
        "latency": 150,
        "throughput": 1000
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Error Codes

| Code              | Description                    |
|-------------------|--------------------------------|
| not_found         | Model not found               |
| unauthorized      | Invalid or missing API key     |
| rate_limit_exceeded| Too many requests            |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Get model details
model = client.models.get("model_123")
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Get model details
const model = await client.models.get('model_123');
```

## Model Types

| Type      | Description                    |
|-----------|--------------------------------|
| language  | Natural language processing    |
| vision    | Computer vision               |
| audio     | Audio processing              |
| custom    | Custom model type             |

## Model Status

| Status    | Description                    |
|-----------|--------------------------------|
| active    | Model is ready for use         |
| training  | Model is currently training    |
| failed    | Model training failed          |
| archived  | Model is archived              | 