---
sidebar_position: 1
---

# List Models

Retrieve a list of all available AI models in your account.

## Endpoint

```
GET /v1/models
```

## Query Parameters

| Parameter | Type    | Required | Description                    |
|-----------|---------|----------|--------------------------------|
| page      | integer | No       | Page number (default: 1)       |
| limit     | integer | No       | Items per page (default: 10)   |
| type      | string  | No       | Filter by model type           |
| status    | string  | No       | Filter by model status         |

## Example Request

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     "https://api.ai-platform.example.com/v1/models?page=1&limit=10"
```

## Example Response

```json
{
  "data": {
    "models": [
      {
        "id": "model_123",
        "name": "GPT-4",
        "type": "language",
        "status": "active",
        "created_at": "2024-03-14T12:00:00Z",
        "updated_at": "2024-03-14T12:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "pages": 10
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Error Codes

| Code              | Description                    |
|-------------------|--------------------------------|
| invalid_request   | Invalid query parameters       |
| unauthorized      | Invalid or missing API key     |
| rate_limit_exceeded| Too many requests            |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# List all models
models = client.models.list(
    page=1,
    limit=10,
    type="language",
    status="active"
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// List all models
const models = await client.models.list({
  page: 1,
  limit: 10,
  type: 'language',
  status: 'active'
});
``` 