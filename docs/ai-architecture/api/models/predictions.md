---
sidebar_position: 3
---

# Model Predictions

Make predictions using your AI models.

## Endpoint

```
POST /v1/models/{model_id}/predict
```

## Path Parameters

| Parameter | Type   | Required | Description        |
|-----------|--------|----------|--------------------|
| model_id  | string | Yes      | ID of the model    |

## Request Body

```json
{
  "input": {
    "text": "Your input text here",
    "parameters": {
      "temperature": 0.7,
      "max_tokens": 100,
      "top_p": 1.0
    }
  }
}
```

## Example Request

```bash
curl -X POST \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "input": {
         "text": "Hello, how are you?",
         "parameters": {
           "temperature": 0.7,
           "max_tokens": 100
         }
       }
     }' \
     "https://api.ai-platform.example.com/v1/models/model_123/predict"
```

## Example Response

```json
{
  "data": {
    "prediction": {
      "id": "pred_123",
      "model_id": "model_123",
      "input": {
        "text": "Hello, how are you?",
        "parameters": {
          "temperature": 0.7,
          "max_tokens": 100
        }
      },
      "output": {
        "text": "I'm doing well, thank you for asking! How can I help you today?",
        "confidence": 0.95
      },
      "created_at": "2024-03-14T12:00:00Z",
      "processing_time": 150
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Error Codes

| Code              | Description                    |
|-------------------|--------------------------------|
| invalid_request   | Invalid input parameters       |
| model_not_found   | Model not found               |
| model_not_ready   | Model is not ready for use     |
| rate_limit_exceeded| Too many requests            |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Make a prediction
prediction = client.models.predict(
    model_id="model_123",
    input={
        "text": "Hello, how are you?",
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 100
        }
    }
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Make a prediction
const prediction = await client.models.predict('model_123', {
  input: {
    text: 'Hello, how are you?',
    parameters: {
      temperature: 0.7,
      max_tokens: 100
    }
  }
});
```

## Best Practices

1. Always validate input data before sending
2. Use appropriate model parameters
3. Handle rate limits and errors gracefully
4. Cache predictions when possible
5. Monitor model performance

## Rate Limits

| Plan      | Requests per minute |
|-----------|-------------------|
| Standard  | 100              |
| Pro       | 500              |
| Enterprise| 1000             | 