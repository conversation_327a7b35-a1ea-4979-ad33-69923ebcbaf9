---
sidebar_position: 3
---

# API Tokens

Manage API tokens for authentication and access control.

## Create Token

### Endpoint

```
POST /v1/auth/tokens
```

### Request Body

```json
{
  "name": "production_token",
  "description": "Token for production environment",
  "expires_at": "2025-03-14T12:00:00Z",
  "permissions": [
    "read:models",
    "write:models",
    "read:data"
  ],
  "metadata": {
    "environment": "production",
    "purpose": "model_deployment"
  }
}
```

### Example Response

```json
{
  "data": {
    "token": {
      "id": "token_123",
      "name": "production_token",
      "key": "sk_live_123456789",
      "created_at": "2024-03-14T12:00:00Z",
      "expires_at": "2025-03-14T12:00:00Z",
      "permissions": [
        "read:models",
        "write:models",
        "read:data"
      ]
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## List Tokens

### Endpoint

```
GET /v1/auth/tokens
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "tokens": [
      {
        "id": "token_123",
        "name": "production_token",
        "created_at": "2024-03-14T12:00:00Z",
        "expires_at": "2025-03-14T12:00:00Z",
        "status": "active",
        "last_used": "2024-03-14T12:30:00Z"
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:30:00Z",
    "request_id": "req_123456"
  }
}
```

## Revoke Token

### Endpoint

```
DELETE /v1/auth/tokens/{token_id}
```

### Example Response

```json
{
  "data": {
    "token": {
      "id": "token_123",
      "status": "revoked",
      "revoked_at": "2024-03-14T12:30:00Z"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:30:00Z",
    "request_id": "req_123456"
  }
}
```

## Token Status

| Status     | Description                    |
|------------|--------------------------------|
| active     | Token is active                |
| expired    | Token has expired              |
| revoked    | Token has been revoked         |
| suspended  | Token is temporarily suspended |

## Token Types

| Type       | Description                    | Use Case                      |
|------------|--------------------------------|-------------------------------|
| API Key    | Long-lived API key             | Server-to-server integration  |
| JWT        | Short-lived JWT token          | User authentication           |
| OAuth      | OAuth 2.0 token                | Third-party integration       |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create token
token = client.auth.create_token(
    name="production_token",
    description="Token for production environment",
    expires_at="2025-03-14T12:00:00Z",
    permissions=[
        "read:models",
        "write:models",
        "read:data"
    ]
)

# List tokens
tokens = client.auth.list_tokens(
    page=1,
    limit=10
)

# Revoke token
client.auth.revoke_token("token_123")
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create token
const token = await client.auth.createToken({
  name: 'production_token',
  description: 'Token for production environment',
  expiresAt: '2025-03-14T12:00:00Z',
  permissions: [
    'read:models',
    'write:models',
    'read:data'
  ]
});

// List tokens
const tokens = await client.auth.listTokens({
  page: 1,
  limit: 10
});

// Revoke token
await client.auth.revokeToken('token_123');
```

## Token Best Practices

1. Use descriptive token names
2. Set appropriate expiration
3. Limit token permissions
4. Rotate tokens regularly
5. Monitor token usage
6. Revoke unused tokens
7. Secure token storage
8. Implement rate limiting 