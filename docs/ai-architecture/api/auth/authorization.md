---
sidebar_position: 2
---

# Authorization

Manage user roles, permissions, and access control.

## User Roles

### Endpoint

```
GET /v1/auth/roles
```

### Example Response

```json
{
  "data": {
    "roles": [
      {
        "id": "role_123",
        "name": "admin",
        "description": "Administrator with full access",
        "permissions": [
          "read:*",
          "write:*",
          "delete:*",
          "manage:*"
        ]
      },
      {
        "id": "role_124",
        "name": "data_scientist",
        "description": "Data scientist with model and data access",
        "permissions": [
          "read:models",
          "write:models",
          "read:data",
          "write:data"
        ]
      }
    ]
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Assign Role

### Endpoint

```
POST /v1/auth/users/{user_id}/roles
```

### Request Body

```json
{
  "role_id": "role_123",
  "scope": {
    "type": "dataset",
    "id": "dataset_123"
  }
}
```

### Example Response

```json
{
  "data": {
    "assignment": {
      "id": "assignment_123",
      "user_id": "user_123",
      "role_id": "role_123",
      "scope": {
        "type": "dataset",
        "id": "dataset_123"
      },
      "created_at": "2024-03-14T12:00:00Z"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Check Permissions

### Endpoint

```
GET /v1/auth/permissions/check
```

### Query Parameters

| Parameter  | Type   | Required | Description                    |
|------------|--------|----------|--------------------------------|
| resource   | string | Yes      | Resource type (e.g., dataset)  |
| action     | string | Yes      | Action (e.g., read, write)     |
| resource_id| string | No       | Specific resource ID           |

### Example Response

```json
{
  "data": {
    "permission": {
      "granted": true,
      "reason": "User has admin role",
      "role": "admin",
      "scope": "global"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Role Types

| Role           | Description                    | Permissions                    |
|----------------|--------------------------------|--------------------------------|
| admin          | Full system access             | All permissions                |
| data_scientist | Model and data access          | Read/write models and data     |
| analyst        | Data analysis access           | Read data, run analysis        |
| viewer         | Read-only access               | Read permissions only          |

## Permission Types

| Permission     | Description                    |
|----------------|--------------------------------|
| read:*         | Read access to all resources   |
| write:*        | Write access to all resources  |
| delete:*       | Delete access to all resources |
| manage:*       | Management access to all       |
| read:models    | Read access to models          |
| write:models   | Write access to models         |
| read:data      | Read access to data            |
| write:data     | Write access to data           |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Get roles
roles = client.auth.get_roles()

# Assign role
assignment = client.auth.assign_role(
    "user_123",
    role_id="role_123",
    scope={
        "type": "dataset",
        "id": "dataset_123"
    }
)

# Check permissions
permission = client.auth.check_permission(
    resource="dataset",
    action="read",
    resource_id="dataset_123"
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Get roles
const roles = await client.auth.getRoles();

// Assign role
const assignment = await client.auth.assignRole('user_123', {
  roleId: 'role_123',
  scope: {
    type: 'dataset',
    id: 'dataset_123'
  }
});

// Check permissions
const permission = await client.auth.checkPermission({
  resource: 'dataset',
  action: 'read',
  resourceId: 'dataset_123'
});
```

## Authorization Best Practices

1. Follow principle of least privilege
2. Use role-based access control
3. Implement resource scoping
4. Regular permission audits
5. Document access policies
6. Monitor access patterns
7. Implement audit logging
8. Regular security reviews 