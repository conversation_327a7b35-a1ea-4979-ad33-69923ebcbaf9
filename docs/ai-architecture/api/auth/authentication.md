---
sidebar_position: 1
---

# Authentication

This guide explains how to authenticate with the AI Platform API.

## API Keys

All API requests require an API key for authentication. You can create and manage your API keys in the dashboard.

### Creating an API Key

1. Log in to your dashboard
2. Navigate to Settings > API Keys
3. Click "Create New API Key"
4. Give your key a descriptive name
5. Select the appropriate permissions
6. Copy the key immediately (it won't be shown again)

## Using API Keys

Include your API key in the `Authorization` header of all requests:

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.ai-platform.example.com/v1/models
```

## Key Security

- Never share your API keys
- Rotate keys regularly
- Use different keys for different environments
- Set appropriate permissions for each key

## Key Permissions

API keys can have the following permissions:

- `read`: Read-only access
- `write`: Read and write access
- `admin`: Full administrative access

## Rate Limits

API keys are subject to rate limits based on your plan:

| Plan      | Requests per minute |
|-----------|-------------------|
| Standard  | 100              |
| Pro       | 500              |
| Enterprise| 1000             |

## Key Expiration

API keys can be set to expire after a certain period. You can configure this in the dashboard.

## Best Practices

1. Use environment variables to store API keys
2. Implement key rotation
3. Monitor key usage
4. Set up alerts for suspicious activity

## Example: Python SDK

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Make authenticated requests
models = client.models.list()
```

## Example: JavaScript SDK

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Make authenticated requests
const models = await client.models.list();
```

## Troubleshooting

Common authentication issues:

1. Invalid API key
2. Expired API key
3. Insufficient permissions
4. Rate limit exceeded

If you encounter any issues, check the [Error Handling](/docs/ai-architecture/api/api-overview#error-handling) section for more information. 