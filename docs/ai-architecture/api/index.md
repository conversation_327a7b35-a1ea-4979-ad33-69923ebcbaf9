---
sidebar_position: 1
---

# API Reference

Welcome to the 91.life AI Platform API documentation. This section provides comprehensive documentation for all available API endpoints, authentication methods, and usage examples.

## Getting Started

To get started with the API:

1. Review the [Authentication](/docs/ai-architecture/api/auth/authentication) guide
2. Check out the [API Overview](/docs/ai-architecture/api/api-overview)
3. Explore the available endpoints

## Available Endpoints

### Authentication
- [Authentication](/docs/ai-architecture/api/auth/authentication)
- [Authorization](/docs/ai-architecture/api/auth/authorization)
- [Tokens](/docs/ai-architecture/api/auth/tokens)

### Data Management
- [Data Upload](/docs/ai-architecture/api/data/upload)
- [Data Processing](/docs/ai-architecture/api/data/processing)
- [Data Validation](/docs/ai-architecture/api/data/validation)
- [Data Versioning](/docs/ai-architecture/api/data/versioning)

### Model Management
- [List Models](/docs/ai-architecture/api/models/list-models)
- [Model Details](/docs/ai-architecture/api/models/model-details)
- [Predictions](/docs/ai-architecture/api/models/predictions)

### Monitoring
- [Metrics](/docs/ai-architecture/api/monitoring/metrics)
- [Logs](/docs/ai-architecture/api/monitoring/logs)
- [Alerts](/docs/ai-architecture/api/monitoring/alerts)
- [Dashboards](/docs/ai-architecture/api/monitoring/dashboards)
- [Reports](/docs/ai-architecture/api/monitoring/reports)

## Support

For API support and questions, please contact us at [<EMAIL>](mailto:<EMAIL>). 