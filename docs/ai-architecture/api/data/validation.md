---
sidebar_position: 4
---

# Data Validation

Validate and ensure the quality of your datasets.

## Run Validation

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/validate
```

### Request Body

```json
{
  "validation_rules": {
    "data_quality": {
      "min_samples": 1000,
      "max_missing_values": 0.1,
      "required_columns": ["text", "label"]
    },
    "format_validation": {
      "text_length": {
        "min": 10,
        "max": 1000
      },
      "label_values": ["positive", "negative", "neutral"]
    }
  },
  "options": {
    "generate_report": true,
    "notify_on_completion": true
  }
}
```

### Example Response

```json
{
  "data": {
    "validation": {
      "id": "validation_123",
      "dataset_id": "dataset_123",
      "status": "processing",
      "created_at": "2024-03-14T12:00:00Z",
      "validation_rules": {
        "data_quality": {
          "min_samples": 1000,
          "max_missing_values": 0.1,
          "required_columns": ["text", "label"]
        }
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Validation Results

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/validations/{validation_id}
```

### Example Response

```json
{
  "data": {
    "validation": {
      "id": "validation_123",
      "dataset_id": "dataset_123",
      "status": "completed",
      "created_at": "2024-03-14T12:00:00Z",
      "completed_at": "2024-03-14T12:01:00Z",
      "results": {
        "overall_score": 0.95,
        "checks": {
          "data_quality": {
            "score": 0.98,
            "details": {
              "sample_count": 1500,
              "missing_values": 0.05,
              "required_columns": true
            }
          },
          "format_validation": {
            "score": 0.92,
            "details": {
              "text_length": {
                "valid": 0.95,
                "invalid": 0.05
              },
              "label_values": {
                "valid": 0.98,
                "invalid": 0.02
              }
            }
          }
        },
        "issues": [
          {
            "type": "format_validation",
            "severity": "warning",
            "message": "5% of texts exceed maximum length",
            "affected_samples": 75
          }
        ]
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:01:00Z",
    "request_id": "req_123456"
  }
}
```

## List Validations

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/validations
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "validations": [
      {
        "id": "validation_123",
        "dataset_id": "dataset_123",
        "status": "completed",
        "created_at": "2024-03-14T12:00:00Z",
        "completed_at": "2024-03-14T12:01:00Z",
        "overall_score": 0.95
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:01:00Z",
    "request_id": "req_123456"
  }
}
```

## Validation Status

| Status     | Description                    |
|------------|--------------------------------|
| processing | Validation is running          |
| completed  | Validation completed           |
| failed     | Validation failed              |
| cancelled  | Validation was cancelled       |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Run validation
validation = client.data.validate_dataset(
    "dataset_123",
    validation_rules={
        "data_quality": {
            "min_samples": 1000,
            "max_missing_values": 0.1
        }
    }
)

# Get validation results
results = client.data.get_validation(
    "dataset_123",
    "validation_123"
)

# List validations
validations = client.data.list_validations(
    "dataset_123",
    page=1,
    limit=10
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Run validation
const validation = await client.data.validateDataset('dataset_123', {
  validationRules: {
    dataQuality: {
      minSamples: 1000,
      maxMissingValues: 0.1
    }
  }
});

// Get validation results
const results = await client.data.getValidation(
  'dataset_123',
  'validation_123'
);

// List validations
const validations = await client.data.listValidations('dataset_123', {
  page: 1,
  limit: 10
});
```

## Validation Best Practices

1. Define clear validation rules
2. Set appropriate thresholds
3. Monitor validation results
4. Address issues promptly
5. Maintain validation history
6. Use automated validation
7. Implement custom rules
8. Regular validation schedule 