---
sidebar_position: 5
---

# Data Preprocessing

Transform and prepare your data for model training and inference.

## Create Preprocessing Pipeline

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/preprocessing
```

### Request Body

```json
{
  "name": "text_preprocessing",
  "description": "Standard text preprocessing pipeline",
  "steps": [
    {
      "name": "text_cleaning",
      "type": "text_clean",
      "params": {
        "remove_urls": true,
        "remove_emails": true,
        "remove_numbers": false,
        "remove_special_chars": true
      }
    },
    {
      "name": "tokenization",
      "type": "tokenize",
      "params": {
        "method": "word",
        "lowercase": true,
        "remove_stopwords": true,
        "language": "en"
      }
    },
    {
      "name": "normalization",
      "type": "normalize",
      "params": {
        "method": "lemmatization",
        "language": "en"
      }
    }
  ],
  "options": {
    "save_intermediate": true,
    "parallel_processing": true
  }
}
```

### Example Response

```json
{
  "data": {
    "pipeline": {
      "id": "pipeline_123",
      "name": "text_preprocessing",
      "dataset_id": "dataset_123",
      "status": "created",
      "created_at": "2024-03-14T12:00:00Z",
      "steps": [
        {
          "name": "text_cleaning",
          "type": "text_clean",
          "params": {
            "remove_urls": true,
            "remove_emails": true
          }
        }
      ]
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Run Preprocessing

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/preprocessing/{pipeline_id}/run
```

### Request Body

```json
{
  "options": {
    "batch_size": 1000,
    "max_workers": 4,
    "save_checkpoints": true
  }
}
```

### Example Response

```json
{
  "data": {
    "preprocessing": {
      "id": "preprocessing_123",
      "pipeline_id": "pipeline_123",
      "dataset_id": "dataset_123",
      "status": "processing",
      "created_at": "2024-03-14T12:00:00Z",
      "options": {
        "batch_size": 1000,
        "max_workers": 4
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Preprocessing Status

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/preprocessing/{preprocessing_id}
```

### Example Response

```json
{
  "data": {
    "preprocessing": {
      "id": "preprocessing_123",
      "pipeline_id": "pipeline_123",
      "dataset_id": "dataset_123",
      "status": "completed",
      "created_at": "2024-03-14T12:00:00Z",
      "completed_at": "2024-03-14T12:05:00Z",
      "progress": {
        "total_samples": 10000,
        "processed_samples": 10000,
        "percentage": 100
      },
      "statistics": {
        "text_cleaning": {
          "urls_removed": 500,
          "emails_removed": 200,
          "special_chars_removed": 1000
        },
        "tokenization": {
          "average_tokens": 15,
          "total_tokens": 150000
        },
        "normalization": {
          "unique_lemmas": 5000
        }
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:05:00Z",
    "request_id": "req_123456"
  }
}
```

## List Preprocessing Pipelines

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/preprocessing
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "pipelines": [
      {
        "id": "pipeline_123",
        "name": "text_preprocessing",
        "dataset_id": "dataset_123",
        "status": "active",
        "created_at": "2024-03-14T12:00:00Z",
        "last_run": "2024-03-14T12:05:00Z"
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:05:00Z",
    "request_id": "req_123456"
  }
}
```

## Preprocessing Status

| Status     | Description                    |
|------------|--------------------------------|
| created    | Pipeline created               |
| processing | Preprocessing running          |
| completed  | Preprocessing completed        |
| failed     | Preprocessing failed           |
| cancelled  | Preprocessing cancelled        |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create preprocessing pipeline
pipeline = client.data.create_preprocessing_pipeline(
    "dataset_123",
    name="text_preprocessing",
    steps=[
        {
            "name": "text_cleaning",
            "type": "text_clean",
            "params": {
                "remove_urls": True,
                "remove_emails": True
            }
        }
    ]
)

# Run preprocessing
preprocessing = client.data.run_preprocessing(
    "dataset_123",
    "pipeline_123",
    options={
        "batch_size": 1000,
        "max_workers": 4
    }
)

# Get preprocessing status
status = client.data.get_preprocessing_status(
    "dataset_123",
    "preprocessing_123"
)

# List pipelines
pipelines = client.data.list_preprocessing_pipelines(
    "dataset_123",
    page=1,
    limit=10
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create preprocessing pipeline
const pipeline = await client.data.createPreprocessingPipeline('dataset_123', {
  name: 'text_preprocessing',
  steps: [
    {
      name: 'text_cleaning',
      type: 'text_clean',
      params: {
        removeUrls: true,
        removeEmails: true
      }
    }
  ]
});

// Run preprocessing
const preprocessing = await client.data.runPreprocessing(
  'dataset_123',
  'pipeline_123',
  {
    options: {
      batchSize: 1000,
      maxWorkers: 4
    }
  }
);

// Get preprocessing status
const status = await client.data.getPreprocessingStatus(
  'dataset_123',
  'preprocessing_123'
);

// List pipelines
const pipelines = await client.data.listPreprocessingPipelines('dataset_123', {
  page: 1,
  limit: 10
});
```

## Preprocessing Best Practices

1. Define clear preprocessing steps
2. Use appropriate parameters
3. Monitor preprocessing progress
4. Save intermediate results
5. Implement error handling
6. Use parallel processing
7. Validate output quality
8. Document preprocessing steps 