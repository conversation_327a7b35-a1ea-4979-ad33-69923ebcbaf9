---
sidebar_position: 10
---

# Data Governance

Manage data access, compliance, and security policies.

## Create Access Policy

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/policies
```

### Request Body

```json
{
  "name": "restricted_access",
  "description": "Restrict access to sensitive data",
  "type": "access",
  "rules": [
    {
      "role": "data_scientist",
      "permissions": ["read", "write"],
      "conditions": {
        "ip_range": ["10.0.0.0/24"],
        "time_window": {
          "start": "09:00",
          "end": "17:00",
          "timezone": "UTC"
        }
      }
    },
    {
      "role": "analyst",
      "permissions": ["read"],
      "conditions": {
        "ip_range": ["10.0.0.0/24"],
        "time_window": {
          "start": "09:00",
          "end": "17:00",
          "timezone": "UTC"
        }
      }
    }
  ],
  "metadata": {
    "compliance": ["GDPR", "HIPAA"],
    "data_classification": "sensitive",
    "retention_period": "365d"
  }
}
```

### Example Response

```json
{
  "data": {
    "policy": {
      "id": "policy_123",
      "dataset_id": "dataset_123",
      "name": "restricted_access",
      "type": "access",
      "status": "active",
      "created_at": "2024-03-14T12:00:00Z",
      "rules": [
        {
          "role": "data_scientist",
          "permissions": ["read", "write"]
        }
      ]
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Policy Details

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/policies/{policy_id}
```

### Example Response

```json
{
  "data": {
    "policy": {
      "id": "policy_123",
      "dataset_id": "dataset_123",
      "name": "restricted_access",
      "type": "access",
      "status": "active",
      "created_at": "2024-03-14T12:00:00Z",
      "updated_at": "2024-03-14T12:00:00Z",
      "rules": [
        {
          "role": "data_scientist",
          "permissions": ["read", "write"],
          "conditions": {
            "ip_range": ["10.0.0.0/24"],
            "time_window": {
              "start": "09:00",
              "end": "17:00",
              "timezone": "UTC"
            }
          }
        }
      ],
      "metadata": {
        "compliance": ["GDPR", "HIPAA"],
        "data_classification": "sensitive",
        "retention_period": "365d"
      },
      "audit_log": [
        {
          "timestamp": "2024-03-14T12:00:00Z",
          "action": "create",
          "user": "<EMAIL>"
        }
      ]
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## List Policies

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/policies
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| type      | string  | Filter by policy type          |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "policies": [
      {
        "id": "policy_123",
        "dataset_id": "dataset_123",
        "name": "restricted_access",
        "type": "access",
        "status": "active",
        "created_at": "2024-03-14T12:00:00Z",
        "metadata": {
          "compliance": ["GDPR", "HIPAA"],
          "data_classification": "sensitive"
        }
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Policy Status

| Status     | Description                    |
|------------|--------------------------------|
| active     | Policy is active               |
| inactive   | Policy is inactive             |
| draft      | Policy is in draft             |
| archived   | Policy is archived             |

## Policy Types

### Access Policies

- Role-based access control
- IP-based restrictions
- Time-based restrictions
- Data masking rules
- Encryption requirements

### Compliance Policies

- Data retention rules
- Privacy requirements
- Regulatory compliance
- Data classification
- Audit requirements

### Security Policies

- Encryption standards
- Authentication requirements
- Network security
- Data protection
- Incident response

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create policy
policy = client.data.create_policy(
    "dataset_123",
    name="restricted_access",
    type="access",
    rules=[
        {
            "role": "data_scientist",
            "permissions": ["read", "write"]
        }
    ]
)

# Get policy details
details = client.data.get_policy(
    "dataset_123",
    "policy_123"
)

# List policies
policies = client.data.list_policies(
    "dataset_123",
    page=1,
    limit=10
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create policy
const policy = await client.data.createPolicy('dataset_123', {
  name: 'restricted_access',
  type: 'access',
  rules: [
    {
      role: 'data_scientist',
      permissions: ['read', 'write']
    }
  ]
});

// Get policy details
const details = await client.data.getPolicy(
  'dataset_123',
  'policy_123'
);

// List policies
const policies = await client.data.listPolicies('dataset_123', {
  page: 1,
  limit: 10
});
```

## Governance Best Practices

1. Define clear policies
2. Implement role-based access
3. Monitor compliance
4. Regular audits
5. Data classification
6. Security measures
7. Documentation
8. Training programs 