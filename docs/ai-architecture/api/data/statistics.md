---
sidebar_position: 8
---

# Data Statistics

Analyze and understand your datasets through comprehensive statistics.

## Generate Statistics

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/statistics
```

### Request Body

```json
{
  "metrics": [
    "basic_stats",
    "distribution",
    "correlation",
    "quality_metrics"
  ],
  "options": {
    "include_visualizations": true,
    "save_results": true,
    "format": "json"
  }
}
```

### Example Response

```json
{
  "data": {
    "statistics": {
      "id": "stats_123",
      "dataset_id": "dataset_123",
      "status": "processing",
      "created_at": "2024-03-14T12:00:00Z",
      "metrics": [
        "basic_stats",
        "distribution",
        "correlation",
        "quality_metrics"
      ]
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Statistics Results

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/statistics/{statistics_id}
```

### Example Response

```json
{
  "data": {
    "statistics": {
      "id": "stats_123",
      "dataset_id": "dataset_123",
      "status": "completed",
      "created_at": "2024-03-14T12:00:00Z",
      "completed_at": "2024-03-14T12:01:00Z",
      "results": {
        "basic_stats": {
          "total_samples": 10000,
          "features": {
            "text": {
              "type": "text",
              "unique_values": 9500,
              "null_count": 0,
              "min_length": 10,
              "max_length": 1000,
              "avg_length": 150
            },
            "label": {
              "type": "categorical",
              "unique_values": 3,
              "null_count": 0,
              "distribution": {
                "positive": 0.6,
                "negative": 0.3,
                "neutral": 0.1
              }
            }
          }
        },
        "distribution": {
          "text_length": {
            "mean": 150,
            "std": 50,
            "percentiles": {
              "25": 100,
              "50": 150,
              "75": 200
            },
            "histogram": {
              "bins": [0, 50, 100, 150, 200, 250],
              "counts": [100, 1000, 4000, 3000, 1500, 400]
            }
          }
        },
        "correlation": {
          "text_length_label": {
            "correlation": 0.2,
            "p_value": 0.001
          }
        },
        "quality_metrics": {
          "completeness": 1.0,
          "consistency": 0.95,
          "accuracy": 0.98,
          "issues": [
            {
              "type": "inconsistent_format",
              "severity": "warning",
              "count": 50,
              "description": "Text contains inconsistent formatting"
            }
          ]
        }
      },
      "visualizations": {
        "text_length_dist": "https://storage.ai-platform.example.com/visualizations/stats_123/text_length_dist.png",
        "label_dist": "https://storage.ai-platform.example.com/visualizations/stats_123/label_dist.png",
        "correlation_matrix": "https://storage.ai-platform.example.com/visualizations/stats_123/correlation_matrix.png"
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:01:00Z",
    "request_id": "req_123456"
  }
}
```

## List Statistics

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/statistics
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "statistics": [
      {
        "id": "stats_123",
        "dataset_id": "dataset_123",
        "status": "completed",
        "created_at": "2024-03-14T12:00:00Z",
        "completed_at": "2024-03-14T12:01:00Z",
        "metrics": [
          "basic_stats",
          "distribution",
          "correlation",
          "quality_metrics"
        ]
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:01:00Z",
    "request_id": "req_123456"
  }
}
```

## Statistics Status

| Status     | Description                    |
|------------|--------------------------------|
| processing | Statistics generation running  |
| completed  | Statistics generation completed|
| failed     | Statistics generation failed   |
| cancelled  | Statistics generation cancelled|

## Available Metrics

### Basic Statistics

- Total samples
- Feature types
- Unique values
- Null counts
- Min/max/mean values
- Value distributions

### Distribution Analysis

- Histograms
- Percentiles
- Skewness
- Kurtosis
- Outlier detection

### Correlation Analysis

- Feature correlations
- P-values
- Correlation matrices
- Feature importance

### Quality Metrics

- Completeness
- Consistency
- Accuracy
- Data quality issues
- Anomaly detection

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Generate statistics
stats = client.data.generate_statistics(
    "dataset_123",
    metrics=[
        "basic_stats",
        "distribution",
        "correlation",
        "quality_metrics"
    ]
)

# Get statistics results
results = client.data.get_statistics(
    "dataset_123",
    "stats_123"
)

# List statistics
stats_list = client.data.list_statistics(
    "dataset_123",
    page=1,
    limit=10
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Generate statistics
const stats = await client.data.generateStatistics('dataset_123', {
  metrics: [
    'basic_stats',
    'distribution',
    'correlation',
    'quality_metrics'
  ]
});

// Get statistics results
const results = await client.data.getStatistics(
  'dataset_123',
  'stats_123'
);

// List statistics
const statsList = await client.data.listStatistics('dataset_123', {
  page: 1,
  limit: 10
});
```

## Statistics Best Practices

1. Choose relevant metrics
2. Monitor data quality
3. Track distributions
4. Analyze correlations
5. Detect anomalies
6. Generate visualizations
7. Document findings
8. Regular analysis 