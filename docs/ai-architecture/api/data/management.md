---
sidebar_position: 2
---

# Data Management

Manage and organize your datasets effectively.

## List Datasets

### Endpoint

```
GET /v1/data/datasets
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| type      | string  | Filter by data type            |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "datasets": [
      {
        "id": "dataset_123",
        "name": "training_dataset",
        "type": "text",
        "format": "json",
        "size": "1.2GB",
        "status": "ready",
        "created_at": "2024-03-14T12:00:00Z",
        "updated_at": "2024-03-14T12:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "pages": 10
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Dataset Details

### Endpoint

```
GET /v1/data/datasets/{dataset_id}
```

### Example Response

```json
{
  "data": {
    "dataset": {
      "id": "dataset_123",
      "name": "training_dataset",
      "type": "text",
      "format": "json",
      "size": "1.2GB",
      "status": "ready",
      "created_at": "2024-03-14T12:00:00Z",
      "updated_at": "2024-03-14T12:00:00Z",
      "metadata": {
        "source": "customer_feedback",
        "language": "en"
      },
      "statistics": {
        "total_samples": 10000,
        "unique_labels": 5,
        "average_length": 150
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Update Dataset

### Endpoint

```
PATCH /v1/data/datasets/{dataset_id}
```

### Request Body

```json
{
  "name": "updated_dataset_name",
  "description": "Updated description",
  "metadata": {
    "source": "new_source",
    "language": "fr"
  }
}
```

## Delete Dataset

### Endpoint

```
DELETE /v1/data/datasets/{dataset_id}
```

## Dataset Status

| Status    | Description                    |
|-----------|--------------------------------|
| processing| Dataset is being processed     |
| ready     | Dataset is ready for use       |
| failed    | Processing failed              |
| archived  | Dataset is archived            |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# List datasets
datasets = client.data.list_datasets(
    page=1,
    limit=10,
    type="text"
)

# Get dataset details
dataset = client.data.get_dataset("dataset_123")

# Update dataset
updated = client.data.update_dataset(
    "dataset_123",
    name="updated_name",
    description="New description"
)

# Delete dataset
client.data.delete_dataset("dataset_123")
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// List datasets
const datasets = await client.data.listDatasets({
  page: 1,
  limit: 10,
  type: 'text'
});

// Get dataset details
const dataset = await client.data.getDataset('dataset_123');

// Update dataset
const updated = await client.data.updateDataset('dataset_123', {
  name: 'updated_name',
  description: 'New description'
});

// Delete dataset
await client.data.deleteDataset('dataset_123');
```

## Best Practices

1. Use meaningful dataset names
2. Keep metadata up to date
3. Monitor dataset status
4. Archive unused datasets
5. Regular data validation
6. Implement version control
7. Document data lineage
8. Set up access controls 