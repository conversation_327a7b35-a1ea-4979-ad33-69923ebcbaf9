---
sidebar_position: 7
---

# Data Export

Export your datasets in various formats for different use cases.

## Create Export Job

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/export
```

### Request Body

```json
{
  "format": "json",
  "options": {
    "include_metadata": true,
    "include_statistics": true,
    "compression": "gzip",
    "split": {
      "type": "train_test",
      "train_ratio": 0.8,
      "validation_ratio": 0.1,
      "test_ratio": 0.1,
      "stratify_by": "label"
    }
  },
  "filters": {
    "date_range": {
      "start": "2024-01-01",
      "end": "2024-03-14"
    },
    "labels": ["positive", "negative"],
    "min_confidence": 0.8
  }
}
```

### Example Response

```json
{
  "data": {
    "export": {
      "id": "export_123",
      "dataset_id": "dataset_123",
      "format": "json",
      "status": "processing",
      "created_at": "2024-03-14T12:00:00Z",
      "options": {
        "include_metadata": true,
        "compression": "gzip"
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Export Status

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/exports/{export_id}
```

### Example Response

```json
{
  "data": {
    "export": {
      "id": "export_123",
      "dataset_id": "dataset_123",
      "format": "json",
      "status": "completed",
      "created_at": "2024-03-14T12:00:00Z",
      "completed_at": "2024-03-14T12:01:00Z",
      "files": [
        {
          "name": "train.json.gz",
          "size": "100MB",
          "url": "https://storage.ai-platform.example.com/exports/export_123/train.json.gz",
          "expires_at": "2024-03-21T12:00:00Z"
        },
        {
          "name": "validation.json.gz",
          "size": "10MB",
          "url": "https://storage.ai-platform.example.com/exports/export_123/validation.json.gz",
          "expires_at": "2024-03-21T12:00:00Z"
        },
        {
          "name": "test.json.gz",
          "size": "10MB",
          "url": "https://storage.ai-platform.example.com/exports/export_123/test.json.gz",
          "expires_at": "2024-03-21T12:00:00Z"
        }
      ],
      "statistics": {
        "total_samples": 10000,
        "train_samples": 8000,
        "validation_samples": 1000,
        "test_samples": 1000,
        "exported_size": "120MB"
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:01:00Z",
    "request_id": "req_123456"
  }
}
```

## List Exports

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/exports
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| status    | string  | Filter by status               |
| format    | string  | Filter by format               |

### Example Response

```json
{
  "data": {
    "exports": [
      {
        "id": "export_123",
        "dataset_id": "dataset_123",
        "format": "json",
        "status": "completed",
        "created_at": "2024-03-14T12:00:00Z",
        "completed_at": "2024-03-14T12:01:00Z",
        "total_size": "120MB"
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:01:00Z",
    "request_id": "req_123456"
  }
}
```

## Export Status

| Status     | Description                    |
|------------|--------------------------------|
| processing | Export is running              |
| completed  | Export completed               |
| failed     | Export failed                  |
| cancelled  | Export cancelled               |

## Supported Formats

| Format     | Description                    | Compression                    |
|------------|--------------------------------|--------------------------------|
| json       | JSON format                    | gzip, zip                     |
| csv        | CSV format                     | gzip, zip                     |
| parquet    | Parquet format                 | snappy, gzip                  |
| tfrecord   | TensorFlow record format       | gzip                          |
| hdf5       | HDF5 format                    | gzip                          |

## Split Types

| Type           | Description                    |
|----------------|--------------------------------|
| train_test     | Train/test split              |
| train_val_test | Train/validation/test split   |
| k_fold         | K-fold cross-validation       |
| custom         | Custom split ratios           |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create export
export = client.data.create_export(
    "dataset_123",
    format="json",
    options={
        "include_metadata": True,
        "compression": "gzip",
        "split": {
            "type": "train_test",
            "train_ratio": 0.8
        }
    }
)

# Get export status
status = client.data.get_export_status(
    "dataset_123",
    "export_123"
)

# List exports
exports = client.data.list_exports(
    "dataset_123",
    page=1,
    limit=10
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create export
const export = await client.data.createExport('dataset_123', {
  format: 'json',
  options: {
    includeMetadata: true,
    compression: 'gzip',
    split: {
      type: 'train_test',
      trainRatio: 0.8
    }
  }
});

// Get export status
const status = await client.data.getExportStatus(
  'dataset_123',
  'export_123'
);

// List exports
const exports = await client.data.listExports('dataset_123', {
  page: 1,
  limit: 10
});
```

## Export Best Practices

1. Choose appropriate format
2. Use compression when needed
3. Include metadata
4. Implement data splitting
5. Set expiration dates
6. Monitor export progress
7. Handle large datasets
8. Validate exported data 