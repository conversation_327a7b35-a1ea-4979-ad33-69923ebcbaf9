---
sidebar_position: 2
---

# Data Processing

Process and transform your datasets using configurable pipelines.

## Create Processing Pipeline

### Endpoint

```
POST /v1/data/pipelines
```

### Request Body

```json
{
  "name": "text_preprocessing",
  "description": "Text preprocessing pipeline",
  "steps": [
    {
      "name": "clean_text",
      "type": "text",
      "config": {
        "remove_special_chars": true,
        "lowercase": true,
        "remove_numbers": false
      }
    },
    {
      "name": "tokenize",
      "type": "text",
      "config": {
        "method": "word",
        "max_tokens": 100
      }
    },
    {
      "name": "remove_stopwords",
      "type": "text",
      "config": {
        "language": "en"
      }
    }
  ],
  "input_schema": {
    "type": "object",
    "properties": {
      "text": {
        "type": "string"
      }
    }
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "tokens": {
        "type": "array",
        "items": {
          "type": "string"
        }
      }
    }
  }
}
```

### Example Response

```json
{
  "data": {
    "pipeline": {
      "id": "pipeline_123",
      "name": "text_preprocessing",
      "description": "Text preprocessing pipeline",
      "steps": [
        {
          "id": "step_123",
          "name": "clean_text",
          "type": "text",
          "config": {
            "remove_special_chars": true,
            "lowercase": true,
            "remove_numbers": false
          }
        }
      ],
      "status": "active",
      "created_at": "2024-03-14T12:00:00Z",
      "updated_at": "2024-03-14T12:00:00Z"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Run Pipeline

### Endpoint

```
POST /v1/data/pipelines/{pipeline_id}/run
```

### Request Body

```json
{
  "input": {
    "text": "Hello, World! This is a test."
  },
  "options": {
    "batch_size": 100,
    "timeout": 300
  }
}
```

### Example Response

```json
{
  "data": {
    "job": {
      "id": "job_123",
      "pipeline_id": "pipeline_123",
      "status": "processing",
      "created_at": "2024-03-14T12:00:00Z",
      "input": {
        "text": "Hello, World! This is a test."
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Pipeline Results

### Endpoint

```
GET /v1/data/pipelines/{pipeline_id}/jobs/{job_id}
```

### Example Response

```json
{
  "data": {
    "job": {
      "id": "job_123",
      "pipeline_id": "pipeline_123",
      "status": "completed",
      "created_at": "2024-03-14T12:00:00Z",
      "completed_at": "2024-03-14T12:00:05Z",
      "input": {
        "text": "Hello, World! This is a test."
      },
      "output": {
        "tokens": ["hello", "world", "test"]
      },
      "metrics": {
        "processing_time": 5.0,
        "memory_usage": 100.5
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:05Z",
    "request_id": "req_123456"
  }
}
```

## Processing Steps

| Type      | Description                    | Use Case                      |
|-----------|--------------------------------|-------------------------------|
| text      | Text processing                | NLP, text cleaning            |
| image     | Image processing               | Computer vision               |
| audio     | Audio processing               | Speech recognition            |
| video     | Video processing               | Video analysis                |
| tabular   | Tabular data processing        | Data transformation           |
| custom    | Custom processing              | Specialized tasks             |

## Processing Status

| Status     | Description                    |
|------------|--------------------------------|
| created    | Pipeline created               |
| active     | Pipeline is active             |
| inactive   | Pipeline is inactive           |
| error      | Pipeline has errors            |

## Job Status

| Status     | Description                    |
|------------|--------------------------------|
| queued     | Job is queued                  |
| processing | Job is processing              |
| completed  | Job is completed               |
| failed     | Job has failed                 |
| cancelled  | Job was cancelled              |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create pipeline
pipeline = client.data.create_pipeline(
    name="text_preprocessing",
    description="Text preprocessing pipeline",
    steps=[
        {
            "name": "clean_text",
            "type": "text",
            "config": {
                "remove_special_chars": True,
                "lowercase": True
            }
        }
    ]
)

# Run pipeline
job = client.data.run_pipeline(
    "pipeline_123",
    input={
        "text": "Hello, World!"
    }
)

# Get results
results = client.data.get_pipeline_job(
    "pipeline_123",
    "job_123"
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create pipeline
const pipeline = await client.data.createPipeline({
  name: 'text_preprocessing',
  description: 'Text preprocessing pipeline',
  steps: [
    {
      name: 'clean_text',
      type: 'text',
      config: {
        removeSpecialChars: true,
        lowercase: true
      }
    }
  ]
});

// Run pipeline
const job = await client.data.runPipeline('pipeline_123', {
  input: {
    text: 'Hello, World!'
  }
});

// Get results
const results = await client.data.getPipelineJob('pipeline_123', 'job_123');
```

## Processing Best Practices

1. Define clear schemas
2. Use appropriate steps
3. Handle errors gracefully
4. Monitor performance
5. Validate inputs/outputs
6. Document transformations
7. Test thoroughly
8. Version pipelines 