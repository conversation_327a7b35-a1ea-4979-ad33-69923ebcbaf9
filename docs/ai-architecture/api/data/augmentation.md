---
sidebar_position: 6
---

# Data Augmentation

Enhance your datasets through various augmentation techniques.

## Create Augmentation Pipeline

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/augmentation
```

### Request Body

```json
{
  "name": "text_augmentation",
  "description": "Standard text augmentation pipeline",
  "techniques": [
    {
      "name": "synonym_replacement",
      "type": "text",
      "params": {
        "max_replacements": 3,
        "language": "en",
        "preserve_pos": true
      }
    },
    {
      "name": "back_translation",
      "type": "text",
      "params": {
        "intermediate_languages": ["fr", "de"],
        "preserve_meaning": true
      }
    },
    {
      "name": "random_insertion",
      "type": "text",
      "params": {
        "max_insertions": 2,
        "insertion_type": "synonym"
      }
    }
  ],
  "options": {
    "augmentation_factor": 2.0,
    "preserve_original": true,
    "seed": 42
  }
}
```

### Example Response

```json
{
  "data": {
    "pipeline": {
      "id": "pipeline_123",
      "name": "text_augmentation",
      "dataset_id": "dataset_123",
      "status": "created",
      "created_at": "2024-03-14T12:00:00Z",
      "techniques": [
        {
          "name": "synonym_replacement",
          "type": "text",
          "params": {
            "max_replacements": 3,
            "language": "en"
          }
        }
      ]
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Run Augmentation

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/augmentation/{pipeline_id}/run
```

### Request Body

```json
{
  "options": {
    "batch_size": 1000,
    "max_workers": 4,
    "save_checkpoints": true
  }
}
```

### Example Response

```json
{
  "data": {
    "augmentation": {
      "id": "augmentation_123",
      "pipeline_id": "pipeline_123",
      "dataset_id": "dataset_123",
      "status": "processing",
      "created_at": "2024-03-14T12:00:00Z",
      "options": {
        "batch_size": 1000,
        "max_workers": 4
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Augmentation Status

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/augmentation/{augmentation_id}
```

### Example Response

```json
{
  "data": {
    "augmentation": {
      "id": "augmentation_123",
      "pipeline_id": "pipeline_123",
      "dataset_id": "dataset_123",
      "status": "completed",
      "created_at": "2024-03-14T12:00:00Z",
      "completed_at": "2024-03-14T12:05:00Z",
      "progress": {
        "total_samples": 10000,
        "processed_samples": 10000,
        "percentage": 100
      },
      "statistics": {
        "original_samples": 10000,
        "augmented_samples": 20000,
        "techniques": {
          "synonym_replacement": {
            "samples_processed": 10000,
            "average_replacements": 2.5
          },
          "back_translation": {
            "samples_processed": 10000,
            "languages_used": ["fr", "de"]
          },
          "random_insertion": {
            "samples_processed": 10000,
            "average_insertions": 1.8
          }
        }
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:05:00Z",
    "request_id": "req_123456"
  }
}
```

## List Augmentation Pipelines

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/augmentation
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "pipelines": [
      {
        "id": "pipeline_123",
        "name": "text_augmentation",
        "dataset_id": "dataset_123",
        "status": "active",
        "created_at": "2024-03-14T12:00:00Z",
        "last_run": "2024-03-14T12:05:00Z"
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:05:00Z",
    "request_id": "req_123456"
  }
}
```

## Augmentation Status

| Status     | Description                    |
|------------|--------------------------------|
| created    | Pipeline created               |
| processing | Augmentation running           |
| completed  | Augmentation completed         |
| failed     | Augmentation failed            |
| cancelled  | Augmentation cancelled         |

## Supported Techniques

### Text Augmentation

| Technique           | Description                    |
|---------------------|--------------------------------|
| Synonym Replacement | Replace words with synonyms    |
| Back Translation    | Translate to another language and back |
| Random Insertion    | Insert new words               |
| Random Deletion     | Delete random words            |
| Random Swap         | Swap word positions            |

### Image Augmentation

| Technique           | Description                    |
|---------------------|--------------------------------|
| Rotation            | Rotate image                   |
| Flip                | Horizontal/vertical flip       |
| Crop                | Random crop                    |
| Color Jitter        | Adjust color properties        |
| Noise Addition      | Add random noise               |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create augmentation pipeline
pipeline = client.data.create_augmentation_pipeline(
    "dataset_123",
    name="text_augmentation",
    techniques=[
        {
            "name": "synonym_replacement",
            "type": "text",
            "params": {
                "max_replacements": 3,
                "language": "en"
            }
        }
    ]
)

# Run augmentation
augmentation = client.data.run_augmentation(
    "dataset_123",
    "pipeline_123",
    options={
        "batch_size": 1000,
        "max_workers": 4
    }
)

# Get augmentation status
status = client.data.get_augmentation_status(
    "dataset_123",
    "augmentation_123"
)

# List pipelines
pipelines = client.data.list_augmentation_pipelines(
    "dataset_123",
    page=1,
    limit=10
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create augmentation pipeline
const pipeline = await client.data.createAugmentationPipeline('dataset_123', {
  name: 'text_augmentation',
  techniques: [
    {
      name: 'synonym_replacement',
      type: 'text',
      params: {
        maxReplacements: 3,
        language: 'en'
      }
    }
  ]
});

// Run augmentation
const augmentation = await client.data.runAugmentation(
  'dataset_123',
  'pipeline_123',
  {
    options: {
      batchSize: 1000,
      maxWorkers: 4
    }
  }
);

// Get augmentation status
const status = await client.data.getAugmentationStatus(
  'dataset_123',
  'augmentation_123'
);

// List pipelines
const pipelines = await client.data.listAugmentationPipelines('dataset_123', {
  page: 1,
  limit: 10
});
```

## Augmentation Best Practices

1. Choose appropriate techniques
2. Maintain data quality
3. Preserve original meaning
4. Use reasonable augmentation factors
5. Monitor augmentation results
6. Validate augmented data
7. Document augmentation steps
8. Consider computational costs 