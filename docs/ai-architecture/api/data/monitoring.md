---
sidebar_position: 9
---

# Data Monitoring

Monitor and track changes in your datasets over time.

## Create Monitoring Job

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/monitoring
```

### Request Body

```json
{
  "name": "data_quality_monitoring",
  "description": "Monitor data quality metrics",
  "metrics": [
    {
      "name": "completeness",
      "type": "quality",
      "threshold": 0.95,
      "alert_on": "below"
    },
    {
      "name": "distribution_shift",
      "type": "drift",
      "threshold": 0.1,
      "alert_on": "above"
    },
    {
      "name": "anomaly_detection",
      "type": "anomaly",
      "threshold": 0.05,
      "alert_on": "above"
    }
  ],
  "schedule": {
    "frequency": "daily",
    "time": "00:00",
    "timezone": "UTC"
  },
  "notifications": {
    "email": ["<EMAIL>"],
    "webhook": "https://api.example.com/webhook",
    "slack": "https://hooks.slack.com/services/xxx"
  }
}
```

### Example Response

```json
{
  "data": {
    "monitoring": {
      "id": "monitoring_123",
      "dataset_id": "dataset_123",
      "name": "data_quality_monitoring",
      "status": "active",
      "created_at": "2024-03-14T12:00:00Z",
      "metrics": [
        {
          "name": "completeness",
          "type": "quality",
          "threshold": 0.95
        }
      ]
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Monitoring Results

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/monitoring/{monitoring_id}/results
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| start_date| string  | Start date (ISO format)        |
| end_date  | string  | End date (ISO format)          |
| metrics   | array   | Filter by metrics              |

### Example Response

```json
{
  "data": {
    "results": {
      "monitoring_id": "monitoring_123",
      "dataset_id": "dataset_123",
      "period": {
        "start": "2024-03-01T00:00:00Z",
        "end": "2024-03-14T00:00:00Z"
      },
      "metrics": {
        "completeness": {
          "current": 0.98,
          "history": [
            {
              "timestamp": "2024-03-01T00:00:00Z",
              "value": 0.99
            },
            {
              "timestamp": "2024-03-02T00:00:00Z",
              "value": 0.98
            }
          ],
          "alerts": [
            {
              "timestamp": "2024-03-03T00:00:00Z",
              "value": 0.94,
              "threshold": 0.95,
              "type": "below_threshold"
            }
          ]
        },
        "distribution_shift": {
          "current": 0.05,
          "history": [
            {
              "timestamp": "2024-03-01T00:00:00Z",
              "value": 0.02
            },
            {
              "timestamp": "2024-03-02T00:00:00Z",
              "value": 0.03
            }
          ],
          "alerts": []
        }
      },
      "summary": {
        "total_checks": 14,
        "alerts": 1,
        "status": "warning"
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T00:00:00Z",
    "request_id": "req_123456"
  }
}
```

## List Monitoring Jobs

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/monitoring
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "monitoring": [
      {
        "id": "monitoring_123",
        "dataset_id": "dataset_123",
        "name": "data_quality_monitoring",
        "status": "active",
        "created_at": "2024-03-14T12:00:00Z",
        "last_run": "2024-03-14T00:00:00Z",
        "metrics": [
          "completeness",
          "distribution_shift",
          "anomaly_detection"
        ]
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T00:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Monitoring Status

| Status     | Description                    |
|------------|--------------------------------|
| active     | Monitoring is active           |
| paused     | Monitoring is paused           |
| inactive   | Monitoring is inactive         |
| error      | Monitoring has errors          |

## Available Metrics

### Quality Metrics

- Completeness
- Consistency
- Accuracy
- Validity
- Uniqueness

### Drift Metrics

- Distribution shift
- Feature drift
- Concept drift
- Data drift
- Model drift

### Anomaly Metrics

- Statistical anomalies
- Pattern anomalies
- Outlier detection
- Change detection
- Trend analysis

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create monitoring job
monitoring = client.data.create_monitoring(
    "dataset_123",
    name="data_quality_monitoring",
    metrics=[
        {
            "name": "completeness",
            "type": "quality",
            "threshold": 0.95
        }
    ]
)

# Get monitoring results
results = client.data.get_monitoring_results(
    "dataset_123",
    "monitoring_123",
    start_date="2024-03-01",
    end_date="2024-03-14"
)

# List monitoring jobs
jobs = client.data.list_monitoring(
    "dataset_123",
    page=1,
    limit=10
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create monitoring job
const monitoring = await client.data.createMonitoring('dataset_123', {
  name: 'data_quality_monitoring',
  metrics: [
    {
      name: 'completeness',
      type: 'quality',
      threshold: 0.95
    }
  ]
});

// Get monitoring results
const results = await client.data.getMonitoringResults(
  'dataset_123',
  'monitoring_123',
  {
    startDate: '2024-03-01',
    endDate: '2024-03-14'
  }
);

// List monitoring jobs
const jobs = await client.data.listMonitoring('dataset_123', {
  page: 1,
  limit: 10
});
```

## Monitoring Best Practices

1. Define clear metrics
2. Set appropriate thresholds
3. Configure notifications
4. Monitor regularly
5. Track historical data
6. Analyze trends
7. Respond to alerts
8. Document findings 