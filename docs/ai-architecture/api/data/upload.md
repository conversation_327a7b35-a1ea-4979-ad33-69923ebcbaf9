---
sidebar_position: 1
---

# Data Upload

Upload and manage your training and inference data.

## Endpoint

```
POST /v1/data/upload
```

## Request Body

```json
{
  "name": "training_dataset",
  "type": "text",
  "format": "json",
  "description": "Training data for language model",
  "metadata": {
    "source": "customer_feedback",
    "language": "en"
  }
}
```

## Example Request

```bash
curl -X POST \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "training_dataset",
       "type": "text",
       "format": "json",
       "description": "Training data for language model"
     }' \
     "https://api.ai-platform.example.com/v1/data/upload"
```

## Example Response

```json
{
  "data": {
    "upload": {
      "id": "upload_123",
      "name": "training_dataset",
      "type": "text",
      "format": "json",
      "status": "processing",
      "created_at": "2024-03-14T12:00:00Z",
      "upload_url": "https://storage.ai-platform.example.com/upload_123"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Supported Data Types

| Type      | Description                    | Formats                    |
|-----------|--------------------------------|----------------------------|
| text      | Text data                      | json, csv, txt            |
| image     | Image data                     | jpg, png, tiff            |
| audio     | Audio data                     | mp3, wav, flac            |
| video     | Video data                     | mp4, avi, mov             |
| tabular   | Tabular data                   | csv, parquet, excel       |

## Error Codes

| Code              | Description                    |
|-------------------|--------------------------------|
| invalid_request   | Invalid input parameters       |
| invalid_format    | Unsupported data format        |
| quota_exceeded    | Storage quota exceeded         |
| rate_limit_exceeded| Too many requests            |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Upload data
upload = client.data.upload(
    name="training_dataset",
    type="text",
    format="json",
    description="Training data for language model"
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Upload data
const upload = await client.data.upload({
  name: 'training_dataset',
  type: 'text',
  format: 'json',
  description: 'Training data for language model'
});
```

## Best Practices

1. Validate data before uploading
2. Use appropriate data formats
3. Include meaningful metadata
4. Monitor upload progress
5. Handle errors gracefully

## Storage Limits

| Plan      | Storage Limit |
|-----------|---------------|
| Standard  | 10 GB         |
| Pro       | 100 GB        |
| Enterprise| Custom        | 