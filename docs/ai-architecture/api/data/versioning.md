---
sidebar_position: 3
---

# Data Versioning

Track and manage different versions of your datasets.

## Create Version

### Endpoint

```
POST /v1/data/datasets/{dataset_id}/versions
```

### Request Body

```json
{
  "name": "v1.0.0",
  "description": "Initial version with customer feedback data",
  "tags": ["production", "validated"],
  "metadata": {
    "validation_score": 0.95,
    "data_quality": "high"
  }
}
```

### Example Response

```json
{
  "data": {
    "version": {
      "id": "version_123",
      "name": "v1.0.0",
      "dataset_id": "dataset_123",
      "description": "Initial version with customer feedback data",
      "tags": ["production", "validated"],
      "created_at": "2024-03-14T12:00:00Z",
      "metadata": {
        "validation_score": 0.95,
        "data_quality": "high"
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## List Versions

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/versions
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| tag       | string  | Filter by tag                  |

### Example Response

```json
{
  "data": {
    "versions": [
      {
        "id": "version_123",
        "name": "v1.0.0",
        "dataset_id": "dataset_123",
        "description": "Initial version",
        "tags": ["production"],
        "created_at": "2024-03-14T12:00:00Z"
      },
      {
        "id": "version_124",
        "name": "v1.0.1",
        "dataset_id": "dataset_123",
        "description": "Updated version",
        "tags": ["staging"],
        "created_at": "2024-03-15T12:00:00Z"
      }
    ],
    "pagination": {
      "total": 2,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-15T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Version Details

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/versions/{version_id}
```

### Example Response

```json
{
  "data": {
    "version": {
      "id": "version_123",
      "name": "v1.0.0",
      "dataset_id": "dataset_123",
      "description": "Initial version",
      "tags": ["production"],
      "created_at": "2024-03-14T12:00:00Z",
      "metadata": {
        "validation_score": 0.95,
        "data_quality": "high"
      },
      "changes": {
        "added_samples": 1000,
        "removed_samples": 50,
        "modified_samples": 200
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-15T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Compare Versions

### Endpoint

```
GET /v1/data/datasets/{dataset_id}/versions/compare
```

### Query Parameters

| Parameter     | Type   | Description                    |
|---------------|--------|--------------------------------|
| version1_id   | string | First version ID              |
| version2_id   | string | Second version ID             |

### Example Response

```json
{
  "data": {
    "comparison": {
      "version1": {
        "id": "version_123",
        "name": "v1.0.0"
      },
      "version2": {
        "id": "version_124",
        "name": "v1.0.1"
      },
      "differences": {
        "added_samples": 100,
        "removed_samples": 20,
        "modified_samples": 50,
        "metadata_changes": {
          "validation_score": {
            "from": 0.95,
            "to": 0.97
          }
        }
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-15T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create version
version = client.data.create_version(
    "dataset_123",
    name="v1.0.0",
    description="Initial version",
    tags=["production"]
)

# List versions
versions = client.data.list_versions(
    "dataset_123",
    page=1,
    limit=10
)

# Get version details
version_details = client.data.get_version(
    "dataset_123",
    "version_123"
)

# Compare versions
comparison = client.data.compare_versions(
    "dataset_123",
    version1_id="version_123",
    version2_id="version_124"
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create version
const version = await client.data.createVersion('dataset_123', {
  name: 'v1.0.0',
  description: 'Initial version',
  tags: ['production']
});

// List versions
const versions = await client.data.listVersions('dataset_123', {
  page: 1,
  limit: 10
});

// Get version details
const versionDetails = await client.data.getVersion(
  'dataset_123',
  'version_123'
);

// Compare versions
const comparison = await client.data.compareVersions('dataset_123', {
  version1Id: 'version_123',
  version2Id: 'version_124'
});
```

## Versioning Best Practices

1. Use semantic versioning
2. Document version changes
3. Tag important versions
4. Maintain version history
5. Track metadata changes
6. Implement rollback capability
7. Validate version integrity
8. Set up version policies 