---
sidebar_position: 4
---

# Monitoring Dashboards

Create and manage custom monitoring dashboards.

## Create Dashboard

### Endpoint

```
POST /v1/monitoring/dashboards
```

### Request Body

```json
{
  "name": "Model Performance Dashboard",
  "description": "Overview of model performance metrics",
  "layout": "grid",
  "panels": [
    {
      "title": "Model Latency",
      "type": "line",
      "metrics": ["model_latency"],
      "position": {
        "x": 0,
        "y": 0,
        "width": 6,
        "height": 4
      },
      "options": {
        "interval": "5m",
        "aggregation": "avg"
      }
    },
    {
      "title": "Error Rate",
      "type": "gauge",
      "metrics": ["model_errors"],
      "position": {
        "x": 6,
        "y": 0,
        "width": 6,
        "height": 4
      },
      "options": {
        "thresholds": {
          "warning": 5,
          "critical": 10
        }
      }
    }
  ],
  "refresh_interval": "1m",
  "tags": ["models", "performance"]
}
```

### Example Response

```json
{
  "data": {
    "dashboard": {
      "id": "dashboard_123",
      "name": "Model Performance Dashboard",
      "description": "Overview of model performance metrics",
      "layout": "grid",
      "panels": [
        {
          "id": "panel_123",
          "title": "Model Latency",
          "type": "line",
          "metrics": ["model_latency"],
          "position": {
            "x": 0,
            "y": 0,
            "width": 6,
            "height": 4
          }
        },
        {
          "id": "panel_124",
          "title": "Error Rate",
          "type": "gauge",
          "metrics": ["model_errors"],
          "position": {
            "x": 6,
            "y": 0,
            "width": 6,
            "height": 4
          }
        }
      ],
      "refresh_interval": "1m",
      "tags": ["models", "performance"],
      "created_at": "2024-03-14T12:00:00Z",
      "updated_at": "2024-03-14T12:00:00Z"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Dashboard

### Endpoint

```
GET /v1/monitoring/dashboards/{dashboard_id}
```

### Example Response

```json
{
  "data": {
    "dashboard": {
      "id": "dashboard_123",
      "name": "Model Performance Dashboard",
      "description": "Overview of model performance metrics",
      "layout": "grid",
      "panels": [
        {
          "id": "panel_123",
          "title": "Model Latency",
          "type": "line",
          "metrics": ["model_latency"],
          "position": {
            "x": 0,
            "y": 0,
            "width": 6,
            "height": 4
          },
          "data": {
            "values": [
              {
                "timestamp": "2024-03-14T12:00:00Z",
                "value": 150.5
              }
            ]
          }
        }
      ],
      "refresh_interval": "1m",
      "tags": ["models", "performance"],
      "created_at": "2024-03-14T12:00:00Z",
      "updated_at": "2024-03-14T12:00:00Z"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## List Dashboards

### Endpoint

```
GET /v1/monitoring/dashboards
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 100)  |
| tags      | string  | Filter by tags                 |
| search    | string  | Search query                   |

### Example Response

```json
{
  "data": {
    "dashboards": [
      {
        "id": "dashboard_123",
        "name": "Model Performance Dashboard",
        "description": "Overview of model performance metrics",
        "tags": ["models", "performance"],
        "created_at": "2024-03-14T12:00:00Z",
        "updated_at": "2024-03-14T12:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 100,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Panel Types

| Type      | Description                    | Use Case                      |
|-----------|--------------------------------|-------------------------------|
| line      | Line chart                    | Time series data              |
| bar       | Bar chart                     | Categorical data              |
| gauge     | Gauge chart                   | Current value with thresholds |
| pie       | Pie chart                     | Distribution data             |
| table     | Data table                    | Detailed metrics              |
| stat      | Statistic                     | Single value                  |
| heatmap   | Heat map                      | Correlation data              |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create dashboard
dashboard = client.monitoring.create_dashboard(
    name="Model Performance Dashboard",
    description="Overview of model performance metrics",
    panels=[
        {
            "title": "Model Latency",
            "type": "line",
            "metrics": ["model_latency"],
            "position": {"x": 0, "y": 0, "width": 6, "height": 4}
        }
    ]
)

# Get dashboard
dashboard_details = client.monitoring.get_dashboard("dashboard_123")

# List dashboards
dashboards = client.monitoring.list_dashboards(
    page=1,
    limit=100,
    tags=["models"]
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create dashboard
const dashboard = await client.monitoring.createDashboard({
  name: 'Model Performance Dashboard',
  description: 'Overview of model performance metrics',
  panels: [
    {
      title: 'Model Latency',
      type: 'line',
      metrics: ['model_latency'],
      position: { x: 0, y: 0, width: 6, height: 4 }
    }
  ]
});

// Get dashboard
const dashboardDetails = await client.monitoring.getDashboard('dashboard_123');

// List dashboards
const dashboards = await client.monitoring.listDashboards({
  page: 1,
  limit: 100,
  tags: ['models']
});
```

## Dashboard Best Practices

1. Organize panels logically
2. Use appropriate visualizations
3. Set meaningful thresholds
4. Include key metrics
5. Add context and labels
6. Set refresh intervals
7. Use tags for organization
8. Share with team members 