---
sidebar_position: 5
---

# Monitoring Reports

Generate and schedule monitoring reports.

## Create Report

### Endpoint

```
POST /v1/monitoring/reports
```

### Request Body

```json
{
  "name": "Weekly Model Performance Report",
  "description": "Weekly summary of model performance metrics",
  "type": "scheduled",
  "schedule": {
    "frequency": "weekly",
    "day": "monday",
    "time": "09:00",
    "timezone": "UTC"
  },
  "content": {
    "sections": [
      {
        "title": "Model Performance",
        "metrics": ["model_accuracy", "model_latency", "model_errors"],
        "type": "summary",
        "time_range": "7d"
      },
      {
        "title": "Data Quality",
        "metrics": ["data_quality", "data_freshness"],
        "type": "trend",
        "time_range": "7d"
      },
      {
        "title": "System Health",
        "metrics": ["cpu_usage", "memory_usage", "error_count"],
        "type": "summary",
        "time_range": "7d"
      }
    ],
    "format": "pdf",
    "include_charts": true,
    "include_tables": true
  },
  "recipients": [
    {
      "type": "email",
      "address": "<EMAIL>"
    },
    {
      "type": "slack",
      "channel": "#model-monitoring"
    }
  ]
}
```

### Example Response

```json
{
  "data": {
    "report": {
      "id": "report_123",
      "name": "Weekly Model Performance Report",
      "description": "Weekly summary of model performance metrics",
      "type": "scheduled",
      "schedule": {
        "frequency": "weekly",
        "day": "monday",
        "time": "09:00",
        "timezone": "UTC",
        "next_run": "2024-03-18T09:00:00Z"
      },
      "content": {
        "sections": [
          {
            "id": "section_123",
            "title": "Model Performance",
            "metrics": ["model_accuracy", "model_latency", "model_errors"],
            "type": "summary",
            "time_range": "7d"
          }
        ],
        "format": "pdf",
        "include_charts": true,
        "include_tables": true
      },
      "recipients": [
        {
          "type": "email",
          "address": "<EMAIL>"
        }
      ],
      "status": "active",
      "created_at": "2024-03-14T12:00:00Z",
      "updated_at": "2024-03-14T12:00:00Z"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Report

### Endpoint

```
GET /v1/monitoring/reports/{report_id}
```

### Example Response

```json
{
  "data": {
    "report": {
      "id": "report_123",
      "name": "Weekly Model Performance Report",
      "description": "Weekly summary of model performance metrics",
      "type": "scheduled",
      "schedule": {
        "frequency": "weekly",
        "day": "monday",
        "time": "09:00",
        "timezone": "UTC",
        "next_run": "2024-03-18T09:00:00Z",
        "last_run": "2024-03-11T09:00:00Z"
      },
      "content": {
        "sections": [
          {
            "id": "section_123",
            "title": "Model Performance",
            "metrics": ["model_accuracy", "model_latency", "model_errors"],
            "type": "summary",
            "time_range": "7d",
            "data": {
              "summary": {
                "model_accuracy": 95.5,
                "model_latency": 150.5,
                "model_errors": 10
              }
            }
          }
        ],
        "format": "pdf",
        "include_charts": true,
        "include_tables": true
      },
      "recipients": [
        {
          "type": "email",
          "address": "<EMAIL>"
        }
      ],
      "status": "active",
      "created_at": "2024-03-14T12:00:00Z",
      "updated_at": "2024-03-14T12:00:00Z"
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## List Reports

### Endpoint

```
GET /v1/monitoring/reports
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 100)  |
| type      | string  | Report type                    |
| status    | string  | Report status                  |
| search    | string  | Search query                   |

### Example Response

```json
{
  "data": {
    "reports": [
      {
        "id": "report_123",
        "name": "Weekly Model Performance Report",
        "type": "scheduled",
        "status": "active",
        "schedule": {
          "frequency": "weekly",
          "next_run": "2024-03-18T09:00:00Z"
        },
        "created_at": "2024-03-14T12:00:00Z",
        "updated_at": "2024-03-14T12:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 100,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Report Types

| Type      | Description                    | Use Case                      |
|-----------|--------------------------------|-------------------------------|
| scheduled | Regular automated reports      | Weekly/monthly summaries      |
| on-demand | Generated when requested       | Ad-hoc analysis              |
| triggered | Generated on specific events   | Alerts and notifications      |

## Report Formats

| Format    | Description                    | Use Case                      |
|-----------|--------------------------------|-------------------------------|
| pdf       | Portable Document Format       | Formal reports                |
| html      | Web page format               | Interactive reports           |
| csv       | Comma-separated values         | Data analysis                 |
| json      | JavaScript Object Notation     | API integration               |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create report
report = client.monitoring.create_report(
    name="Weekly Model Performance Report",
    type="scheduled",
    schedule={
        "frequency": "weekly",
        "day": "monday",
        "time": "09:00",
        "timezone": "UTC"
    },
    content={
        "sections": [
            {
                "title": "Model Performance",
                "metrics": ["model_accuracy", "model_latency"],
                "type": "summary",
                "time_range": "7d"
            }
        ],
        "format": "pdf"
    }
)

# Get report
report_details = client.monitoring.get_report("report_123")

# List reports
reports = client.monitoring.list_reports(
    page=1,
    limit=100,
    type="scheduled"
)
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create report
const report = await client.monitoring.createReport({
  name: 'Weekly Model Performance Report',
  type: 'scheduled',
  schedule: {
    frequency: 'weekly',
    day: 'monday',
    time: '09:00',
    timezone: 'UTC'
  },
  content: {
    sections: [
      {
        title: 'Model Performance',
        metrics: ['model_accuracy', 'model_latency'],
        type: 'summary',
        timeRange: '7d'
      }
    ],
    format: 'pdf'
  }
});

// Get report
const reportDetails = await client.monitoring.getReport('report_123');

// List reports
const reports = await client.monitoring.listReports({
  page: 1,
  limit: 100,
  type: 'scheduled'
});
```

## Report Best Practices

1. Define clear objectives
2. Include relevant metrics
3. Set appropriate schedules
4. Choose right format
5. Add context and insights
6. Use visualizations
7. Include recommendations
8. Review and update regularly 