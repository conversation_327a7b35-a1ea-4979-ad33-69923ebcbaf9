---
sidebar_position: 3
---

# Monitoring Metrics

Track and analyze system and model performance metrics.

## Get Metrics

### Endpoint

```
GET /v1/monitoring/metrics
```

### Query Parameters

| Parameter   | Type    | Description                    |
|-------------|---------|--------------------------------|
| start_time  | string  | Start time (ISO format)        |
| end_time    | string  | End time (ISO format)          |
| metric      | string  | Metric name                    |
| interval    | string  | Time interval (1m, 5m, 1h)     |
| aggregation | string  | Aggregation method (avg, max)  |
| page        | integer | Page number (default: 1)       |
| limit       | integer | Items per page (default: 100)  |

### Example Response

```json
{
  "data": {
    "metrics": [
      {
        "id": "metric_123",
        "name": "model_latency",
        "value": 150.5,
        "timestamp": "2024-03-14T12:00:00Z",
        "labels": {
          "model_id": "model_123",
          "environment": "production"
        },
        "metadata": {
          "unit": "ms",
          "type": "gauge"
        }
      }
    ],
    "pagination": {
      "total": 1000,
      "page": 1,
      "limit": 100,
      "pages": 10
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Metric Details

### Endpoint

```
GET /v1/monitoring/metrics/{metric_id}
```

### Example Response

```json
{
  "data": {
    "metric": {
      "id": "metric_123",
      "name": "model_latency",
      "description": "Model prediction latency",
      "type": "gauge",
      "unit": "ms",
      "values": [
        {
          "value": 150.5,
          "timestamp": "2024-03-14T12:00:00Z",
          "labels": {
            "model_id": "model_123",
            "environment": "production"
          }
        }
      ],
      "statistics": {
        "min": 100.0,
        "max": 200.0,
        "avg": 150.5,
        "p95": 180.0,
        "p99": 190.0
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Metric Types

| Type      | Description                    | Example                      |
|-----------|--------------------------------|------------------------------|
| gauge     | Current value                  | CPU usage, memory usage      |
| counter   | Increasing value               | Request count, error count   |
| histogram | Value distribution             | Response time distribution   |
| summary   | Statistical summary            | Model accuracy summary       |

## Available Metrics

### System Metrics

| Metric           | Type    | Unit | Description                    |
|------------------|---------|------|--------------------------------|
| cpu_usage        | gauge   | %    | CPU utilization               |
| memory_usage     | gauge   | %    | Memory utilization            |
| disk_usage       | gauge   | %    | Disk space utilization        |
| network_traffic  | counter | bytes| Network traffic               |
| request_count    | counter | count| HTTP request count           |
| error_count      | counter | count| Error count                  |

### Model Metrics

| Metric           | Type    | Unit | Description                    |
|------------------|---------|------|--------------------------------|
| model_latency    | gauge   | ms   | Prediction latency            |
| model_throughput | gauge   | req/s| Requests per second          |
| model_accuracy   | gauge   | %    | Model accuracy                |
| model_errors     | counter | count| Model error count            |
| model_confidence | gauge   | %    | Prediction confidence         |

### Data Metrics

| Metric           | Type    | Unit | Description                    |
|------------------|---------|------|--------------------------------|
| data_volume      | counter | bytes| Processed data volume         |
| data_quality     | gauge   | %    | Data quality score            |
| data_freshness   | gauge   | min  | Data freshness                |
| data_errors      | counter | count| Data processing errors       |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Get metrics
metrics = client.monitoring.get_metrics(
    start_time="2024-03-14T00:00:00Z",
    end_time="2024-03-14T12:00:00Z",
    metric="model_latency",
    interval="5m",
    aggregation="avg",
    page=1,
    limit=100
)

# Get metric details
metric_details = client.monitoring.get_metric("metric_123")
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Get metrics
const metrics = await client.monitoring.getMetrics({
  startTime: '2024-03-14T00:00:00Z',
  endTime: '2024-03-14T12:00:00Z',
  metric: 'model_latency',
  interval: '5m',
  aggregation: 'avg',
  page: 1,
  limit: 100
});

// Get metric details
const metricDetails = await client.monitoring.getMetric('metric_123');
```

## Metrics Best Practices

1. Use consistent naming
2. Include units
3. Add labels
4. Set appropriate intervals
5. Monitor trends
6. Set thresholds
7. Use aggregations
8. Document metrics 