---
sidebar_position: 2
---

# Monitoring Logs

Access and analyze system logs for monitoring and debugging.

## Get Logs

### Endpoint

```
GET /v1/monitoring/logs
```

### Query Parameters

| Parameter   | Type    | Description                    |
|-------------|---------|--------------------------------|
| start_time  | string  | Start time (ISO format)        |
| end_time    | string  | End time (ISO format)          |
| level       | string  | Log level (info, warn, error)  |
| source      | string  | Log source (model, data, etc.) |
| search      | string  | Search query                   |
| page        | integer | Page number (default: 1)       |
| limit       | integer | Items per page (default: 100)  |

### Example Response

```json
{
  "data": {
    "logs": [
      {
        "id": "log_123",
        "timestamp": "2024-03-14T12:00:00Z",
        "level": "error",
        "source": "model",
        "message": "Model prediction failed",
        "context": {
          "model_id": "model_123",
          "request_id": "req_123456",
          "error": "Invalid input format"
        },
        "metadata": {
          "environment": "production",
          "version": "1.0.0"
        }
      }
    ],
    "pagination": {
      "total": 1000,
      "page": 1,
      "limit": 100,
      "pages": 10
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Log Details

### Endpoint

```
GET /v1/monitoring/logs/{log_id}
```

### Example Response

```json
{
  "data": {
    "log": {
      "id": "log_123",
      "timestamp": "2024-03-14T12:00:00Z",
      "level": "error",
      "source": "model",
      "message": "Model prediction failed",
      "context": {
        "model_id": "model_123",
        "request_id": "req_123456",
        "error": "Invalid input format",
        "stack_trace": "...",
        "input_data": {
          "text": "example input"
        }
      },
      "metadata": {
        "environment": "production",
        "version": "1.0.0",
        "host": "server-1",
        "region": "us-west"
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Log Levels

| Level     | Description                    | Use Case                      |
|-----------|--------------------------------|-------------------------------|
| debug     | Detailed debugging information | Development and debugging      |
| info      | General information            | Normal operation events       |
| warn      | Warning messages               | Potential issues              |
| error     | Error messages                 | Operation failures            |
| critical  | Critical errors                | System failures               |

## Log Sources

| Source    | Description                    | Log Types                     |
|-----------|--------------------------------|-------------------------------|
| model     | Model-related logs             | Predictions, training, errors |
| data      | Data-related logs              | Processing, validation        |
| system    | System-related logs            | Performance, health           |
| security  | Security-related logs          | Authentication, access        |
| custom    | Custom application logs        | Business logic, events        |

## Log Retention

| Plan      | Retention Period | Storage Limit |
|-----------|------------------|---------------|
| Standard  | 7 days          | 1 GB          |
| Pro       | 30 days         | 10 GB         |
| Enterprise| Custom           | Custom        |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Get logs
logs = client.monitoring.get_logs(
    start_time="2024-03-14T00:00:00Z",
    end_time="2024-03-14T12:00:00Z",
    level="error",
    source="model",
    page=1,
    limit=100
)

# Get log details
log_details = client.monitoring.get_log("log_123")
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Get logs
const logs = await client.monitoring.getLogs({
  startTime: '2024-03-14T00:00:00Z',
  endTime: '2024-03-14T12:00:00Z',
  level: 'error',
  source: 'model',
  page: 1,
  limit: 100
});

// Get log details
const logDetails = await client.monitoring.getLog('log_123');
```

## Logging Best Practices

1. Use appropriate log levels
2. Include relevant context
3. Structure log messages
4. Add request IDs
5. Include timestamps
6. Add metadata
7. Implement log rotation
8. Monitor log volume 