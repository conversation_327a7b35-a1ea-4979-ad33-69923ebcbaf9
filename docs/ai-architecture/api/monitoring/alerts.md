---
sidebar_position: 1
---

# Monitoring <PERSON>erts

Configure and manage monitoring alerts for your AI models and data.

## Create Alert

### Endpoint

```
POST /v1/monitoring/alerts
```

### Request Body

```json
{
  "name": "model_performance_alert",
  "description": "Alert on model performance degradation",
  "type": "model",
  "model_id": "model_123",
  "conditions": {
    "metric": "accuracy",
    "operator": "below",
    "threshold": 0.95,
    "window": "1h",
    "min_samples": 100
  },
  "notifications": {
    "channels": [
      {
        "type": "email",
        "recipients": ["<EMAIL>"]
      },
      {
        "type": "slack",
        "webhook": "https://hooks.slack.com/services/xxx"
      }
    ],
    "cooldown": "1h"
  }
}
```

### Example Response

```json
{
  "data": {
    "alert": {
      "id": "alert_123",
      "name": "model_performance_alert",
      "type": "model",
      "status": "active",
      "created_at": "2024-03-14T12:00:00Z",
      "conditions": {
        "metric": "accuracy",
        "operator": "below",
        "threshold": 0.95
      }
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## List Alerts

### Endpoint

```
GET /v1/monitoring/alerts
```

### Query Parameters

| Parameter | Type    | Description                    |
|-----------|---------|--------------------------------|
| page      | integer | Page number (default: 1)       |
| limit     | integer | Items per page (default: 10)   |
| type      | string  | Filter by alert type           |
| status    | string  | Filter by status               |

### Example Response

```json
{
  "data": {
    "alerts": [
      {
        "id": "alert_123",
        "name": "model_performance_alert",
        "type": "model",
        "status": "active",
        "created_at": "2024-03-14T12:00:00Z",
        "last_triggered": "2024-03-14T11:30:00Z"
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "pages": 1
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Get Alert Details

### Endpoint

```
GET /v1/monitoring/alerts/{alert_id}
```

### Example Response

```json
{
  "data": {
    "alert": {
      "id": "alert_123",
      "name": "model_performance_alert",
      "type": "model",
      "status": "active",
      "created_at": "2024-03-14T12:00:00Z",
      "conditions": {
        "metric": "accuracy",
        "operator": "below",
        "threshold": 0.95,
        "window": "1h",
        "min_samples": 100
      },
      "notifications": {
        "channels": [
          {
            "type": "email",
            "recipients": ["<EMAIL>"]
          }
        ],
        "cooldown": "1h"
      },
      "history": [
        {
          "timestamp": "2024-03-14T11:30:00Z",
          "status": "triggered",
          "value": 0.94,
          "threshold": 0.95
        }
      ]
    }
  },
  "meta": {
    "timestamp": "2024-03-14T12:00:00Z",
    "request_id": "req_123456"
  }
}
```

## Alert Types

| Type           | Description                    | Metrics                        |
|----------------|--------------------------------|--------------------------------|
| model          | Model performance alerts       | accuracy, latency, throughput  |
| data           | Data quality alerts            | drift, anomalies, completeness |
| system         | System health alerts           | CPU, memory, disk usage        |
| custom         | Custom metric alerts           | User-defined metrics           |

## Alert Status

| Status     | Description                    |
|------------|--------------------------------|
| active     | Alert is active                |
| paused     | Alert is paused                |
| inactive   | Alert is inactive              |
| error      | Alert has errors               |

## Notification Channels

| Channel    | Description                    | Configuration                  |
|------------|--------------------------------|--------------------------------|
| email      | Email notifications            | Recipients list               |
| slack      | Slack notifications            | Webhook URL                   |
| webhook    | Custom webhook                 | Webhook URL and headers        |
| pagerduty  | PagerDuty integration          | Service key                   |

## SDK Examples

### Python

```python
from ai_platform import Client

client = Client(api_key="YOUR_API_KEY")

# Create alert
alert = client.monitoring.create_alert(
    name="model_performance_alert",
    type="model",
    model_id="model_123",
    conditions={
        "metric": "accuracy",
        "operator": "below",
        "threshold": 0.95
    }
)

# List alerts
alerts = client.monitoring.list_alerts(
    page=1,
    limit=10
)

# Get alert details
details = client.monitoring.get_alert("alert_123")
```

### JavaScript

```javascript
import { Client } from '@ai-platform/sdk';

const client = new Client({
  apiKey: 'YOUR_API_KEY'
});

// Create alert
const alert = await client.monitoring.createAlert({
  name: 'model_performance_alert',
  type: 'model',
  modelId: 'model_123',
  conditions: {
    metric: 'accuracy',
    operator: 'below',
    threshold: 0.95
  }
});

// List alerts
const alerts = await client.monitoring.listAlerts({
  page: 1,
  limit: 10
});

// Get alert details
const details = await client.monitoring.getAlert('alert_123');
```

## Alert Best Practices

1. Set appropriate thresholds
2. Use meaningful alert names
3. Configure notification channels
4. Implement alert cooldowns
5. Monitor alert history
6. Regular alert reviews
7. Document alert policies
8. Test alert configurations 