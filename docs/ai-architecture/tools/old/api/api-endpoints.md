# MLOps Platform API Endpoints

This document provides a comprehensive list of all available endpoints for the MLOps platform services.

## PostgreSQL

**Base URL**: `postgresql:5432`

### Connection Details
- Host: `postgresql`
- Port: `5432`
- Default Database: `${POSTGRES_DB}`
- Username: `${POSTGRES_USER}`
- Password: `${POSTGRES_PASSWORD}`

## MongoDB

**Base URL**: `mongodb:27017`

### Connection Details
- Host: `mongodb`
- Port: `27017`
- Database: `${MONGODB_DATABASE}`
- Username: `${MONGODB_USERNAME}`
- Password: `${MONGODB_PASSWORD}`

## MinIO

**Base URL**: `http://minio:9000`

### Authentication
- Access Key: `${MINIO_ACCESS_KEY}`
- Secret Key: `${MINIO_SECRET_KEY}`

### Endpoints
- Console: `http://minio:9001`
- API: `http://minio:9000`

### Common Operations
1. Create Bucket
   ```
   PUT /{bucket-name}
   ```

2. Upload Object
   ```
   PUT /{bucket-name}/{object-name}
   ```

3. Download Object
   ```
   GET /{bucket-name}/{object-name}
   ```

4. List Objects
   ```
   GET /{bucket-name}?prefix={prefix}&delimiter={delimiter}
   ```

## LakeFS

**Base URL**: `http://lakefs:8000`

### Authentication
- Access Key: `${LAKEFS_ACCESS_KEY}`
- Secret Key: `${LAKEFS_SECRET_KEY}`

### Endpoints
1. Create Repository
   ```
   POST /repositories
   ```

2. List Repositories
   ```
   GET /repositories
   ```

3. Create Branch
   ```
   POST /repositories/{repository}/branches
   ```

4. List Objects
   ```
   GET /repositories/{repository}/refs/{ref}/objects/ls
   ```

5. Upload Object
   ```
   PUT /repositories/{repository}/refs/{ref}/objects
   ```

6. Download Object
   ```
   GET /repositories/{repository}/refs/{ref}/objects
   ```

## Trino

**Base URL**: `http://trino:8080`

### Authentication
- Username: `${TRINO_USER}`
- Password: `${TRINO_PASSWORD}`

### Endpoints
1. Execute Query
   ```
   POST /v1/statement
   ```

2. Get Query Status
   ```
   GET /v1/query/{queryId}
   ```

3. Cancel Query
   ```
   DELETE /v1/query/{queryId}
   ```

4. Get Query Results
   ```
   GET /v1/query/{queryId}/results
   ```

### Example Queries
1. Query PostgreSQL
   ```sql
   SELECT * FROM postgresql.public.your_table;
   ```

2. Query MongoDB
   ```sql
   SELECT * FROM mongodb.your_database.your_collection;
   ```

3. Query MinIO (via LakeFS)
   ```sql
   SELECT * FROM lakefs.your_repository.your_branch.your_table;
   ```

## OpenMetadata

**Base URL**: `http://openmetadata:8585`

### Authentication
- Username: `${OPENMETADATA_USER}`
- Password: `${OPENMETADATA_PASSWORD}`

### Endpoints
1. Create Service
   ```
   POST /api/v1/services
   ```

2. List Services
   ```
   GET /api/v1/services
   ```

3. Create Pipeline
   ```
   POST /api/v1/pipelines
   ```

4. List Pipelines
   ```
   GET /api/v1/pipelines
   ```

5. Create Dashboard
   ```
   POST /api/v1/dashboards
   ```

6. List Dashboards
   ```
   GET /api/v1/dashboards
   ```

## Orchestration & Integration

- **Airflow**: Orchestrates ETL and ML pipelines, triggers Trino queries, manages data movement, and triggers Kubeflow pipelines.
- **Trino**: Performs distributed SQL queries on data in MinIO, LakeFS, PostgreSQL, and MongoDB.
- **LakeFS**: Provides version control for all ingested and processed data.
- **MinIO**: Stores raw, processed, and model artifact data.
- **Feature Store**: Manages and versions features for ML pipelines (integrated with LakeFS/MinIO).
- **Kubeflow**: Runs ML pipelines, reads versioned data/features, logs experiments to MLflow, and deploys models via KServe.
- **MLflow**: Tracks experiments, metrics, and manages model registry.
- **OpenMetadata**: Catalogs all data, features, and models, and tracks lineage.
- **KServe**: Serves models for inference and sends metrics to monitoring.
- **Monitoring/Alerting**: Receives metrics and alerts from all major components.

## 3. Example API/Service Endpoints

Keep your existing endpoint documentation, but consider grouping them by flow (e.g., "Data Ingestion APIs", "Feature Store APIs", "ML Pipeline APIs", "Model Serving APIs", etc.) for clarity.

## 4. Security and Error Handling

Keep your security and error handling notes as they are—they are already professional and comprehensive.

## Error Handling
- All endpoints return appropriate HTTP status codes
- Error responses include detailed messages
- Implement proper retry mechanisms for transient failures
- Log all API errors for monitoring and debugging 