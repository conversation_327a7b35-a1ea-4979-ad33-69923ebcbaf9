---
id: api
title: API Documentation
sidebar_label: API Overview
---

# API Documentation

Welcome to the MLOps platform API documentation. This documentation provides detailed information about all available APIs, their endpoints, authentication methods, and usage examples.

## API Categories

### [Model Management](./model-management/index.md)
APIs for managing machine learning models, including serving, registry, and experiment tracking.

### [Data Management](./data-management/index.md)
APIs for managing data, including feature stores, data quality, and storage systems.

### [Monitoring](./monitoring/index.md)
APIs for monitoring model performance and system health, including alerting.

### [Orchestration](./orchestration/index.md)
APIs for workflow orchestration and pipeline management.

### [Visualization](./visualization/index.md)
Diagrams and visual documentation for the platform.

## Getting Started

1. Review the [API Endpoints](./api-endpoints.md) for a complete list of available endpoints
2. Check the authentication requirements for each API
3. Follow the usage examples to integrate with the platform
4. Refer to the troubleshooting guide for common issues 