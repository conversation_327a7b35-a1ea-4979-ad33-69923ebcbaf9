---
id: data-quality-api
title: Data Quality API Documentation
sidebar_label: Data Quality API
---

# Data Quality API Documentation

The Data Quality API provides endpoints for validating, monitoring, and ensuring data quality across the platform. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://data-quality.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Data Validation

#### Create Validation Rule
```http
POST /rules
```

**Request Body:**
```json
{
    "name": "customer_data_validation",
    "description": "Validate customer data quality",
    "dataset": "customers",
    "rules": [
        {
            "name": "email_format",
            "type": "regex",
            "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
            "column": "email",
            "severity": "error"
        },
        {
            "name": "age_range",
            "type": "range",
            "min": 18,
            "max": 120,
            "column": "age",
            "severity": "warning"
        },
        {
            "name": "required_fields",
            "type": "not_null",
            "columns": ["id", "name", "email"],
            "severity": "error"
        }
    ],
    "schedule": {
        "frequency": "daily",
        "time": "00:00:00Z"
    },
    "notifications": {
        "channels": ["slack", "email"],
        "recipients": ["<EMAIL>"]
    }
}
```

**Response:**
```json
{
    "rule_id": "rule_001",
    "name": "customer_data_validation",
    "status": "active",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### Run Validation
```http
POST /validations
```

**Request Body:**
```json
{
    "rule_id": "rule_001",
    "dataset": "customers",
    "timestamp": "2024-03-20T10:00:00Z",
    "options": {
        "sample_size": 1000,
        "parallel_processing": true
    }
}
```

**Response:**
```json
{
    "validation_id": "validation_001",
    "rule_id": "rule_001",
    "status": "running",
    "started_at": "2024-03-20T10:00:00Z"
}
```

#### Get Validation Results
```http
GET /validations/{validation_id}
```

**Response:**
```json
{
    "validation_id": "validation_001",
    "rule_id": "rule_001",
    "status": "completed",
    "started_at": "2024-03-20T10:00:00Z",
    "completed_at": "2024-03-20T10:05:00Z",
    "results": {
        "total_records": 1000,
        "valid_records": 980,
        "invalid_records": 20,
        "violations": [
            {
                "rule": "email_format",
                "severity": "error",
                "count": 15,
                "examples": [
                    {
                        "record_id": "123",
                        "value": "invalid-email",
                        "expected": "valid email format"
                    }
                ]
            },
            {
                "rule": "age_range",
                "severity": "warning",
                "count": 5,
                "examples": [
                    {
                        "record_id": "456",
                        "value": 150,
                        "expected": "age between 18 and 120"
                    }
                ]
            }
        ]
    }
}
```

### Data Profiling

#### Run Data Profile
```http
POST /profiles
```

**Request Body:**
```json
{
    "dataset": "customers",
    "columns": ["id", "name", "email", "age", "address"],
    "metrics": [
        "completeness",
        "uniqueness",
        "distribution",
        "statistics"
    ],
    "options": {
        "sample_size": 1000,
        "include_correlations": true
    }
}
```

**Response:**
```json
{
    "profile_id": "profile_001",
    "status": "running",
    "started_at": "2024-03-20T10:00:00Z"
}
```

#### Get Profile Results
```http
GET /profiles/{profile_id}
```

**Response:**
```json
{
    "profile_id": "profile_001",
    "status": "completed",
    "started_at": "2024-03-20T10:00:00Z",
    "completed_at": "2024-03-20T10:10:00Z",
    "results": {
        "columns": {
            "id": {
                "completeness": 1.0,
                "uniqueness": 1.0,
                "null_count": 0,
                "distinct_count": 1000
            },
            "email": {
                "completeness": 0.98,
                "uniqueness": 0.99,
                "null_count": 20,
                "distinct_count": 990,
                "format_validity": 0.95
            },
            "age": {
                "completeness": 0.99,
                "null_count": 10,
                "min": 18,
                "max": 85,
                "mean": 35.5,
                "std_dev": 12.3,
                "distribution": {
                    "bins": [18, 30, 40, 50, 60, 85],
                    "counts": [200, 300, 250, 150, 100]
                }
            }
        },
        "correlations": {
            "age_income": 0.75,
            "education_income": 0.65
        }
    }
}
```

### Data Quality Metrics

#### Get Quality Metrics
```http
GET /metrics/{dataset}
```

**Query Parameters:**
- `start_time` (optional): Start time for metrics
- `end_time` (optional): End time for metrics
- `metrics` (optional): Comma-separated list of metrics

**Response:**
```json
{
    "dataset": "customers",
    "metrics": [
        {
            "timestamp": "2024-03-20T10:00:00Z",
            "completeness": 0.98,
            "accuracy": 0.95,
            "consistency": 0.97,
            "timeliness": 0.99,
            "uniqueness": 0.96
        }
    ],
    "trends": {
        "completeness": {
            "trend": "stable",
            "change": 0.01
        }
    }
}
```

## Usage Examples

### Python Example
```python
from data_quality import Client

# Initialize client
client = Client(
    host="https://data-quality.91.life",
    auth_token="your-token"
)

# Create validation rule
rule = {
    "name": "customer_data_validation",
    "description": "Validate customer data quality",
    "dataset": "customers",
    "rules": [
        {
            "name": "email_format",
            "type": "regex",
            "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
            "column": "email",
            "severity": "error"
        }
    ]
}
rule_id = client.create_rule(rule)

# Run validation
validation = client.run_validation(
    rule_id=rule_id,
    dataset="customers"
)
results = client.get_validation_results(validation["validation_id"])
print(results)
```

### cURL Example
```bash
# Create validation rule
curl -X POST https://data-quality.91.life/api/v1/rules \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "customer_data_validation",
    "description": "Validate customer data quality",
    "dataset": "customers",
    "rules": [
      {
        "name": "email_format",
        "type": "regex",
        "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
        "column": "email",
        "severity": "error"
      }
    ]
  }'

# Run validation
curl -X POST https://data-quality.91.life/api/v1/validations \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "rule_id": "rule_001",
    "dataset": "customers",
    "timestamp": "2024-03-20T10:00:00Z"
  }'
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Validation Rules**
   - Define clear validation criteria
   - Use appropriate severity levels
   - Include helpful error messages

2. **Data Profiling**
   - Profile data regularly
   - Monitor data distributions
   - Track data quality trends

3. **Quality Metrics**
   - Set quality thresholds
   - Monitor metric trends
   - Set up alerts for issues

4. **Performance**
   - Use sampling for large datasets
   - Enable parallel processing
   - Cache profile results 