---
id: lakefs-api
title: LakeFS API Documentation
sidebar_label: LakeFS API
---

# LakeFS API Documentation

LakeFS provides a RESTful API for managing data versioning and branching in your data lake. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://lakefs.example.com/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Repository Management

#### Create Repository
```http
POST /repositories
```

**Request Body:**
```json
{
    "name": "my-repo",
    "storage_namespace": "s3://my-bucket/my-repo",
    "default_branch": "main"
}
```

**Response:**
```json
{
    "id": "my-repo",
    "creation_date": "2024-03-20T10:00:00Z",
    "default_branch": "main",
    "storage_namespace": "s3://my-bucket/my-repo"
}
```

#### List Repositories
```http
GET /repositories
```

**Query Parameters:**
- `prefix` (optional): Filter repositories by prefix
- `after` (optional): Pagination token
- `amount` (optional): Number of results per page

**Response:**
```json
{
    "results": [
        {
            "id": "my-repo",
            "creation_date": "2024-03-20T10:00:00Z",
            "default_branch": "main",
            "storage_namespace": "s3://my-bucket/my-repo"
        }
    ],
    "pagination": {
        "has_more": false,
        "next_offset": ""
    }
}
```

### Branch Management

#### Create Branch
```http
POST /repositories/{repository}/branches
```

**Request Body:**
```json
{
    "name": "feature-branch",
    "source": "main"
}
```

**Response:**
```json
{
    "id": "feature-branch",
    "commit_id": "abc123...",
    "creation_date": "2024-03-20T10:00:00Z"
}
```

#### List Branches
```http
GET /repositories/{repository}/branches
```

**Query Parameters:**
- `prefix` (optional): Filter branches by prefix
- `after` (optional): Pagination token
- `amount` (optional): Number of results per page

**Response:**
```json
{
    "results": [
        {
            "id": "main",
            "commit_id": "abc123...",
            "creation_date": "2024-03-20T10:00:00Z"
        }
    ],
    "pagination": {
        "has_more": false,
        "next_offset": ""
    }
}
```

### Object Operations

#### Upload Object
```http
PUT /repositories/{repository}/branches/{branch}/objects
```

**Headers:**
- `Content-Type`: application/octet-stream
- `X-Lakefs-Content-Type`: (optional) MIME type of the object

**Request Body:**
Binary content of the object

**Response:**
```json
{
    "path": "path/to/object",
    "size_bytes": 1024,
    "checksum": "abc123..."
}
```

#### Get Object
```http
GET /repositories/{repository}/branches/{branch}/objects
```

**Query Parameters:**
- `path`: Path to the object

**Response:**
Binary content of the object

### Commit Management

#### Create Commit
```http
POST /repositories/{repository}/branches/{branch}/commits
```

**Request Body:**
```json
{
    "message": "Commit message",
    "metadata": {
        "key": "value"
    }
}
```

**Response:**
```json
{
    "id": "abc123...",
    "parents": ["def456..."],
    "committer": "<EMAIL>",
    "message": "Commit message",
    "creation_date": "2024-03-20T10:00:00Z",
    "meta_range_id": "ghi789...",
    "metadata": {
        "key": "value"
    }
}
```

#### List Commits
```http
GET /repositories/{repository}/branches/{branch}/commits
```

**Query Parameters:**
- `after` (optional): Pagination token
- `amount` (optional): Number of results per page

**Response:**
```json
{
    "results": [
        {
            "id": "abc123...",
            "parents": ["def456..."],
            "committer": "<EMAIL>",
            "message": "Commit message",
            "creation_date": "2024-03-20T10:00:00Z",
            "meta_range_id": "ghi789...",
            "metadata": {
                "key": "value"
            }
        }
    ],
    "pagination": {
        "has_more": false,
        "next_offset": ""
    }
}
```

## Usage Examples

### Python Example
```python
import requests

def create_repository(name, storage_namespace, token):
    url = "https://lakefs.example.com/api/v1/repositories"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "name": name,
        "storage_namespace": storage_namespace,
        "default_branch": "main"
    }
    response = requests.post(url, headers=headers, json=data)
    return response.json()

def upload_object(repo, branch, path, content, token):
    url = f"https://lakefs.example.com/api/v1/repositories/{repo}/branches/{branch}/objects"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/octet-stream"
    }
    response = requests.put(url, headers=headers, data=content)
    return response.json()
```

### cURL Example
```bash
# Create repository
curl -X POST https://lakefs.example.com/api/v1/repositories \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-repo",
    "storage_namespace": "s3://my-bucket/my-repo",
    "default_branch": "main"
  }'

# Upload object
curl -X PUT https://lakefs.example.com/api/v1/repositories/my-repo/branches/main/objects \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/octet-stream" \
  --data-binary @file.txt
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Error Handling**
   - Implement retry logic with exponential backoff
   - Handle rate limiting appropriately
   - Check response status codes

2. **Performance**
   - Use appropriate chunk sizes for large uploads
   - Implement parallel uploads for multiple objects
   - Cache frequently accessed objects

3. **Security**
   - Rotate authentication tokens regularly
   - Use HTTPS for all API calls
   - Implement proper access controls

4. **Monitoring**
   - Monitor API response times
   - Track error rates
   - Set up alerts for unusual patterns 