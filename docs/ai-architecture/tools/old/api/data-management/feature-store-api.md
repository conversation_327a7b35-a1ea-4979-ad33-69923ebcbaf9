---
id: feature-store-api
title: Feature Store API Documentation
sidebar_label: Feature Store API
---

# Feature Store API Documentation

The Feature Store provides a RESTful API for managing and serving ML features. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://feature-store.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Feature Management

#### Create Feature
```http
POST /features
```

**Request Body:**
```json
{
    "name": "user_click_count",
    "description": "Number of clicks per user",
    "type": "INT64",
    "entity": "user",
    "data_source": {
        "type": "batch",
        "path": "s3://my-bucket/features/user_clicks.parquet",
        "format": "parquet"
    },
    "tags": {
        "team": "recommendation",
        "importance": "high"
    }
}
```

**Response:**
```json
{
    "feature_id": "user_click_count",
    "name": "user_click_count",
    "description": "Number of clicks per user",
    "type": "INT64",
    "entity": "user",
    "created_at": "2024-03-20T10:00:00Z",
    "status": "active"
}
```

#### List Features
```http
GET /features
```

**Query Parameters:**
- `entity` (optional): Filter by entity
- `tag` (optional): Filter by tag
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "features": [
        {
            "feature_id": "user_click_count",
            "name": "user_click_count",
            "description": "Number of clicks per user",
            "type": "INT64",
            "entity": "user",
            "created_at": "2024-03-20T10:00:00Z",
            "status": "active"
        }
    ],
    "next_page_token": "abc123..."
}
```

### Feature Set Management

#### Create Feature Set
```http
POST /feature-sets
```

**Request Body:**
```json
{
    "name": "user_features",
    "description": "User-related features",
    "features": [
        "user_click_count",
        "user_purchase_amount",
        "user_last_active"
    ],
    "entity": "user",
    "tags": {
        "team": "recommendation",
        "version": "v1"
    }
}
```

**Response:**
```json
{
    "feature_set_id": "user_features_v1",
    "name": "user_features",
    "description": "User-related features",
    "features": [
        "user_click_count",
        "user_purchase_amount",
        "user_last_active"
    ],
    "entity": "user",
    "created_at": "2024-03-20T10:00:00Z",
    "status": "active"
}
```

#### List Feature Sets
```http
GET /feature-sets
```

**Query Parameters:**
- `entity` (optional): Filter by entity
- `tag` (optional): Filter by tag
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "feature_sets": [
        {
            "feature_set_id": "user_features_v1",
            "name": "user_features",
            "description": "User-related features",
            "features": [
                "user_click_count",
                "user_purchase_amount",
                "user_last_active"
            ],
            "entity": "user",
            "created_at": "2024-03-20T10:00:00Z",
            "status": "active"
        }
    ],
    "next_page_token": "abc123..."
}
```

### Feature Serving

#### Get Feature Values
```http
POST /feature-sets/{feature_set_id}/values
```

**Request Body:**
```json
{
    "entity_keys": ["user1", "user2", "user3"],
    "feature_names": ["user_click_count", "user_purchase_amount"],
    "timestamp": "2024-03-20T10:00:00Z"
}
```

**Response:**
```json
{
    "values": [
        {
            "entity_key": "user1",
            "features": {
                "user_click_count": 42,
                "user_purchase_amount": 150.50
            }
        },
        {
            "entity_key": "user2",
            "features": {
                "user_click_count": 15,
                "user_purchase_amount": 75.25
            }
        },
        {
            "entity_key": "user3",
            "features": {
                "user_click_count": 28,
                "user_purchase_amount": 200.00
            }
        }
    ],
    "metadata": {
        "timestamp": "2024-03-20T10:00:00Z",
        "feature_set_id": "user_features_v1"
    }
}
```

## Usage Examples

### Python Example
```python
from feature_store import Client

# Initialize client
client = Client(
    host="https://feature-store.91.life",
    auth_token="your-token"
)

# Create feature
feature = {
    "name": "user_click_count",
    "description": "Number of clicks per user",
    "type": "INT64",
    "entity": "user",
    "data_source": {
        "type": "batch",
        "path": "s3://my-bucket/features/user_clicks.parquet",
        "format": "parquet"
    }
}
feature_id = client.create_feature(feature)

# Get feature values
values = client.get_feature_values(
    feature_set_id="user_features_v1",
    entity_keys=["user1", "user2", "user3"],
    feature_names=["user_click_count", "user_purchase_amount"]
)
print(values)
```

### cURL Example
```bash
# Create feature
curl -X POST https://feature-store.91.life/api/v1/features \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "user_click_count",
    "description": "Number of clicks per user",
    "type": "INT64",
    "entity": "user",
    "data_source": {
      "type": "batch",
      "path": "s3://my-bucket/features/user_clicks.parquet",
      "format": "parquet"
    }
  }'

# Get feature values
curl -X POST https://feature-store.91.life/api/v1/feature-sets/user_features_v1/values \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "entity_keys": ["user1", "user2", "user3"],
    "feature_names": ["user_click_count", "user_purchase_amount"],
    "timestamp": "2024-03-20T10:00:00Z"
  }'
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Feature Management**
   - Use meaningful feature names
   - Document feature definitions
   - Version features appropriately

2. **Feature Sets**
   - Group related features
   - Use consistent naming
   - Document feature sets

3. **Performance**
   - Batch feature requests
   - Use appropriate caching
   - Monitor latency

4. **Security**
   - Implement proper access controls
   - Monitor feature usage
   - Audit feature changes 