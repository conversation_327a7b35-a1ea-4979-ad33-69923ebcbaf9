---
id: openmetadata-api
title: OpenMetadata API Documentation
sidebar_label: OpenMetadata API
---

# OpenMetadata API Documentation

OpenMetadata provides a RESTful API for managing metadata and data lineage. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://openmetadata.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Entity Management

#### Create Entity
```http
POST /entities
```

**Request Body:**
```json
{
    "name": "customer_table",
    "description": "Customer information table",
    "type": "table",
    "service": "hive",
    "database": "sales",
    "schema": "public",
    "columns": [
        {
            "name": "customer_id",
            "type": "INT64",
            "description": "Unique customer identifier",
            "tags": ["PII", "primary_key"]
        },
        {
            "name": "email",
            "type": "STRING",
            "description": "Customer email address",
            "tags": ["PII", "contact"]
        }
    ],
    "tags": {
        "domain": "customer",
        "tier": "tier1"
    }
}
```

**Response:**
```json
{
    "id": "customer_table",
    "name": "customer_table",
    "description": "Customer information table",
    "type": "table",
    "service": "hive",
    "database": "sales",
    "schema": "public",
    "created_at": "2024-03-20T10:00:00Z",
    "updated_at": "2024-03-20T10:00:00Z",
    "version": 1
}
```

#### List Entities
```http
GET /entities
```

**Query Parameters:**
- `type` (optional): Filter by entity type
- `service` (optional): Filter by service
- `tag` (optional): Filter by tag
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "entities": [
        {
            "id": "customer_table",
            "name": "customer_table",
            "description": "Customer information table",
            "type": "table",
            "service": "hive",
            "database": "sales",
            "schema": "public",
            "created_at": "2024-03-20T10:00:00Z",
            "updated_at": "2024-03-20T10:00:00Z",
            "version": 1
        }
    ],
    "next_page_token": "abc123..."
}
```

### Lineage Management

#### Create Lineage
```http
POST /lineage
```

**Request Body:**
```json
{
    "source": {
        "id": "customer_table",
        "type": "table"
    },
    "target": {
        "id": "customer_features",
        "type": "feature"
    },
    "description": "Customer features derived from customer table",
    "type": "derived",
    "tags": {
        "pipeline": "feature_engineering",
        "version": "v1"
    }
}
```

**Response:**
```json
{
    "id": "lineage_001",
    "source": {
        "id": "customer_table",
        "type": "table"
    },
    "target": {
        "id": "customer_features",
        "type": "feature"
    },
    "description": "Customer features derived from customer table",
    "type": "derived",
    "created_at": "2024-03-20T10:00:00Z",
    "version": 1
}
```

#### Get Lineage
```http
GET /lineage/{entity_id}
```

**Query Parameters:**
- `depth` (optional): Lineage depth to retrieve
- `direction` (optional): "upstream" or "downstream"

**Response:**
```json
{
    "entity": {
        "id": "customer_features",
        "type": "feature"
    },
    "lineage": {
        "upstream": [
            {
                "id": "customer_table",
                "type": "table",
                "relationship": "derived"
            }
        ],
        "downstream": [
            {
                "id": "customer_model",
                "type": "model",
                "relationship": "used_by"
            }
        ]
    }
}
```

### Tag Management

#### Create Tag
```http
POST /tags
```

**Request Body:**
```json
{
    "name": "PII",
    "description": "Personally Identifiable Information",
    "category": "data_classification",
    "color": "#FF0000"
}
```

**Response:**
```json
{
    "id": "PII",
    "name": "PII",
    "description": "Personally Identifiable Information",
    "category": "data_classification",
    "color": "#FF0000",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### List Tags
```http
GET /tags
```

**Query Parameters:**
- `category` (optional): Filter by category
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "tags": [
        {
            "id": "PII",
            "name": "PII",
            "description": "Personally Identifiable Information",
            "category": "data_classification",
            "color": "#FF0000",
            "created_at": "2024-03-20T10:00:00Z"
        }
    ],
    "next_page_token": "abc123..."
}
```

## Usage Examples

### Python Example
```python
from openmetadata import Client

# Initialize client
client = Client(
    host="https://openmetadata.91.life",
    auth_token="your-token"
)

# Create entity
entity = {
    "name": "customer_table",
    "description": "Customer information table",
    "type": "table",
    "service": "hive",
    "database": "sales",
    "schema": "public",
    "columns": [
        {
            "name": "customer_id",
            "type": "INT64",
            "description": "Unique customer identifier",
            "tags": ["PII", "primary_key"]
        }
    ]
}
entity_id = client.create_entity(entity)

# Get lineage
lineage = client.get_lineage(
    entity_id="customer_features",
    depth=2,
    direction="upstream"
)
print(lineage)
```

### cURL Example
```bash
# Create entity
curl -X POST https://openmetadata.91.life/api/v1/entities \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "customer_table",
    "description": "Customer information table",
    "type": "table",
    "service": "hive",
    "database": "sales",
    "schema": "public",
    "columns": [
      {
        "name": "customer_id",
        "type": "INT64",
        "description": "Unique customer identifier",
        "tags": ["PII", "primary_key"]
      }
    ]
  }'

# Get lineage
curl -X GET "https://openmetadata.91.life/api/v1/lineage/customer_features?depth=2&direction=upstream" \
  -H "Authorization: Bearer ${TOKEN}"
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Entity Management**
   - Use consistent naming conventions
   - Provide detailed descriptions
   - Tag entities appropriately

2. **Lineage Tracking**
   - Document all data dependencies
   - Keep lineage up to date
   - Use appropriate relationship types

3. **Tag Management**
   - Use standardized tag categories
   - Document tag meanings
   - Apply tags consistently

4. **Security**
   - Implement proper access controls
   - Monitor metadata changes
   - Audit tag usage 