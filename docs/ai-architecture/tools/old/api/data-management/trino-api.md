---
id: trino-api
title: Trino API Documentation
sidebar_label: Trino API
---

# Trino API Documentation

Trino provides a RESTful API for executing SQL queries and managing query execution. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://trino.example.com/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Query Execution

#### Submit Query
```http
POST /statement
```

**Headers:**
- `X-Trino-User`: Username
- `X-Trino-Schema`: Default schema
- `X-Trino-Catalog`: Default catalog
- `X-Trino-Time-Zone`: Time zone (e.g., "UTC")

**Request Body:**
```sql
SELECT * FROM my_table WHERE date >= DATE '2024-03-20'
```

**Response:**
```json
{
    "id": "20240320_100000_00000_abcde",
    "infoUri": "https://trino.example.com/ui/query.html?20240320_100000_00000_abcde",
    "nextUri": "https://trino.example.com/v1/statement/20240320_100000_00000_abcde/1",
    "stats": {
        "state": "RUNNING",
        "queued": false,
        "scheduled": true,
        "nodes": 1,
        "totalSplits": 10,
        "queuedSplits": 0,
        "runningSplits": 5,
        "completedSplits": 5,
        "cpuTimeMillis": 1000,
        "wallTimeMillis": 2000,
        "queuedTimeMillis": 0,
        "elapsedTimeMillis": 2000,
        "processedRows": 1000,
        "processedBytes": 10000,
        "peakMemoryBytes": 1000000
    }
}
```

#### Get Query Results
```http
GET /statement/{query-id}/{token}
```

**Response:**
```json
{
    "id": "20240320_100000_00000_abcde",
    "infoUri": "https://trino.example.com/ui/query.html?20240320_100000_00000_abcde",
    "nextUri": "https://trino.example.com/v1/statement/20240320_100000_00000_abcde/2",
    "data": [
        ["2024-03-20", 100, "value1"],
        ["2024-03-20", 200, "value2"]
    ],
    "columns": [
        {
            "name": "date",
            "type": "date",
            "typeSignature": {
                "rawType": "date",
                "arguments": []
            }
        },
        {
            "name": "count",
            "type": "bigint",
            "typeSignature": {
                "rawType": "bigint",
                "arguments": []
            }
        },
        {
            "name": "value",
            "type": "varchar",
            "typeSignature": {
                "rawType": "varchar",
                "arguments": []
            }
        }
    ],
    "stats": {
        "state": "RUNNING",
        "queued": false,
        "scheduled": true,
        "nodes": 1,
        "totalSplits": 10,
        "queuedSplits": 0,
        "runningSplits": 5,
        "completedSplits": 5,
        "cpuTimeMillis": 1000,
        "wallTimeMillis": 2000,
        "queuedTimeMillis": 0,
        "elapsedTimeMillis": 2000,
        "processedRows": 1000,
        "processedBytes": 10000,
        "peakMemoryBytes": 1000000
    }
}
```

#### Cancel Query
```http
DELETE /statement/{query-id}
```

**Response:**
```json
{
    "id": "20240320_100000_00000_abcde",
    "infoUri": "https://trino.example.com/ui/query.html?20240320_100000_00000_abcde",
    "stats": {
        "state": "CANCELED",
        "queued": false,
        "scheduled": false,
        "nodes": 1,
        "totalSplits": 10,
        "queuedSplits": 0,
        "runningSplits": 0,
        "completedSplits": 5,
        "cpuTimeMillis": 1000,
        "wallTimeMillis": 2000,
        "queuedTimeMillis": 0,
        "elapsedTimeMillis": 2000,
        "processedRows": 1000,
        "processedBytes": 10000,
        "peakMemoryBytes": 1000000
    }
}
```

### Query Management

#### List Queries
```http
GET /query
```

**Query Parameters:**
- `state` (optional): Filter by query state
- `user` (optional): Filter by user
- `limit` (optional): Maximum number of queries to return

**Response:**
```json
{
    "queries": [
        {
            "queryId": "20240320_100000_00000_abcde",
            "session": {
                "user": "<EMAIL>",
                "schema": "default",
                "catalog": "hive",
                "timeZone": "UTC"
            },
            "state": "RUNNING",
            "memoryPool": "general",
            "scheduled": true,
            "self": "https://trino.example.com/v1/query/20240320_100000_00000_abcde",
            "query": "SELECT * FROM my_table WHERE date >= DATE '2024-03-20'",
            "queryStats": {
                "createTime": "2024-03-20T10:00:00Z",
                "endTime": null,
                "elapsedTimeMillis": 2000,
                "queuedTimeMillis": 0,
                "totalDrivers": 10,
                "queuedDrivers": 0,
                "runningDrivers": 5,
                "completedDrivers": 5,
                "blockedDrivers": 0,
                "cumulativeUserMemory": 1000000,
                "userMemoryReservation": 1000000,
                "peakUserMemoryReservation": 1000000,
                "totalCpuTime": 1000,
                "totalScheduledTime": 2000,
                "peakTotalMemory": 1000000,
                "totalAllocation": 1000000
            }
        }
    ]
}
```

## Usage Examples

### Python Example
```python
import trino
import pandas as pd

# Initialize client
conn = trino.dbapi.connect(
    host="trino.example.com",
    port=443,
    user="<EMAIL>",
    catalog="hive",
    schema="default",
    auth=trino.auth.OAuth2Authentication("your-token")
)

# Execute query
cursor = conn.cursor()
cursor.execute("""
    SELECT date, count, value
    FROM my_table
    WHERE date >= DATE '2024-03-20'
    LIMIT 1000
""")

# Fetch results
rows = cursor.fetchall()
columns = [desc[0] for desc in cursor.description]
df = pd.DataFrame(rows, columns=columns)
print(df)
```

### cURL Example
```bash
# Submit query
curl -X POST https://trino.example.com/v1/statement \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "X-Trino-User: <EMAIL>" \
  -H "X-Trino-Schema: default" \
  -H "X-Trino-Catalog: hive" \
  -H "X-Trino-Time-Zone: UTC" \
  -d "SELECT * FROM my_table WHERE date >= DATE '2024-03-20'"

# Get query results
curl -X GET https://trino.example.com/v1/statement/20240320_100000_00000_abcde/1 \
  -H "Authorization: Bearer ${TOKEN}"

# Cancel query
curl -X DELETE https://trino.example.com/v1/statement/20240320_100000_00000_abcde \
  -H "Authorization: Bearer ${TOKEN}"
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid SQL query |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Query doesn't exist |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Query Optimization**
   - Use appropriate filters
   - Limit result set size
   - Use proper indexing

2. **Resource Management**
   - Monitor query execution
   - Cancel long-running queries
   - Use appropriate timeouts

3. **Performance**
   - Use prepared statements
   - Implement proper error handling
   - Use connection pooling

4. **Security**
   - Use parameterized queries
   - Implement proper access controls
   - Monitor query patterns 