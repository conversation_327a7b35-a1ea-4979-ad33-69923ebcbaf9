---
id: minio-api
title: MinIO API Documentation
sidebar_label: MinIO API
---

# MinIO API Documentation

MinIO provides an S3-compatible API for object storage operations. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://minio.example.com
```

## Authentication

All API requests require authentication using AWS Signature Version 4. Include the following headers:

```bash
Authorization: AWS4-HMAC-SHA256 Credential=<access-key>/<date>/<region>/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=<signature>
x-amz-date: <timestamp>
```

## API Endpoints

### Bucket Operations

#### Create Bucket
```http
PUT /{bucket-name}
```

**Headers:**
- `x-amz-acl` (optional): Bucket access control list
- `x-amz-bucket-object-lock-enabled` (optional): Enable object lock

**Response:**
```json
{
    "bucket": "my-bucket",
    "created": true,
    "location": "us-east-1"
}
```

#### List Buckets
```http
GET /
```

**Response:**
```json
{
    "buckets": [
        {
            "name": "my-bucket",
            "creation_date": "2024-03-20T10:00:00Z"
        }
    ],
    "owner": {
        "id": "minio",
        "display_name": "minio"
    }
}
```

### Object Operations

#### Upload Object
```http
PUT /{bucket-name}/{object-name}
```

**Headers:**
- `Content-Type`: MIME type of the object
- `x-amz-meta-*`: Custom metadata
- `x-amz-storage-class`: Storage class (STANDARD, REDUCED_REDUNDANCY)

**Request Body:**
Binary content of the object

**Response:**
```json
{
    "etag": "\"d41d8cd98f00b204e9800998ecf8427e\"",
    "version_id": "null"
}
```

#### Get Object
```http
GET /{bucket-name}/{object-name}
```

**Query Parameters:**
- `versionId` (optional): Object version ID
- `response-content-type` (optional): Override response content type
- `response-content-disposition` (optional): Override response content disposition

**Response:**
Binary content of the object

#### List Objects
```http
GET /{bucket-name}
```

**Query Parameters:**
- `prefix` (optional): Filter objects by prefix
- `delimiter` (optional): Character used to group keys
- `max-keys` (optional): Maximum number of keys to return
- `marker` (optional): Key to start listing from

**Response:**
```json
{
    "name": "my-bucket",
    "prefix": "",
    "marker": "",
    "max_keys": 1000,
    "is_truncated": false,
    "contents": [
        {
            "key": "example.txt",
            "last_modified": "2024-03-20T10:00:00Z",
            "etag": "\"d41d8cd98f00b204e9800998ecf8427e\"",
            "size": 1024,
            "storage_class": "STANDARD",
            "owner": {
                "id": "minio",
                "display_name": "minio"
            }
        }
    ]
}
```

### Multipart Upload

#### Initiate Multipart Upload
```http
POST /{bucket-name}/{object-name}?uploads
```

**Headers:**
- `Content-Type`: MIME type of the object
- `x-amz-meta-*`: Custom metadata

**Response:**
```json
{
    "bucket": "my-bucket",
    "key": "large-file.zip",
    "upload_id": "abc123..."
}
```

#### Upload Part
```http
PUT /{bucket-name}/{object-name}?partNumber={part-number}&uploadId={upload-id}
```

**Request Body:**
Binary content of the part

**Response:**
```json
{
    "etag": "\"d41d8cd98f00b204e9800998ecf8427e\""
}
```

#### Complete Multipart Upload
```http
POST /{bucket-name}/{object-name}?uploadId={upload-id}
```

**Request Body:**
```json
{
    "parts": [
        {
            "part_number": 1,
            "etag": "\"d41d8cd98f00b204e9800998ecf8427e\""
        }
    ]
}
```

**Response:**
```json
{
    "location": "https://minio.example.com/my-bucket/large-file.zip",
    "bucket": "my-bucket",
    "key": "large-file.zip",
    "etag": "\"d41d8cd98f00b204e9800998ecf8427e\""
}
```

## Usage Examples

### Python Example
```python
from minio import Minio
from minio.error import S3Error

# Initialize client
client = Minio(
    "minio.example.com",
    access_key="your-access-key",
    secret_key="your-secret-key",
    secure=True
)

# Create bucket
client.make_bucket("my-bucket")

# Upload object
client.put_object(
    "my-bucket",
    "example.txt",
    data="Hello, World!",
    length=13,
    content_type="text/plain"
)

# List objects
objects = client.list_objects("my-bucket", prefix="example")
for obj in objects:
    print(f"Object: {obj.object_name}, Size: {obj.size}")

# Download object
data = client.get_object("my-bucket", "example.txt")
print(data.read().decode())
```

### cURL Example
```bash
# Create bucket
curl -X PUT https://minio.example.com/my-bucket \
  -H "Authorization: AWS4-HMAC-SHA256 Credential=${ACCESS_KEY}/20240320/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=${SIGNATURE}" \
  -H "x-amz-date: 20240320T100000Z"

# Upload object
curl -X PUT https://minio.example.com/my-bucket/example.txt \
  -H "Authorization: AWS4-HMAC-SHA256 Credential=${ACCESS_KEY}/20240320/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=${SIGNATURE}" \
  -H "x-amz-date: 20240320T100000Z" \
  -H "Content-Type: text/plain" \
  -d "Hello, World!"

# List objects
curl -X GET https://minio.example.com/my-bucket \
  -H "Authorization: AWS4-HMAC-SHA256 Credential=${ACCESS_KEY}/20240320/us-east-1/s3/aws4_request, SignedHeaders=host;x-amz-date, Signature=${SIGNATURE}" \
  -H "x-amz-date: 20240320T100000Z"
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Bucket Management**
   - Use meaningful bucket names
   - Configure appropriate access policies
   - Enable versioning when needed

2. **Object Operations**
   - Use appropriate content types
   - Set proper metadata
   - Implement proper error handling

3. **Performance**
   - Use multipart upload for large files
   - Implement proper retry logic
   - Use appropriate chunk sizes

4. **Security**
   - Rotate access keys regularly
   - Use HTTPS for all operations
   - Implement proper access controls 