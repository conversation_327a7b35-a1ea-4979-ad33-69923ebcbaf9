---
id: kserve-api
title: KServe API Documentation
sidebar_label: KServe API
---

# KServe API Documentation

KServe provides a standardized API for model serving and inference in Kubernetes. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://kserve.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Model Serving

#### Deploy Model
```http
POST /models
```

**Request Body:**
```json
{
    "name": "my-model",
    "version": "v1",
    "model_format": "pytorch",
    "storage_uri": "s3://my-bucket/models/model.pt",
    "resources": {
        "cpu": "2",
        "memory": "4Gi",
        "gpu": "1"
    },
    "scaling": {
        "min_replicas": 1,
        "max_replicas": 3,
        "target_concurrency": 10
    },
    "config": {
        "batch_size": 32,
        "max_batch_delay": "100ms"
    }
}
```

**Response:**
```json
{
    "model_id": "my-model-v1",
    "name": "my-model",
    "version": "v1",
    "status": "DEPLOYING",
    "endpoint": "https://kserve.91.life/models/model-001",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### List Models
```http
GET /models
```

**Query Parameters:**
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination
- `status` (optional): Filter by status

**Response:**
```json
{
    "models": [
        {
            "model_id": "my-model-v1",
            "name": "my-model",
            "version": "v1",
            "status": "RUNNING",
            "endpoint": "https://kserve.91.life/models/model-001",
            "created_at": "2024-03-20T10:00:00Z"
        }
    ],
    "next_page_token": "abc123..."
}
```

### Inference

#### Model Inference
```http
POST /models/{model_id}/infer
```

**Request Body:**
```json
{
    "inputs": [
        {
            "name": "input-0",
            "shape": [1, 3, 224, 224],
            "datatype": "FP32",
            "data": [0.1, 0.2, 0.3, ...]
        }
    ],
    "parameters": {
        "batch_size": 1
    }
}
```

**Response:**
```json
{
    "id": "inference-001",
    "model_name": "my-model",
    "model_version": "v1",
    "outputs": [
        {
            "name": "output-0",
            "shape": [1, 1000],
            "datatype": "FP32",
            "data": [0.1, 0.2, 0.3, ...]
        }
    ],
    "parameters": {
        "batch_size": 1
    }
}
```

### Model Management

#### Update Model
```http
PATCH /models/{model_id}
```

**Request Body:**
```json
{
    "scaling": {
        "min_replicas": 2,
        "max_replicas": 5
    },
    "config": {
        "batch_size": 64
    }
}
```

**Response:**
```json
{
    "model_id": "my-model-v1",
    "name": "my-model",
    "version": "v1",
    "status": "UPDATING",
    "endpoint": "https://kserve.91.life/models/model-001",
    "updated_at": "2024-03-20T11:00:00Z"
}
```

#### Delete Model
```http
DELETE /models/{model_id}
```

**Response:**
```json
{
    "model_id": "my-model-v1",
    "status": "DELETING",
    "deleted_at": "2024-03-20T12:00:00Z"
}
```

## Usage Examples

### Python Example
```python
from kserve import Client
import numpy as np

# Initialize client
client = Client(host="https://kserve.91.life")

# Deploy model
model = {
    "name": "my-model",
    "version": "v1",
    "model_format": "pytorch",
    "storage_uri": "s3://my-bucket/models/model.pt",
    "resources": {
        "cpu": "2",
        "memory": "4Gi",
        "gpu": "1"
    }
}
model_id = client.deploy_model(model)

# Make inference
input_data = np.random.rand(1, 3, 224, 224).astype(np.float32)
response = client.infer(
    model_id=model_id,
    inputs=[{
        "name": "input-0",
        "shape": input_data.shape,
        "datatype": "FP32",
        "data": input_data.tolist()
    }]
)
print(f"Prediction: {response.outputs[0].data}")
```

### cURL Example
```bash
# Deploy model
curl -X POST https://kserve.91.life/api/v1/models \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-model",
    "version": "v1",
    "model_format": "pytorch",
    "storage_uri": "s3://my-bucket/models/model.pt",
    "resources": {
      "cpu": "2",
      "memory": "4Gi",
      "gpu": "1"
    }
  }'

# Make inference
curl -X POST https://kserve.91.life/api/v1/models/model-001/predict \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": [
      {
        "name": "input-0",
        "shape": [1, 3, 224, 224],
        "datatype": "FP32",
        "data": [0.1, 0.2, 0.3, ...]
      }
    ]
  }'
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Model Deployment**
   - Version your models appropriately
   - Set appropriate resource limits
   - Configure proper scaling parameters

2. **Inference**
   - Use appropriate batch sizes
   - Implement proper error handling
   - Monitor inference latency

3. **Performance**
   - Optimize model serving configuration
   - Use appropriate hardware resources
   - Implement caching when possible

4. **Monitoring**
   - Monitor model performance
   - Track resource usage
   - Set up alerts for failures 