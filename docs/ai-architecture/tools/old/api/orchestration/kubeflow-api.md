---
id: kubeflow-api
title: Kubeflow API Documentation
sidebar_label: Kubeflow API
---

# Kubeflow API Documentation

Kubeflow provides a comprehensive API for managing ML pipelines, notebooks, and model serving in Kubernetes. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://kubeflow.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Pipeline Management

#### Create Pipeline
```http
POST /pipelines
```

**Request Body:**
```json
{
    "name": "training-pipeline",
    "description": "End-to-end model training pipeline",
    "pipeline_spec": {
        "pipeline_id": "training-pipeline-v1",
        "pipeline_manifest": "base64_encoded_pipeline_yaml",
        "parameters": [
            {
                "name": "learning_rate",
                "value": "0.001"
            },
            {
                "name": "batch_size",
                "value": "32"
            }
        ]
    }
}
```

**Response:**
```json
{
    "pipeline_id": "training-pipeline-v1",
    "name": "training-pipeline",
    "description": "End-to-end model training pipeline",
    "created_at": "2024-03-20T10:00:00Z",
    "status": "active"
}
```

#### List Pipelines
```http
GET /pipelines
```

**Query Parameters:**
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination
- `sort_by` (optional): Field to sort by
- `filter` (optional): Filter expression

**Response:**
```json
{
    "pipelines": [
        {
            "pipeline_id": "training-pipeline-v1",
            "name": "training-pipeline",
            "description": "End-to-end model training pipeline",
            "created_at": "2024-03-20T10:00:00Z",
            "status": "active"
        }
    ],
    "next_page_token": "abc123..."
}
```

### Pipeline Run Management

#### Create Pipeline Run
```http
POST /pipelines/{pipeline_id}/runs
```

**Request Body:**
```json
{
    "name": "training-run-001",
    "parameters": {
        "learning_rate": "0.001",
        "batch_size": "32",
        "epochs": "10"
    },
    "service_account": "pipeline-runner",
    "resources": {
        "cpu": "2",
        "memory": "4Gi",
        "gpu": "1"
    }
}
```

**Response:**
```json
{
    "run_id": "run-001",
    "pipeline_id": "training-pipeline-v1",
    "name": "training-run-001",
    "status": "PENDING",
    "created_at": "2024-03-20T10:00:00Z",
    "parameters": {
        "learning_rate": "0.001",
        "batch_size": "32",
        "epochs": "10"
    }
}
```

#### Get Pipeline Run Status
```http
GET /pipelines/{pipeline_id}/runs/{run_id}
```

**Response:**
```json
{
    "run_id": "run-001",
    "pipeline_id": "training-pipeline-v1",
    "name": "training-run-001",
    "status": "RUNNING",
    "created_at": "2024-03-20T10:00:00Z",
    "started_at": "2024-03-20T10:01:00Z",
    "finished_at": null,
    "parameters": {
        "learning_rate": "0.001",
        "batch_size": "32",
        "epochs": "10"
    },
    "metrics": {
        "accuracy": 0.95,
        "loss": 0.05
    }
}
```

### Notebook Management

#### Create Notebook Server
```http
POST /notebooks
```

**Request Body:**
```json
{
    "name": "data-science-notebook",
    "image": "jupyter/tensorflow-notebook:latest",
    "resources": {
        "cpu": "2",
        "memory": "4Gi",
        "gpu": "1"
    },
    "volumes": [
        {
            "name": "data-volume",
            "mount_path": "/home/<USER>/data",
            "size": "10Gi"
        }
    ],
    "environment_variables": {
        "MLFLOW_TRACKING_URI": "https://mlflow.91.life"
    }
}
```

**Response:**
```json
{
    "notebook_id": "notebook-001",
    "name": "data-science-notebook",
    "status": "PENDING",
    "url": "https://notebook.91.life/notebook-001",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### List Notebook Servers
```http
GET /notebooks
```

**Query Parameters:**
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "notebooks": [
        {
            "notebook_id": "notebook-001",
            "name": "data-science-notebook",
            "status": "RUNNING",
            "url": "https://notebook.91.life/notebook-001",
            "created_at": "2024-03-20T10:00:00Z"
        }
    ],
    "next_page_token": "abc123..."
}
```

## Usage Examples

### Python Example
```python
from kubeflow import Client
from kubeflow.pipeline import Pipeline, PipelineRun

# Initialize client
client = Client(host="https://kubeflow.91.life")

# Create pipeline
pipeline = Pipeline(
    name="training-pipeline",
    description="End-to-end model training pipeline",
    pipeline_spec={
        "pipeline_id": "training-pipeline-v1",
        "pipeline_manifest": pipeline_yaml,
        "parameters": [
            {"name": "learning_rate", "value": "0.001"},
            {"name": "batch_size", "value": "32"}
        ]
    }
)
pipeline_id = client.create_pipeline(pipeline)

# Create pipeline run
run = PipelineRun(
    name="training-run-001",
    pipeline_id=pipeline_id,
    parameters={
        "learning_rate": "0.001",
        "batch_size": "32",
        "epochs": "10"
    }
)
run_id = client.create_pipeline_run(run)

# Monitor run status
status = client.get_pipeline_run_status(pipeline_id, run_id)
print(f"Run status: {status}")
```

### cURL Example
```bash
# Create pipeline
curl -X POST https://kubeflow.91.life/api/v1/pipelines \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "training-pipeline",
    "description": "End-to-end model training pipeline",
    "pipeline_spec": {
      "pipeline_id": "training-pipeline-v1",
      "pipeline_manifest": "base64_encoded_pipeline_yaml",
      "parameters": [
        {"name": "learning_rate", "value": "0.001"},
        {"name": "batch_size", "value": "32"}
      ]
    }
  }'

# Create pipeline run
curl -X POST https://kubeflow.91.life/api/v1/pipelines/training-pipeline-v1/runs \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "training-run-001",
    "parameters": {
      "learning_rate": "0.001",
      "batch_size": "32",
      "epochs": "10"
    }
  }'
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Pipeline Management**
   - Version your pipeline definitions
   - Use meaningful names and descriptions
   - Document pipeline parameters

2. **Resource Management**
   - Set appropriate resource limits
   - Monitor resource usage
   - Clean up unused resources

3. **Security**
   - Use service accounts with minimal permissions
   - Implement proper access controls
   - Secure sensitive parameters

4. **Monitoring**
   - Monitor pipeline run status
   - Track resource usage
   - Set up alerts for failures 