---
id: mlflow-api
title: MLflow API Documentation
sidebar_label: MLflow API
---

# MLflow API Documentation

MLflow provides a RESTful API for experiment tracking, model registry, and model serving. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://mlflow.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Experiment Management

#### Create Experiment
```http
POST /experiments
```

**Request Body:**
```json
{
    "name": "my-experiment",
    "artifact_location": "s3://my-bucket/experiments/my-experiment",
    "tags": {
        "project": "ml-pipeline",
        "team": "data-science"
    }
}
```

**Response:**
```json
{
    "experiment_id": "123",
    "name": "my-experiment",
    "artifact_location": "s3://my-bucket/experiments/my-experiment",
    "lifecycle_stage": "active",
    "tags": {
        "project": "ml-pipeline",
        "team": "data-science"
    }
}
```

#### List Experiments
```http
GET /experiments
```

**Query Parameters:**
- `view_type` (optional): "ACTIVE_ONLY", "DELETED_ONLY", or "ALL"
- `max_results` (optional): Maximum number of experiments to return
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "experiments": [
        {
            "experiment_id": "123",
            "name": "my-experiment",
            "artifact_location": "s3://my-bucket/experiments/my-experiment",
            "lifecycle_stage": "active",
            "tags": {
                "project": "ml-pipeline",
                "team": "data-science"
            }
        }
    ],
    "next_page_token": "abc123..."
}
```

### Run Management

#### Create Run
```http
POST /runs
```

**Request Body:**
```json
{
    "experiment_id": "123",
    "start_time": "2024-03-20T10:00:00Z",
    "tags": {
        "mlflow.user": "<EMAIL>",
        "mlflow.source.name": "train.py"
    }
}
```

**Response:**
```json
{
    "run": {
        "info": {
            "run_id": "abc123...",
            "experiment_id": "123",
            "status": "RUNNING",
            "start_time": "2024-03-20T10:00:00Z",
            "end_time": null,
            "lifecycle_stage": "active"
        },
        "data": {
            "metrics": {},
            "params": {},
            "tags": {
                "mlflow.user": "<EMAIL>",
                "mlflow.source.name": "train.py"
            }
        }
    }
}
```

#### Log Metric
```http
POST /runs/{run_id}/metrics
```

**Request Body:**
```json
{
    "key": "accuracy",
    "value": 0.95,
    "timestamp": "2024-03-20T10:00:00Z",
    "step": 1
}
```

#### Log Parameter
```http
POST /runs/{run_id}/params
```

**Request Body:**
```json
{
    "key": "learning_rate",
    "value": "0.001"
}
```

### Model Registry

#### Register Model
```http
POST /registered-models
```

**Request Body:**
```json
{
    "name": "my-model",
    "description": "My ML model",
    "tags": {
        "framework": "pytorch",
        "version": "1.0"
    }
}
```

**Response:**
```json
{
    "registered_model": {
        "name": "my-model",
        "creation_timestamp": "2024-03-20T10:00:00Z",
        "last_updated_timestamp": "2024-03-20T10:00:00Z",
        "description": "My ML model",
        "latest_versions": [],
        "tags": {
            "framework": "pytorch",
            "version": "1.0"
        }
    }
}
```

#### Create Model Version
```http
POST /model-versions
```

**Request Body:**
```json
{
    "name": "my-model",
    "source": "s3://my-bucket/models/model.pkl",
    "run_id": "abc123...",
    "tags": {
        "stage": "production"
    }
}
```

**Response:**
```json
{
    "model_version": {
        "name": "my-model",
        "version": "1",
        "creation_timestamp": "2024-03-20T10:00:00Z",
        "last_updated_timestamp": "2024-03-20T10:00:00Z",
        "current_stage": "None",
        "description": "",
        "source": "s3://my-bucket/models/model.pkl",
        "run_id": "abc123...",
        "status": "READY",
        "tags": {
            "stage": "production"
        }
    }
}
```

## Usage Examples

### Python Example
```python
import mlflow
import mlflow.sklearn
from sklearn.ensemble import RandomForestClassifier

# Set tracking URI
mlflow.set_tracking_uri("https://mlflow.91.life")

# Start run
with mlflow.start_run(experiment_id="123") as run:
    # Train model
    model = RandomForestClassifier()
    model.fit(X_train, y_train)
    
    # Log parameters
    mlflow.log_param("n_estimators", 100)
    mlflow.log_param("max_depth", 10)
    
    # Log metrics
    mlflow.log_metric("accuracy", 0.95)
    mlflow.log_metric("f1_score", 0.92)
    
    # Log model
    mlflow.sklearn.log_model(model, "model")
    
    # Register model
    mlflow.register_model(
        f"runs:/{run.info.run_id}/model",
        "my-model"
    )
```

### cURL Example
```bash
# Create experiment
curl -X POST https://mlflow.91.life/api/v1/experiments \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-experiment",
    "artifact_location": "s3://my-bucket/experiments/my-experiment"
  }'

# Log metric
curl -X POST https://mlflow.91.life/api/v1/runs/abc123/metrics \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "key": "accuracy",
    "value": 0.95,
    "timestamp": "2024-03-20T10:00:00Z"
  }'
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Experiment Organization**
   - Use meaningful experiment names
   - Tag experiments with relevant metadata
   - Organize experiments by project/team

2. **Run Management**
   - Log all relevant parameters and metrics
   - Use consistent naming conventions
   - Include run descriptions

3. **Model Registry**
   - Version models appropriately
   - Document model changes
   - Use stage transitions (Staging, Production)

4. **Performance**
   - Batch metric logging when possible
   - Use appropriate artifact storage
   - Implement proper error handling 