---
id: model-serving-api
title: Model Serving API Documentation
sidebar_label: Model Serving API
---

# Model Serving API Documentation

The Model Serving API provides endpoints for serving machine learning models, managing inference requests, and monitoring model performance. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://model-serving.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Model Inference

#### Batch Inference
```http
POST /models/{model_id}/batch
```

**Request Body:**
```json
{
    "instances": [
        {
            "customer_id": "123",
            "age": 35,
            "income": 75000,
            "tenure": 24,
            "usage_frequency": 0.8
        },
        {
            "customer_id": "124",
            "age": 42,
            "income": 85000,
            "tenure": 36,
            "usage_frequency": 0.6
        }
    ],
    "parameters": {
        "timeout": 30,
        "batch_size": 32
    }
}
```

**Response:**
```json
{
    "predictions": [
        {
            "customer_id": "123",
            "churn_probability": 0.15,
            "prediction": "no_churn",
            "confidence": 0.85
        },
        {
            "customer_id": "124",
            "churn_probability": 0.75,
            "prediction": "churn",
            "confidence": 0.75
        }
    ],
    "model_id": "model_001",
    "model_version": "1.0.0",
    "inference_time_ms": 150
}
```

#### Real-time Inference
```http
POST /models/{model_id}/predict
```

**Request Body:**
```json
{
    "instance": {
        "customer_id": "123",
        "age": 35,
        "income": 75000,
        "tenure": 24,
        "usage_frequency": 0.8
    },
    "parameters": {
        "timeout": 5
    }
}
```

**Response:**
```json
{
    "prediction": {
        "customer_id": "123",
        "churn_probability": 0.15,
        "prediction": "no_churn",
        "confidence": 0.85
    },
    "model_id": "model_001",
    "model_version": "1.0.0",
    "inference_time_ms": 50
}
```

### Model Status

#### Get Model Status
```http
GET /models/{model_id}/status
```

**Response:**
```json
{
    "model_id": "model_001",
    "status": "serving",
    "version": "1.0.0",
    "endpoint": "https://api.91.life/v1/predict",
    "metrics": {
        "requests_per_second": 100,
        "latency_ms": {
            "p50": 50,
            "p90": 100,
            "p99": 200
        },
        "error_rate": 0.01,
        "cpu_usage": 45.5,
        "memory_usage": 60.2
    },
    "last_updated": "2024-03-20T10:00:00Z"
}
```

#### Get Model Health
```http
GET /models/{model_id}/health
```

**Response:**
```json
{
    "status": "healthy",
    "checks": {
        "model_loaded": true,
        "endpoint_available": true,
        "resources_available": true
    },
    "last_checked": "2024-03-20T10:00:00Z"
}
```

### Model Configuration

#### Update Model Configuration
```http
PATCH /models/{model_id}/config
```

**Request Body:**
```json
{
    "batch_size": 64,
    "timeout": 30,
    "max_retries": 3,
    "scaling": {
        "min_replicas": 2,
        "max_replicas": 10,
        "target_cpu_utilization": 80
    },
    "resources": {
        "cpu": "2",
        "memory": "4Gi"
    }
}
```

**Response:**
```json
{
    "model_id": "model_001",
    "status": "updating",
    "config": {
        "batch_size": 64,
        "timeout": 30,
        "max_retries": 3,
        "scaling": {
            "min_replicas": 2,
            "max_replicas": 10,
            "target_cpu_utilization": 80
        },
        "resources": {
            "cpu": "2",
            "memory": "4Gi"
        }
    },
    "updated_at": "2024-03-20T10:00:00Z"
}
```

## Usage Examples

### Python Example
```python
from model_serving import Client

# Initialize client
client = Client(
    host="https://model-serving.91.life",
    auth_token="your-token"
)

# Batch inference
predictions = client.batch_predict(
    model_id="model_001",
    instances=[
        {
            "customer_id": "123",
            "age": 35,
            "income": 75000,
            "tenure": 24,
            "usage_frequency": 0.8
        }
    ]
)
print(predictions)

# Get model status
status = client.get_model_status("model_001")
print(status)
```

### cURL Example
```bash
# Batch inference
curl -X POST https://model-serving.91.life/api/v1/models/model_001/batch \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "instances": [
      {
        "customer_id": "123",
        "age": 35,
        "income": 75000,
        "tenure": 24,
        "usage_frequency": 0.8
      }
    ]
  }'

# Get model status
curl -X GET https://model-serving.91.life/api/v1/models/model_001/status \
  -H "Authorization: Bearer ${TOKEN}"
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 408 | Request Timeout - Inference timeout |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |
| 503 | Service Unavailable - Model not ready |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Inference Requests**
   - Use batch inference for multiple predictions
   - Set appropriate timeouts
   - Handle errors gracefully

2. **Performance**
   - Monitor latency metrics
   - Configure appropriate resources
   - Use auto-scaling

3. **Monitoring**
   - Track model performance
   - Monitor resource usage
   - Set up alerts

4. **Security**
   - Validate input data
   - Rate limit requests
   - Secure endpoints 