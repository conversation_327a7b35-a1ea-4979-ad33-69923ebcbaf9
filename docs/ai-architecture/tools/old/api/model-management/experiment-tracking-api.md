---
id: experiment-tracking-api
title: Experiment Tracking API Documentation
sidebar_label: Experiment Tracking API
---

# Experiment Tracking API Documentation

The Experiment Tracking API provides endpoints for managing machine learning experiments, tracking metrics, and comparing model performance. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://experiment-tracking.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Experiments

#### Create Experiment
```http
POST /experiments
```

**Request Body:**
```json
{
    "name": "customer_churn_prediction",
    "description": "Predict customer churn using various features",
    "tags": ["churn", "classification", "customer"],
    "metrics": {
        "primary": "f1_score",
        "secondary": ["accuracy", "precision", "recall"]
    },
    "parameters": {
        "model_type": "xgboost",
        "max_depth": 6,
        "learning_rate": 0.1
    }
}
```

**Response:**
```json
{
    "experiment_id": "exp_001",
    "name": "customer_churn_prediction",
    "status": "active",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### List Experiments
```http
GET /experiments
```

**Query Parameters:**
- `tags` (optional): Filter by tags
- `status` (optional): Filter by status
- `start_time` (optional): Start time for experiments
- `end_time` (optional): End time for experiments
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "experiments": [
        {
            "experiment_id": "exp_001",
            "name": "customer_churn_prediction",
            "description": "Predict customer churn using various features",
            "tags": ["churn", "classification", "customer"],
            "status": "active",
            "created_at": "2024-03-20T10:00:00Z",
            "last_updated": "2024-03-20T10:30:00Z"
        }
    ],
    "next_page_token": "abc123..."
}
```

### Runs

#### Create Run
```http
POST /experiments/{experiment_id}/runs
```

**Request Body:**
```json
{
    "name": "run_001",
    "parameters": {
        "model_type": "xgboost",
        "max_depth": 6,
        "learning_rate": 0.1,
        "n_estimators": 100
    },
    "metrics": {
        "accuracy": 0.95,
        "precision": 0.92,
        "recall": 0.94,
        "f1_score": 0.93
    },
    "artifacts": [
        {
            "name": "model",
            "type": "model",
            "uri": "s3://models/churn/xgboost/v1"
        },
        {
            "name": "feature_importance",
            "type": "plot",
            "uri": "s3://plots/churn/feature_importance.png"
        }
    ]
}
```

**Response:**
```json
{
    "run_id": "run_001",
    "experiment_id": "exp_001",
    "status": "completed",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### Get Run Details
```http
GET /experiments/{experiment_id}/runs/{run_id}
```

**Response:**
```json
{
    "run_id": "run_001",
    "experiment_id": "exp_001",
    "name": "run_001",
    "status": "completed",
    "parameters": {
        "model_type": "xgboost",
        "max_depth": 6,
        "learning_rate": 0.1,
        "n_estimators": 100
    },
    "metrics": {
        "accuracy": 0.95,
        "precision": 0.92,
        "recall": 0.94,
        "f1_score": 0.93
    },
    "artifacts": [
        {
            "name": "model",
            "type": "model",
            "uri": "s3://models/churn/xgboost/v1"
        },
        {
            "name": "feature_importance",
            "type": "plot",
            "uri": "s3://plots/churn/feature_importance.png"
        }
    ],
    "created_at": "2024-03-20T10:00:00Z",
    "completed_at": "2024-03-20T10:30:00Z"
}
```

### Metrics

#### Log Metrics
```http
POST /experiments/{experiment_id}/runs/{run_id}/metrics
```

**Request Body:**
```json
{
    "metrics": {
        "accuracy": 0.95,
        "precision": 0.92,
        "recall": 0.94,
        "f1_score": 0.93
    },
    "step": 100,
    "timestamp": "2024-03-20T10:30:00Z"
}
```

**Response:**
```json
{
    "status": "recorded",
    "timestamp": "2024-03-20T10:30:00Z"
}
```

#### Get Metrics History
```http
GET /experiments/{experiment_id}/runs/{run_id}/metrics
```

**Query Parameters:**
- `metric_names` (optional): Comma-separated list of metric names
- `start_step` (optional): Start step for metrics
- `end_step` (optional): End step for metrics

**Response:**
```json
{
    "metrics": {
        "accuracy": [
            {
                "step": 100,
                "value": 0.95,
                "timestamp": "2024-03-20T10:30:00Z"
            }
        ],
        "f1_score": [
            {
                "step": 100,
                "value": 0.93,
                "timestamp": "2024-03-20T10:30:00Z"
            }
        ]
    }
}
```

## Usage Examples

### Python Example
```python
from experiment_tracking import Client

# Initialize client
client = Client(
    host="https://experiment-tracking.91.life",
    auth_token="your-token"
)

# Create experiment
experiment = {
    "name": "customer_churn_prediction",
    "description": "Predict customer churn using various features",
    "tags": ["churn", "classification", "customer"]
}
experiment_id = client.create_experiment(experiment)

# Create run
run = {
    "name": "run_001",
    "parameters": {
        "model_type": "xgboost",
        "max_depth": 6,
        "learning_rate": 0.1
    },
    "metrics": {
        "accuracy": 0.95,
        "f1_score": 0.93
    }
}
run_id = client.create_run(experiment_id, run)

# Log metrics
client.log_metrics(
    experiment_id=experiment_id,
    run_id=run_id,
    metrics={
        "accuracy": 0.96,
        "f1_score": 0.94
    },
    step=200
)
```

### cURL Example
```bash
# Create experiment
curl -X POST https://experiment-tracking.91.life/api/v1/experiments \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "customer_churn_prediction",
    "description": "Predict customer churn using various features",
    "tags": ["churn", "classification", "customer"]
  }'

# Create run
curl -X POST https://experiment-tracking.91.life/api/v1/experiments/exp_001/runs \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "run_001",
    "parameters": {
      "model_type": "xgboost",
      "max_depth": 6,
      "learning_rate": 0.1
    },
    "metrics": {
      "accuracy": 0.95,
      "f1_score": 0.93
    }
  }'
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Experiment Organization**
   - Use meaningful experiment names
   - Add descriptive tags
   - Document experiment purpose

2. **Run Management**
   - Log all relevant parameters
   - Track key metrics
   - Save important artifacts

3. **Metrics Tracking**
   - Log metrics regularly
   - Use consistent metric names
   - Include timestamps

4. **Performance**
   - Batch metric logging
   - Use appropriate step intervals
   - Clean up old experiments 