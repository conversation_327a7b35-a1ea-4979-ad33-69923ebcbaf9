---
id: model-registry-api
title: Model Registry API Documentation
sidebar_label: Model Registry API
---

# Model Registry API Documentation

The Model Registry API provides endpoints for managing model versions, tracking model lineage, and managing model deployments. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://model-registry.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Models

#### Register Model
```http
POST /models
```

**Request Body:**
```json
{
    "name": "customer_churn_predictor",
    "description": "XGBoost model for customer churn prediction",
    "type": "classification",
    "framework": "xgboost",
    "version": "1.0.0",
    "artifacts": {
        "model": "s3://models/churn/xgboost/v1/model.pkl",
        "config": "s3://models/churn/xgboost/v1/config.json",
        "requirements": "s3://models/churn/xgboost/v1/requirements.txt"
    },
    "metrics": {
        "accuracy": 0.95,
        "precision": 0.92,
        "recall": 0.94,
        "f1_score": 0.93
    },
    "parameters": {
        "max_depth": 6,
        "learning_rate": 0.1,
        "n_estimators": 100
    },
    "tags": ["churn", "classification", "customer"],
    "experiment_id": "exp_001",
    "run_id": "run_001"
}
```

**Response:**
```json
{
    "model_id": "model_001",
    "name": "customer_churn_predictor",
    "version": "1.0.0",
    "status": "registered",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### List Models
```http
GET /models
```

**Query Parameters:**
- `name` (optional): Filter by model name
- `type` (optional): Filter by model type
- `framework` (optional): Filter by framework
- `tags` (optional): Filter by tags
- `status` (optional): Filter by status
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "models": [
        {
            "model_id": "model_001",
            "name": "customer_churn_predictor",
            "description": "XGBoost model for customer churn prediction",
            "type": "classification",
            "framework": "xgboost",
            "version": "1.0.0",
            "status": "registered",
            "created_at": "2024-03-20T10:00:00Z",
            "last_updated": "2024-03-20T10:30:00Z"
        }
    ],
    "next_page_token": "abc123..."
}
```

### Model Versions

#### Create Model Version
```http
POST /models/{model_id}/versions
```

**Request Body:**
```json
{
    "version": "1.1.0",
    "description": "Updated model with new features",
    "artifacts": {
        "model": "s3://models/churn/xgboost/v1.1.0/model.pkl",
        "config": "s3://models/churn/xgboost/v1.1.0/config.json",
        "requirements": "s3://models/churn/xgboost/v1.1.0/requirements.txt"
    },
    "metrics": {
        "accuracy": 0.96,
        "precision": 0.93,
        "recall": 0.95,
        "f1_score": 0.94
    },
    "parameters": {
        "max_depth": 8,
        "learning_rate": 0.05,
        "n_estimators": 200
    },
    "experiment_id": "exp_002",
    "run_id": "run_002"
}
```

**Response:**
```json
{
    "version_id": "version_001",
    "model_id": "model_001",
    "version": "1.1.0",
    "status": "registered",
    "created_at": "2024-03-20T11:00:00Z"
}
```

#### List Model Versions
```http
GET /models/{model_id}/versions
```

**Query Parameters:**
- `status` (optional): Filter by status
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "versions": [
        {
            "version_id": "version_001",
            "model_id": "model_001",
            "version": "1.1.0",
            "description": "Updated model with new features",
            "status": "registered",
            "created_at": "2024-03-20T11:00:00Z",
            "metrics": {
                "accuracy": 0.96,
                "precision": 0.93,
                "recall": 0.95,
                "f1_score": 0.94
            }
        }
    ],
    "next_page_token": "abc123..."
}
```

### Model Deployments

#### Create Deployment
```http
POST /models/{model_id}/versions/{version_id}/deployments
```

**Request Body:**
```json
{
    "name": "production",
    "environment": "prod",
    "config": {
        "replicas": 3,
        "resources": {
            "cpu": "2",
            "memory": "4Gi"
        },
        "scaling": {
            "min_replicas": 2,
            "max_replicas": 10,
            "target_cpu_utilization": 80
        }
    },
    "endpoint": {
        "type": "rest",
        "path": "/v1/predict"
    }
}
```

**Response:**
```json
{
    "deployment_id": "deployment_001",
    "model_id": "model_001",
    "version_id": "version_001",
    "name": "production",
    "status": "deploying",
    "created_at": "2024-03-20T12:00:00Z"
}
```

#### Get Deployment Status
```http
GET /models/{model_id}/versions/{version_id}/deployments/{deployment_id}
```

**Response:**
```json
{
    "deployment_id": "deployment_001",
    "model_id": "model_001",
    "version_id": "version_001",
    "name": "production",
    "status": "running",
    "endpoint": "https://api.91.life/v1/predict",
    "metrics": {
        "replicas": 3,
        "cpu_usage": 45.5,
        "memory_usage": 60.2,
        "request_rate": 100,
        "latency_ms": 150
    },
    "created_at": "2024-03-20T12:00:00Z",
    "last_updated": "2024-03-20T12:05:00Z"
}
```

## Usage Examples

### Python Example
```python
from model_registry import Client

# Initialize client
client = Client(
    host="https://model-registry.91.life",
    auth_token="your-token"
)

# Register model
model = {
    "name": "customer_churn_predictor",
    "description": "XGBoost model for customer churn prediction",
    "type": "classification",
    "framework": "xgboost",
    "version": "1.0.0",
    "artifacts": {
        "model": "s3://models/churn/xgboost/v1/model.pkl"
    }
}
model_id = client.register_model(model)

# Create deployment
deployment = {
    "name": "production",
    "environment": "prod",
    "config": {
        "replicas": 3,
        "resources": {
            "cpu": "2",
            "memory": "4Gi"
        }
    }
}
deployment_id = client.create_deployment(
    model_id=model_id,
    version_id="version_001",
    deployment=deployment
)
```

### cURL Example
```bash
# Register model
curl -X POST https://model-registry.91.life/api/v1/models \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "customer_churn_predictor",
    "description": "XGBoost model for customer churn prediction",
    "type": "classification",
    "framework": "xgboost",
    "version": "1.0.0",
    "artifacts": {
      "model": "s3://models/churn/xgboost/v1/model.pkl"
    }
  }'

# Create deployment
curl -X POST https://model-registry.91.life/api/v1/models/model_001/versions/version_001/deployments \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "production",
    "environment": "prod",
    "config": {
      "replicas": 3,
      "resources": {
        "cpu": "2",
        "memory": "4Gi"
      }
    }
  }'
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Model Registration**
   - Use semantic versioning
   - Include comprehensive metadata
   - Document model changes

2. **Version Management**
   - Track model lineage
   - Maintain version history
   - Document version differences

3. **Deployment Management**
   - Use appropriate resources
   - Configure scaling policies
   - Monitor deployment health

4. **Security**
   - Secure model artifacts
   - Control access to models
   - Audit model usage 