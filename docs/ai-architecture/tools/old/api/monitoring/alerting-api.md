---
id: alerting-api
title: Alerting API Documentation
sidebar_label: Alerting API
---

# Alerting API Documentation

The Alerting API provides endpoints for managing alerts, notifications, and incident response. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://alerting.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Alert Rules

#### Create Alert Rule
```http
POST /rules
```

**Request Body:**
```json
{
    "name": "high_error_rate",
    "description": "Alert when error rate exceeds threshold",
    "condition": {
        "metric": "error_rate",
        "operator": ">",
        "threshold": 0.05,
        "duration": "5m"
    },
    "severity": "critical",
    "labels": {
        "team": "ml-platform",
        "component": "model-serving"
    },
    "annotations": {
        "summary": "High error rate detected",
        "description": "Error rate has exceeded 5% for 5 minutes"
    },
    "notifications": {
        "channels": ["slack", "email"],
        "recipients": ["<EMAIL>"],
        "cooldown": "15m"
    }
}
```

**Response:**
```json
{
    "rule_id": "rule_001",
    "name": "high_error_rate",
    "status": "active",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### List Alert Rules
```http
GET /rules
```

**Query Parameters:**
- `severity` (optional): Filter by severity
- `status` (optional): Filter by status
- `label` (optional): Filter by label
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "rules": [
        {
            "rule_id": "rule_001",
            "name": "high_error_rate",
            "description": "Alert when error rate exceeds threshold",
            "condition": {
                "metric": "error_rate",
                "operator": ">",
                "threshold": 0.05,
                "duration": "5m"
            },
            "severity": "critical",
            "status": "active",
            "created_at": "2024-03-20T10:00:00Z"
        }
    ],
    "next_page_token": "abc123..."
}
```

### Alerts

#### List Active Alerts
```http
GET /alerts
```

**Query Parameters:**
- `severity` (optional): Filter by severity
- `rule_id` (optional): Filter by rule
- `status` (optional): Filter by status
- `start_time` (optional): Start time for alerts
- `end_time` (optional): End time for alerts

**Response:**
```json
{
    "alerts": [
        {
            "alert_id": "alert_001",
            "rule_id": "rule_001",
            "name": "high_error_rate",
            "severity": "critical",
            "status": "firing",
            "started_at": "2024-03-20T10:00:00Z",
            "last_updated": "2024-03-20T10:05:00Z",
            "value": 0.08,
            "threshold": 0.05,
            "labels": {
                "team": "ml-platform",
                "component": "model-serving"
            }
        }
    ]
}
```

#### Update Alert Status
```http
PATCH /alerts/{alert_id}
```

**Request Body:**
```json
{
    "status": "acknowledged",
    "comment": "Investigating the issue",
    "assigned_to": "<EMAIL>"
}
```

**Response:**
```json
{
    "alert_id": "alert_001",
    "status": "acknowledged",
    "updated_at": "2024-03-20T10:10:00Z"
}
```

### Notification Channels

#### Create Notification Channel
```http
POST /channels
```

**Request Body:**
```json
{
    "name": "ml-team-slack",
    "type": "slack",
    "config": {
        "webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz",
        "channel": "#ml-alerts",
        "username": "ML Platform Bot"
    },
    "severity_levels": ["critical", "warning"],
    "cooldown": "15m"
}
```

**Response:**
```json
{
    "channel_id": "channel_001",
    "name": "ml-team-slack",
    "type": "slack",
    "status": "active",
    "created_at": "2024-03-20T10:00:00Z"
}
```

#### List Notification Channels
```http
GET /channels
```

**Query Parameters:**
- `type` (optional): Filter by channel type
- `status` (optional): Filter by status
- `page_size` (optional): Number of results per page
- `page_token` (optional): Token for pagination

**Response:**
```json
{
    "channels": [
        {
            "channel_id": "channel_001",
            "name": "ml-team-slack",
            "type": "slack",
            "status": "active",
            "created_at": "2024-03-20T10:00:00Z"
        }
    ],
    "next_page_token": "abc123..."
}
```

## Usage Examples

### Python Example
```python
from alerting import Client

# Initialize client
client = Client(
    host="https://alerting.91.life",
    auth_token="your-token"
)

# Create alert rule
rule = {
    "name": "high_error_rate",
    "description": "Alert when error rate exceeds threshold",
    "condition": {
        "metric": "error_rate",
        "operator": ">",
        "threshold": 0.05,
        "duration": "5m"
    },
    "severity": "critical",
    "notifications": {
        "channels": ["slack"],
        "recipients": ["<EMAIL>"]
    }
}
rule_id = client.create_rule(rule)

# List active alerts
alerts = client.list_alerts(
    severity="critical",
    status="firing"
)
print(alerts)
```

### cURL Example
```bash
# Create alert rule
curl -X POST https://alerting.91.life/api/v1/rules \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "high_error_rate",
    "description": "Alert when error rate exceeds threshold",
    "condition": {
      "metric": "error_rate",
      "operator": ">",
      "threshold": 0.05,
      "duration": "5m"
    },
    "severity": "critical",
    "notifications": {
      "channels": ["slack"],
      "recipients": ["<EMAIL>"]
    }
  }'

# List active alerts
curl -X GET "https://alerting.91.life/api/v1/alerts?severity=critical&status=firing" \
  -H "Authorization: Bearer ${TOKEN}"
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 409 | Conflict - Resource already exists |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Alert Rules**
   - Set appropriate thresholds
   - Use meaningful names and descriptions
   - Configure proper severity levels

2. **Notifications**
   - Use multiple notification channels
   - Configure cooldown periods
   - Set up escalation policies

3. **Alert Management**
   - Acknowledge alerts promptly
   - Document investigation steps
   - Update alert status regularly

4. **Monitoring**
   - Monitor alert effectiveness
   - Review and tune thresholds
   - Track alert response times 