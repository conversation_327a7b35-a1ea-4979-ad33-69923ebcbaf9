---
id: monitoring-api
title: Monitoring API Documentation
sidebar_label: Monitoring API
---

# Monitoring API Documentation

The Monitoring API provides endpoints for tracking model performance, data quality, and system health metrics. This documentation covers all available endpoints, authentication, and usage examples.

## Base URL

```
https://monitoring.91.life/api/v1
```

## Authentication

All API requests require authentication using OAuth2/OIDC. Include the following header:

```bash
Authorization: Bearer <your-token>
```

## API Endpoints

### Model Monitoring

#### Log Model Metrics
```http
POST /models/{model_id}/metrics
```

**Request Body:**
```json
{
    "timestamp": "2024-03-20T10:00:00Z",
    "metrics": {
        "accuracy": 0.95,
        "precision": 0.92,
        "recall": 0.94,
        "f1_score": 0.93,
        "latency_ms": 150,
        "throughput": 100
    },
    "metadata": {
        "environment": "production",
        "version": "v1.2.3",
        "batch_size": 32
    }
}
```

**Response:**
```json
{
    "metric_id": "metric_001",
    "model_id": "model_001",
    "timestamp": "2024-03-20T10:00:00Z",
    "status": "recorded"
}
```

#### Get Model Metrics
```http
GET /models/{model_id}/metrics
```

**Query Parameters:**
- `start_time` (optional): Start time for metrics range
- `end_time` (optional): End time for metrics range
- `metric_names` (optional): Comma-separated list of metric names
- `aggregation` (optional): Aggregation function (avg, min, max, sum)

**Response:**
```json
{
    "metrics": [
        {
            "timestamp": "2024-03-20T10:00:00Z",
            "accuracy": 0.95,
            "precision": 0.92,
            "recall": 0.94,
            "f1_score": 0.93,
            "latency_ms": 150,
            "throughput": 100
        }
    ],
    "aggregations": {
        "accuracy": {
            "avg": 0.95,
            "min": 0.94,
            "max": 0.96
        }
    }
}
```

### Data Quality Monitoring

#### Log Data Quality Metrics
```http
POST /datasets/{dataset_id}/quality
```

**Request Body:**
```json
{
    "timestamp": "2024-03-20T10:00:00Z",
    "metrics": {
        "completeness": 0.98,
        "accuracy": 0.95,
        "consistency": 0.97,
        "timeliness": 0.99,
        "uniqueness": 0.96
    },
    "schema_validation": {
        "valid": true,
        "errors": []
    },
    "data_validation": {
        "valid": true,
        "errors": []
    }
}
```

**Response:**
```json
{
    "quality_id": "quality_001",
    "dataset_id": "dataset_001",
    "timestamp": "2024-03-20T10:00:00Z",
    "status": "recorded"
}
```

#### Get Data Quality Metrics
```http
GET /datasets/{dataset_id}/quality
```

**Query Parameters:**
- `start_time` (optional): Start time for metrics range
- `end_time` (optional): End time for metrics range
- `metric_names` (optional): Comma-separated list of metric names

**Response:**
```json
{
    "metrics": [
        {
            "timestamp": "2024-03-20T10:00:00Z",
            "completeness": 0.98,
            "accuracy": 0.95,
            "consistency": 0.97,
            "timeliness": 0.99,
            "uniqueness": 0.96
        }
    ],
    "trends": {
        "completeness": {
            "trend": "stable",
            "change": 0.01
        }
    }
}
```

### System Health Monitoring

#### Log System Metrics
```http
POST /system/metrics
```

**Request Body:**
```json
{
    "timestamp": "2024-03-20T10:00:00Z",
    "component": "feature_store",
    "metrics": {
        "cpu_usage": 45.5,
        "memory_usage": 60.2,
        "disk_usage": 75.8,
        "request_rate": 100,
        "error_rate": 0.1,
        "latency_ms": 150
    },
    "status": "healthy"
}
```

**Response:**
```json
{
    "metric_id": "system_001",
    "component": "feature_store",
    "timestamp": "2024-03-20T10:00:00Z",
    "status": "recorded"
}
```

#### Get System Health
```http
GET /system/health
```

**Query Parameters:**
- `component` (optional): Filter by component
- `start_time` (optional): Start time for metrics range
- `end_time` (optional): End time for metrics range

**Response:**
```json
{
    "components": [
        {
            "name": "feature_store",
            "status": "healthy",
            "metrics": {
                "cpu_usage": 45.5,
                "memory_usage": 60.2,
                "disk_usage": 75.8,
                "request_rate": 100,
                "error_rate": 0.1,
                "latency_ms": 150
            },
            "last_updated": "2024-03-20T10:00:00Z"
        }
    ],
    "overall_status": "healthy"
}
```

## Usage Examples

### Python Example
```python
from monitoring import Client

# Initialize client
client = Client(
    host="https://monitoring.91.life",
    auth_token="your-token"
)

# Log model metrics
metrics = {
    "timestamp": "2024-03-20T10:00:00Z",
    "metrics": {
        "accuracy": 0.95,
        "precision": 0.92,
        "recall": 0.94,
        "f1_score": 0.93,
        "latency_ms": 150,
        "throughput": 100
    }
}
client.log_model_metrics("model_001", metrics)

# Get data quality metrics
quality_metrics = client.get_data_quality(
    dataset_id="dataset_001",
    start_time="2024-03-19T00:00:00Z",
    end_time="2024-03-20T00:00:00Z"
)
print(quality_metrics)
```

### cURL Example
```bash
# Log model metrics
curl -X POST https://monitoring.91.life/api/v1/models/model_001/metrics \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "timestamp": "2024-03-20T10:00:00Z",
    "metrics": {
      "accuracy": 0.95,
      "precision": 0.92,
      "recall": 0.94,
      "f1_score": 0.93,
      "latency_ms": 150,
      "throughput": 100
    }
  }'

# Get system health
curl -X GET "https://monitoring.91.life/api/v1/system/health?component=feature_store" \
  -H "Authorization: Bearer ${TOKEN}"
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Invalid or missing authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

- Standard rate limit: 100 requests per minute
- Burst rate limit: 200 requests per minute
- Rate limit headers:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time until rate limit resets

## Best Practices

1. **Model Monitoring**
   - Monitor key performance metrics
   - Set up alerts for anomalies
   - Track model drift

2. **Data Quality**
   - Monitor data completeness
   - Track data accuracy
   - Validate data consistency

3. **System Health**
   - Monitor resource usage
   - Track error rates
   - Set up health checks

4. **Alerting**
   - Configure appropriate thresholds
   - Set up notification channels
   - Implement escalation policies 