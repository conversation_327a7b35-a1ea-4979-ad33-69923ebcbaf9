.heroBanner {
  padding: 4rem 0;
  text-align: left;
  position: relative;
  overflow: hidden;
  background: linear-gradient(45deg, var(--ifm-color-primary) 0%, var(--ifm-color-primary-lightest) 100%);
}

.heroContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.heroText {
  flex: 1;
  max-width: 600px;
}

.heroImage {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.andraBot {
  max-width: 300px;
  height: auto;
  animation: float 6s ease-in-out infinite;
}

.buttons {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@media screen and (max-width: 996px) {
  .heroContent {
    flex-direction: column;
    text-align: center;
  }

  .heroText {
    max-width: 100%;
  }

  .buttons {
    justify-content: center;
  }

  .andra<PERSON>ot {
    max-width: 200px;
  }
} 