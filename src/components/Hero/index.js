import React from 'react';
import clsx from 'clsx';
import styles from './styles.module.css';

export default function Hero() {
  return (
    <header className={clsx('hero hero--primary', styles.heroBanner)}>
      <div className="container">
        <div className={styles.heroContent}>
          <div className={styles.heroText}>
            <h1 className="hero__title">Welcome to 91.life AI Platform</h1>
            <p className="hero__subtitle">
              Empowering healthcare with intelligent AI solutions
            </p>
            <div className={styles.buttons}>
              <a
                className="button button--secondary button--lg"
                href="/intro"
              >
                Get Started
              </a>
              <a
                className="button button--primary button--lg"
                href="https://91.life"
                target="_blank"
                rel="noopener noreferrer"
              >
                Visit 91.life
              </a>
            </div>
          </div>
          <div className={styles.heroImage}>
            <img
              src="/img/logos/andra-bot.png"
              alt="<PERSON><PERSON> Bot - AI Assistant"
              className={styles.andraBot}
            />
          </div>
        </div>
      </div>
    </header>
  );
} 