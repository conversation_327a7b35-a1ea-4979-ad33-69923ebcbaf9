/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #2563eb;
  --ifm-color-primary-dark: #1d4ed8;
  --ifm-color-primary-darker: #1e40af;
  --ifm-color-primary-darkest: #1e3a8a;
  --ifm-color-primary-light: #3b82f6;
  --ifm-color-primary-lighter: #60a5fa;
  --ifm-color-primary-lightest: #93c5fd;
  --ifm-code-font-size: 95%;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #60a5fa;
  --ifm-color-primary-dark: #3b82f6;
  --ifm-color-primary-darker: #2563eb;
  --ifm-color-primary-darkest: #1d4ed8;
  --ifm-color-primary-light: #93c5fd;
  --ifm-color-primary-lighter: #bfdbfe;
  --ifm-color-primary-lightest: #dbeafe;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

/* Custom styles */
.hero {
  background: linear-gradient(45deg, var(--ifm-color-primary) 0%, var(--ifm-color-primary-lightest) 100%);
  color: white;
  padding: 4rem 0;
}

.hero__title {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.hero__subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

/* Card styles */
.card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-4px);
}

/* Code block styles */
pre {
  border-radius: 8px;
  margin: 1rem 0;
}

code {
  border-radius: 4px;
  padding: 0.2rem 0.4rem;
}

/* Table styles */
table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin: 1rem 0;
}

th, td {
  padding: 0.75rem;
  border: 1px solid var(--ifm-color-emphasis-200);
}

th {
  background-color: var(--ifm-color-emphasis-100);
  font-weight: 600;
}

/* Navigation styles */
.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar__link {
  font-weight: 500;
}

/* Footer styles */
.footer {
  background-color: var(--ifm-color-emphasis-100);
  padding: 2rem 0;
}

.footer__link-item {
  color: var(--ifm-color-emphasis-700);
}

/* Sidebar styles */
.menu {
  padding: 1rem;
}

.menu__link {
  border-radius: 4px;
  transition: background-color 0.2s ease-in-out;
}

.menu__link:hover {
  background-color: var(--ifm-color-emphasis-100);
}

/* Search styles */
.searchBox {
  border-radius: 8px;
  border: 1px solid var(--ifm-color-emphasis-200);
}

/* Mermaid diagram styles */
.mermaid {
  background-color: var(--ifm-background-color);
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

/* Additional custom variables */
--ifm-font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
--ifm-heading-font-weight: 600;
--ifm-navbar-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

/* Responsive design */
@media (max-width: 996px) {
  .navbar__items {
    font-size: 0.85rem;
  }
  
  .main-wrapper {
    padding: 1rem;
  }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  letter-spacing: -0.02em;
  margin-top: 2rem;
  margin-bottom: 1rem;
}
