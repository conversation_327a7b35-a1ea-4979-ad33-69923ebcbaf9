/** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
const sidebars = {
  "AI-Architecture": [
    {
      type: 'category',
      label: 'AI-Architecture',
      items: [
        {
          type: 'doc',
          id: 'ai-architecture/intro',
          label: 'Introduction',
        },
        {
          type: 'doc',
          id: 'ai-architecture/architecture-overview/index',
          label: 'Architecture Overview',
        },
        {
          type: 'doc',
          id: 'ai-architecture/architecture-requirements/index',
          label: 'R&D Platform Architecture Requirements',
        },
        {
          type: 'doc',
          id: 'ai-architecture/support/index',
          label: 'Support',
        },
        {
          type: 'doc',
          id: 'ai-architecture/system-requirements/index',
          label: 'System Requirements',
        },
        {
          type: 'category',
          label: 'Best Practices',
          items: [
            'ai-architecture/best-practices/development/index',
            'ai-architecture/best-practices/monitoring/index',
            'ai-architecture/best-practices/testing/index',
          ],
        },
        {
          type: 'category',
          label: 'Components',
          items: [
            'ai-architecture/components/ai-models/index',
            'ai-architecture/components/api-gateway/index',
            'ai-architecture/components/data-pipeline/index',
            'ai-architecture/components/monitoring/index',
          ],
        },
        {
          type: 'category',
          label: 'Deployment',
          items: [
            'ai-architecture/deployment/cloud-providers/index',
            'ai-architecture/deployment/kubernetes/index',
            'ai-architecture/deployment/scaling/index',
          ],
        },
        {
          type: 'category',
          label: 'Security',
          items: [
            'ai-architecture/security/authentication/index',
            'ai-architecture/security/authorization/index',
            'ai-architecture/security/data-protection/index',
          ],
        },
        {
          type: 'category',
          label: 'Implementation Guide',
          items: [
            {
              type: 'category',
              label: 'Model Development',
              items: [
                'ai-architecture/implementation/model-development/index',
                'ai-architecture/implementation/model-development/ml-pipeline/index',
                'ai-architecture/implementation/model-development/experiment-tracking/index',
                'ai-architecture/implementation/model-development/model-testing/index',
                'ai-architecture/implementation/model-development/model-explainability/index',
              ],
            },
            {
              type: 'category',
              label: 'Data Catalog And Lineage Versioning',
              items: [
                {
                  type: 'doc',
                  id: 'ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_catalog',
                  label: 'Data Catalog Implementation',
                },
                {
                  type: 'doc',
                  id: 'ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_lineage',
                  label: 'Data Lineage Implementation',
                },
                {
                  type: 'doc',
                  id: 'ai-architecture/implementation/data-catalog-lineage-versioning/minimalistic_data_versioning',
                  label: 'Data Versioning Implementation',
                },
              ],
            },
            {
              type: 'category',
              label: 'Feature Store',
              items: [
                {
                  type: 'doc',
                  id: 'ai-architecture/implementation/feature-store/minimalistic_feature_monitoring',
                  label: 'Feature Monitoring Implementation',
                },
                {
                  type: 'doc',
                  id: 'ai-architecture/implementation/feature-store/minimalistic_feature_store',
                  label: 'Feature Store Implementation',
                },
              ],
            },
            {
              type: 'doc',
              id: 'ai-architecture/implementation/index',
              label: 'Overview',
            },
          ],
        },
        {
          type: 'category',
          label: 'Tools',
          items: [
            'ai-architecture/tools/tools',
            // Add more tool docs as needed
          ],
        },
      ],
    },
  ],
};

module.exports = sidebars; 