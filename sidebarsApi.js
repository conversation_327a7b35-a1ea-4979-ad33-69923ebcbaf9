/** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
const sidebars = {
  apiSidebar: [
    {
      type: 'category',
      label: 'API Reference',
      items: [
        'api-overview',
        {
          type: 'category',
          label: 'Authentication',
          items: [
            'auth/authentication',
            'auth/authorization',
            'auth/tokens',
          ],
        },
        {
          type: 'category',
          label: 'Models',
          items: [
            'models/list-models',
            'models/model-details',
            'models/predictions',
          ],
        },
        {
          type: 'category',
          label: 'Data',
          items: [
            'data/upload',
            'data/processing',
            'data/export',
          ],
        },
        {
          type: 'category',
          label: 'Monitoring',
          items: [
            'monitoring/metrics',
            'monitoring/alerts',
            'monitoring/logs',
          ],
        },
      ],
    },
  ],
};

module.exports = sidebars; 